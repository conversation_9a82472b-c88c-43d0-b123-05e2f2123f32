from flask import Blueprint, jsonify, request, abort, current_app
from flask_login import current_user
from werkzeug.security import generate_password_hash
import json
import os
from datetime import datetime
from core.models import db, User, Article, Category, Tag, Comment, Media
from utils.utils import generate_slug, save_uploaded_file, allowed_file
from functools import wraps

# 创建API蓝图
api = Blueprint('api', __name__)

# API认证装饰器
def token_required(f):
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        # 检查Authorization头
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                
        if not token:
            return jsonify({'message': 'Token缺失!'}), 401
        
        try:
            # 简单实现，实际应用中应使用JWT等更安全的方式
            user = User.query.filter_by(api_token=token).first()
            if not user:
                return jsonify({'message': '无效Token!'}), 401
        except:
            return jsonify({'message': 'Token无效!'}), 401
            
        return f(user, *args, **kwargs)
    return decorated

# API路由

@api.route('/articles', methods=['GET'])
def get_articles():
    """获取文章列表"""
    page = request.args.get('page', 1, type=int)
    per_page = min(request.args.get('per_page', 10, type=int), 50)
    category_slug = request.args.get('category')
    tag = request.args.get('tag')
    
    query = Article.query.filter_by(published=True)
    
    if category_slug:
        category = Category.query.filter_by(slug=category_slug).first()
        if category:
            query = query.filter_by(category_id=category.id)
    
    if tag:
        tag_obj = Tag.query.filter_by(name=tag).first()
        if tag_obj:
            query = query.filter(Article.tags.contains(tag_obj))
    
    # 排序
    sort_by = request.args.get('sort_by', 'published_at')
    order = request.args.get('order', 'desc')
    
    if sort_by == 'title':
        if order == 'desc':
            query = query.order_by(Article.title.desc())
        else:
            query = query.order_by(Article.title.asc())
    elif sort_by == 'views':
        if order == 'desc':
            query = query.order_by(Article.view_count.desc())
        else:
            query = query.order_by(Article.view_count.asc())
    else:  # 默认按发布时间
        if order == 'desc':
            query = query.order_by(db.desc(Article.published_at))
        else:
            query = query.order_by(db.asc(Article.published_at))
    
    # 分页
    pagination = query.paginate(page=page, per_page=per_page, error_out=False)
    articles = pagination.items
    
    # 构建响应
    result = {
        'items': [],
        'total': pagination.total,
        'pages': pagination.pages,
        'page': page,
        'per_page': per_page
    }
    
    for article in articles:
        result['items'].append({
            'id': article.id,
            'title': article.title,
            'slug': article.slug,
            'summary': article.summary,
            'cover_image': article.cover_image,
            'published_at': article.published_at.isoformat() if article.published_at else None,
            'views': article.view_count,
            'category': article.category.name if article.category else None,
            'author': article.author.username if article.author else None,
            'tags': [tag.name for tag in article.tags]
        })
    
    return jsonify(result)

@api.route('/articles/<string:slug>', methods=['GET'])
def get_article(slug):
    """获取单篇文章"""
    article = Article.query.filter_by(slug=slug, published=True).first()
    
    if not article:
        abort(404)
    
    # 增加浏览量
    article.increment_view()
    db.session.commit()
    
    # 构建响应
    result = {
        'id': article.id,
        'title': article.title,
        'slug': article.slug,
        'content_blocks': article.get_content_blocks(),
        'summary': article.summary,
        'cover_image': article.cover_image,
        'created_at': article.created_at.isoformat(),
        'updated_at': article.updated_at.isoformat(),
        'published_at': article.published_at.isoformat() if article.published_at else None,
        'views': article.view_count,
        'reviewer': article.reviewer,
        'source_url': article.source_url,
        'category': {
            'id': article.category.id,
            'name': article.category.name,
            'slug': article.category.slug
        } if article.category else None,
        'author': {
            'id': article.author.id,
            'username': article.author.username
        } if article.author else None,
        'tags': [{'id': tag.id, 'name': tag.name} for tag in article.tags]
    }
    
    return jsonify(result)

@api.route('/categories', methods=['GET'])
def get_categories():
    """获取分类列表"""
    categories = Category.query.all()
    return jsonify([{
        'id': category.id,
        'name': category.name,
        'slug': category.slug,
        'description': category.description,
        'article_count': category.articles.filter_by(published=True).count()
    } for category in categories])

@api.route('/tags', methods=['GET'])
def get_tags():
    """获取标签列表"""
    tags = Tag.query.all()
    return jsonify([{
        'id': tag.id,
        'name': tag.name,
        'article_count': len(tag.articles)
    } for tag in tags])

@api.route('/articles', methods=['POST'])
@token_required
def create_article(current_user):
    """创建文章"""
    if not request.is_json:
        return jsonify({'message': '请求必须是JSON格式!'}), 400
    
    data = request.get_json()
    
    # 必填字段验证
    if 'title' not in data:
        return jsonify({'message': '标题不能为空!'}), 400
    
    # 处理slug
    if 'slug' in data and data['slug']:
        slug = data['slug']
    else:
        slug = generate_slug(data['title'])
    
    # 检查slug是否已存在
    if Article.query.filter_by(slug=slug).first():
        return jsonify({'message': 'URL别名已存在!'}), 400
    
    # 处理分类
    category = None
    if 'category_id' in data and data['category_id']:
        category = Category.query.get(data['category_id'])
        if not category:
            return jsonify({'message': '分类不存在!'}), 400
    
    # 创建文章
    new_article = Article()
    new_article.title = data['title']
    new_article.slug = slug
    new_article.summary = data.get('summary', '')
    new_article.featured_image = data.get('cover_image', '')
    new_article.meta_title = data.get('meta_title', '')
    new_article.meta_description = data.get('meta_description', '')
    new_article.author_id = current_user.id  # user_id should be author_id based on model
    if category:
        new_article.category_id = category.id
    
    # 处理内容块
    if 'content_blocks' in data:
        # 确保内容块中的key_takeaways能被正确处理
        content_blocks = data['content_blocks']
        # 如果是字符串，尝试解析为JSON对象
        if isinstance(content_blocks, str):
            try:
                content_blocks = json.loads(content_blocks)
                # 检查是否有key_takeaways类型的内容块
                if isinstance(content_blocks, list):
                    for block in content_blocks:
                        if block.get('type') == 'key_takeaways' and 'items' in block:
                            # 确保items是列表格式
                            if not isinstance(block['items'], list):
                                if isinstance(block['items'], str):
                                    # 如果是字符串，尝试按行分割
                                    block['items'] = [line.strip() for line in block['items'].split('\n') if line.strip()]
                                else:
                                    # 其他情况转为空列表
                                    block['items'] = []
                data['content_blocks'] = json.dumps(content_blocks)
            except json.JSONDecodeError:
                pass
        elif isinstance(content_blocks, list):
            # 直接处理列表格式
            for block in content_blocks:
                if block.get('type') == 'key_takeaways' and 'items' in block:
                    # 确保items是列表格式
                    if not isinstance(block['items'], list):
                        if isinstance(block['items'], str):
                            # 如果是字符串，尝试按行分割
                            block['items'] = [line.strip() for line in block['items'].split('\n') if line.strip()]
                        else:
                            # 其他情况转为空列表
                            block['items'] = []
            data['content_blocks'] = json.dumps(content_blocks)
        
        new_article.content_blocks = data['content_blocks']
    
    # 处理标签
    if 'tags' in data and data['tags']:
        for tag_name in data['tags']:
            tag = Tag.query.filter_by(name=tag_name).first()
            if not tag:
                tag = Tag()
                tag.name = tag_name
                db.session.add(tag)
            new_article.tags.append(tag)
    
    # 处理发布状态
    if data.get('published', False):
        new_article.published = True
        new_article.published_at = datetime.utcnow()
    
    # 处理推荐状态
    if data.get('featured', False):
        new_article.featured = True
    
    db.session.add(new_article)
    db.session.commit()
    
    return jsonify({
        'message': '文章创建成功!',
        'article_id': new_article.id,
        'slug': new_article.slug
    }), 201

@api.route('/articles/<int:id>', methods=['PUT'])
@token_required
def update_article(current_user, id):
    """更新文章"""
    article = Article.query.get_or_404(id)
    
    # 检查权限
    if article.user_id != current_user.id and not current_user.is_admin:
        return jsonify({'message': '没有权限修改此文章!'}), 403
    
    if not request.is_json:
        return jsonify({'message': '请求必须是JSON格式!'}), 400
    
    data = request.get_json()
    
    # 更新基本信息
    if 'title' in data:
        article.title = data['title']
    
    if 'slug' in data:
        # 检查新slug是否已存在于其他文章中
        existing = Article.query.filter_by(slug=data['slug']).first()
        if existing and existing.id != article.id:
            return jsonify({'message': 'URL别名已存在!'}), 400
        article.slug = data['slug']
    
    if 'summary' in data:
        article.summary = data['summary']
    
    if 'cover_image' in data:
        article.cover_image = data['cover_image']
    
    if 'reviewer' in data:
        article.reviewer = data['reviewer']
    
    if 'source_url' in data:
        article.source_url = data['source_url']
    
    if 'meta_title' in data:
        article.meta_title = data['meta_title']
    
    if 'meta_description' in data:
        article.meta_description = data['meta_description']
    
    # 更新分类
    if 'category_id' in data:
        if data['category_id']:
            category = Category.query.get(data['category_id'])
            if not category:
                return jsonify({'message': '分类不存在!'}), 400
            article.category_id = category.id
        else:
            article.category_id = None
    
    # 更新内容块
    if 'content_blocks' in data:
        # 确保内容块中的key_takeaways能被正确处理
        content_blocks = data['content_blocks']
        # 如果是字符串，尝试解析为JSON对象
        if isinstance(content_blocks, str):
            try:
                content_blocks = json.loads(content_blocks)
                # 检查是否有key_takeaways类型的内容块
                if isinstance(content_blocks, list):
                    for block in content_blocks:
                        if block.get('type') == 'key_takeaways' and 'items' in block:
                            # 确保items是列表格式
                            if not isinstance(block['items'], list):
                                if isinstance(block['items'], str):
                                    # 如果是字符串，尝试按行分割
                                    block['items'] = [line.strip() for line in block['items'].split('\n') if line.strip()]
                                else:
                                    # 其他情况转为空列表
                                    block['items'] = []
                data['content_blocks'] = json.dumps(content_blocks)
            except json.JSONDecodeError:
                pass
        elif isinstance(content_blocks, list):
            # 直接处理列表格式
            for block in content_blocks:
                if block.get('type') == 'key_takeaways' and 'items' in block:
                    # 确保items是列表格式
                    if not isinstance(block['items'], list):
                        if isinstance(block['items'], str):
                            # 如果是字符串，尝试按行分割
                            block['items'] = [line.strip() for line in block['items'].split('\n') if line.strip()]
                        else:
                            # 其他情况转为空列表
                            block['items'] = []
            data['content_blocks'] = json.dumps(content_blocks)
        
        article.content_blocks = data['content_blocks']
    
    # 更新标签
    if 'tags' in data:
        # 清除现有标签
        article.tags.clear()
        
        # 添加新标签
        for tag_name in data['tags']:
            tag = Tag.query.filter_by(name=tag_name).first()
            if not tag:
                tag = Tag()
                tag.name = tag_name
                db.session.add(tag)
            article.tags.append(tag)
    
    # 更新发布状态
    if 'published' in data:
        if data['published'] and not article.published:
            article.publish()
        elif not data['published'] and article.published:
            article.unpublish()
    
    # 更新推荐状态
    if 'featured' in data:
        article.featured = data['featured']
    
    db.session.commit()
    
    return jsonify({
        'message': '文章更新成功!',
        'article_id': article.id,
        'slug': article.slug
    })

@api.route('/articles/<int:id>', methods=['DELETE'])
@token_required
def delete_article(current_user, id):
    """删除文章"""
    article = Article.query.get_or_404(id)
    
    # 检查权限
    if article.user_id != current_user.id and not current_user.is_admin:
        return jsonify({'message': '没有权限删除此文章!'}), 403
    
    db.session.delete(article)
    db.session.commit()
    
    return jsonify({'message': '文章已删除!'})

@api.route('/upload', methods=['POST'])
@token_required
def upload_file(current_user):
    """上传文件"""
    if 'file' not in request.files:
        return jsonify({'message': '没有文件部分'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'message': '没有选择文件'}), 400
    
    if file and allowed_file(file.filename):
        filename = save_uploaded_file(file)
        
        # 保存媒体记录
        media = Media()
        media.filename = os.path.basename(filename)
        media.filepath = filename
        media.filetype = file.content_type
        media.filesize = os.path.getsize(os.path.join(current_app.config['UPLOAD_FOLDER'], filename))
        media.alt_text = request.form.get('alt_text', '')
        media.user_id = current_user.id
        
        db.session.add(media)
        db.session.commit()
        
        return jsonify({
            'message': '文件上传成功',
            'filename': filename,
            'url': f"/uploads/{filename}",
            'id': media.id
        })
    
    return jsonify({'message': '不允许的文件类型'}), 400

@api.route('/comments/<int:article_id>', methods=['POST'])
@token_required
def add_comment(current_user, article_id):
    """添加评论"""
    article = Article.query.get_or_404(article_id)
    
    if not request.is_json:
        return jsonify({'message': '请求必须是JSON格式!'}), 400
    
    data = request.get_json()
    
    if 'content' not in data or not data['content'].strip():
        return jsonify({'message': '评论内容不能为空!'}), 400
    
    comment = Comment()
    comment.content = data['content']
    comment.article_id = article.id
    comment.author_id = current_user.id  # user_id should be author_id based on model
    # 管理员评论自动审核通过
    comment.approved = current_user.is_admin
    
    db.session.add(comment)
    db.session.commit()
    
    return jsonify({
        'message': '评论已提交，等待审核' if not current_user.is_admin else '评论已发布',
        'comment_id': comment.id
    }), 201

def map_scraped_to_api_format(scraped_data):
    """将抓取的数据结构映射到API所需的格式"""
    
    # 基础数据映射
    api_data = {
        "title": scraped_data.get("title", ""),
        "source_url": scraped_data.get("url", ""),
        "content_blocks": [],
        "metadata": {
            "scraped_at": scraped_data.get("scraped_at", "")
        },
        # 其他可选字段
        "slug": generate_slug(scraped_data.get("title", "")),
        "published": True
    }

    # 传递分类信息
    if "category_id" in scraped_data:
        api_data["category_id"] = scraped_data["category_id"]
        print(f"🔄 传递category_id: {scraped_data['category_id']}")

    if "category" in scraped_data:
        api_data["category"] = scraped_data["category"]
        print(f"🔄 传递category: {scraped_data['category']}")
    
    # 映射内容块
    for block in scraped_data.get("content_blocks", []):
        api_block = {}
        
        # 打印当前处理的块，用于调试
        print("处理内容块:", json.dumps(block, ensure_ascii=False, indent=2))
        
        # 直接处理Key Takeaways类型
        if block["type"] == "Key Takeaways":
            print("发现Key Takeaways类型")
            # 直接转换为key_takeaways类型，保留原始数据中的key_points字段
            if "key_points" in block:
                api_block = {
                    "type": "key_takeaways",
                    "items": block["key_points"]
                }
                print("转换Key Takeaways -> key_takeaways:", json.dumps(api_block, ensure_ascii=False, indent=2))
            else:
                # 如果没有key_points字段，尝试其他可能的字段
                items = block.get("items", []) or block.get("list_items", [])
                api_block = {
                    "type": "key_takeaways",
                    "items": items
                }
        
        # 处理blockquote类型
        elif block["type"] == "blockquote":
            print("发现blockquote类型")
            # 从text字段中提取引用内容和来源
            quote_text = block.get("text", "")
            source = ""
            
            # 尝试从文本中分离引用内容和来源（如果文本中包含"-"或"—"）
            if "-" in quote_text:
                parts = quote_text.split("-", 1)
                quote_text = parts[0].strip()
                source = parts[1].strip() if len(parts) > 1 else ""
            elif "—" in quote_text:
                parts = quote_text.split("—", 1)
                quote_text = parts[0].strip()
                source = parts[1].strip() if len(parts) > 1 else ""
            
            api_block = {
                "type": "quote",
                "content": quote_text,
                "source": source
            }
            print("转换blockquote -> quote:", json.dumps(api_block, ensure_ascii=False, indent=2))
        
        # 处理其他常规类型
        elif block["type"] == "paragraph":
            api_block = {
                "type": "paragraph",
                "content": block.get("text", "") or block.get("content", "")
            }
        
        elif block["type"] == "image":
            api_block = {
                "type": "image",
                "content": block.get("src", ""),
                "caption": block.get("alt", "")
            }
            
        elif block["type"] == "heading":
            api_block = {
                "type": "heading",
                "content": block.get("text", "") or block.get("content", "")
            }
                
        elif block["type"] == "subheading":
            api_block = {
                "type": "subheading",
                "content": block.get("text", "") or block.get("content", "")
            }
                
        elif block["type"] == "list":
            # 处理列表类型
            if "list_items" in block:
                # 如果有list_items字段，优先使用
                api_block = {
                    "type": "list",
                    "style": block.get("style", "unordered"),
                    "items": block["list_items"]
                }
            else:
                api_block = {
                    "type": "list",
                    "style": block.get("style", "unordered"),
                    "items": block.get("items", [])
                }
        
        elif block["type"] == "table":
            # 处理表格类型
            api_block = {
                "type": "table",
                "header": block.get("headers", []),
                "rows": block.get("rows", [])
            }
            print("转换table -> table:", json.dumps(api_block, ensure_ascii=False, indent=2))
        
        elif block["type"] == "key_takeaways":
            # 已经是正确的类型，直接使用
            api_block = block
        
        else:
            # 处理其他类型，保留原始类型和内容
            api_block = {
                "type": block["type"],
                "content": block.get("content", "") or block.get("text", "")
            }
        
        # 添加非空的内容块
        if api_block:
            api_data["content_blocks"].append(api_block)
    
    # 生成摘要
    if not api_data.get("summary"):
        # 尝试从第一个段落生成摘要
        paragraphs = [b for b in api_data["content_blocks"] if b["type"] == "paragraph"]
        if paragraphs:
            api_data["summary"] = paragraphs[0]["content"][:150]
            if len(paragraphs[0]["content"]) > 150:
                api_data["summary"] += "..."
    
    return api_data

@api.route('/articles/import', methods=['POST'])
@token_required
def import_article(current_user):
    """API接口，导入从网站抓取的文章数据"""
    if not current_user.is_admin:
        return jsonify({'status': 'error', 'message': '没有权限执行此操作'}), 403
    
    try:
        # 获取原始抓取数据
        scraped_data = request.get_json()
        
        # 打印原始数据，用于调试
        print("原始抓取数据:", json.dumps(scraped_data, ensure_ascii=False, indent=2))

        # 特别检查category_id字段
        if 'category_id' in scraped_data:
            print(f"🔍 检测到category_id字段: {scraped_data['category_id']}")
        else:
            print("🔍 未检测到category_id字段")
        
        # 基本验证
        if not scraped_data.get("title"):
            return jsonify({'status': 'error', 'message': '文章标题不能为空'}), 400
        
        if not scraped_data.get("content_blocks"):
            return jsonify({'status': 'error', 'message': '文章内容不能为空'}), 400
        
        # 转换为API格式
        article_data = map_scraped_to_api_format(scraped_data)
        
        # 打印转换后的数据，用于调试
        print("转换后的API数据:", json.dumps(article_data, ensure_ascii=False, indent=2))
        
        # 检查是否已存在相同标题的文章
        existing_article = Article.query.filter_by(title=article_data['title']).first()
        
        if existing_article:
            return jsonify({
                'status': 'warning', 
                'message': '已存在相似文章', 
                'article_id': existing_article.id,
                'article_slug': existing_article.slug
            }), 200
        
        # 创建文章
        article = Article()
        article.author_id = current_user.id
        article.created_at = datetime.utcnow()
        
        # 更新文章字段
        article.title = article_data['title']
        # 确保生成有效的slug
        slug = article_data.get('slug') or generate_slug(article_data['title'])
        if not slug or slug == '':
            slug = 'article-' + str(datetime.utcnow().timestamp()).replace('.', '')
        article.slug = slug
        article.summary = article_data.get('summary', '')
        
        # 确保内容块以JSON字符串形式存储，与后台编辑器兼容
        content_blocks = article_data['content_blocks']
        
        # 调试信息
        print("导入的内容块:", json.dumps(content_blocks, ensure_ascii=False, indent=2))
        
        # 检查并处理key_takeaways类型
        if isinstance(content_blocks, list):
            has_key_takeaways = False
            for i, block in enumerate(content_blocks):
                print(f"处理内容块 {i}:", json.dumps(block, ensure_ascii=False, indent=2))
                
                if block.get('type') == 'key_takeaways' and 'items' in block:
                    has_key_takeaways = True
                    print(f"发现要点内容块 {i}:", json.dumps(block, ensure_ascii=False, indent=2))
                    
                    # 确保items是列表格式
                    if not isinstance(block['items'], list):
                        print(f"要点内容块 {i} 的items不是列表，类型是:", type(block['items']).__name__)
                        
                        if isinstance(block['items'], str):
                            # 如果是字符串，尝试按行分割
                            block['items'] = [line.strip() for line in block['items'].split('\n') if line.strip()]
                            print(f"将字符串转换为列表后:", json.dumps(block['items'], ensure_ascii=False, indent=2))
                        else:
                            # 其他情况转为空列表
                            block['items'] = []
                            print(f"非字符串类型，转换为空列表")
                
                # 检查特殊的key_takeaways格式
                if block.get('type') == 'key_takeaways' and not 'items' in block and 'content' in block:
                    print(f"发现特殊格式的要点内容块 {i}, 使用content字段")
                    if isinstance(block['content'], str):
                        block['items'] = [line.strip() for line in block['content'].split('\n') if line.strip()]
                        del block['content']  # 删除content字段，避免冲突
                    has_key_takeaways = True
            
            print("处理后的内容块:", json.dumps(content_blocks, ensure_ascii=False, indent=2))
            print("是否包含要点:", has_key_takeaways)
            
            # 转换为JSON字符串
            article.content_blocks = json.dumps(content_blocks)
        else:
            article.content_blocks = content_blocks
            
        article.published = article_data.get('published', True)
        article.featured = article_data.get('featured', False)
        article.source_url = article_data.get('source_url', '')
        
        if article.published:
            article.published_at = datetime.utcnow()
        
        # 处理分类 - 优先处理category_id，然后处理category名称
        if 'category_id' in article_data and article_data['category_id']:
            # 直接使用category_id
            category_id = article_data['category_id']
            category = Category.query.get(category_id)
            if category:
                article.category_id = category.id
                print(f"✅ 分配分类: {category.name} (ID: {category.id})")
            else:
                print(f"⚠️ 分类ID {category_id} 不存在，跳过分类分配")
        elif 'category' in article_data and article_data['category']:
            # 使用分类名称查找或创建分类
            category_name = article_data['category']
            category = Category.query.filter_by(name=category_name).first()
            if not category:
                # 创建新分类
                category = Category()
                category.name = category_name
                category.slug = generate_slug(category_name)
                db.session.add(category)
                db.session.flush()  # 获取ID但不提交事务
                print(f"✅ 创建新分类: {category.name} (ID: {category.id})")
            article.category_id = category.id
            print(f"✅ 分配分类: {category.name} (ID: {category.id})")
        else:
            print("ℹ️ 未指定分类，文章将显示为无分类")
        
        # 处理标签
        if 'tags' in article_data and article_data['tags']:
            for tag_name in article_data['tags']:
                tag = Tag.query.filter_by(name=tag_name).first()
                if not tag:
                    # 创建新标签
                    tag = Tag()
                    tag.name = tag_name
                    tag.slug = generate_slug(tag_name)
                    db.session.add(tag)
                    db.session.flush()  # 获取ID但不提交事务
                article.tags.append(tag)
        
        # 保存文章
        db.session.add(article)
        db.session.commit()
        
        # 验证保存后的内容块格式
        saved_article = Article.query.get(article.id)
        if saved_article:
            try:
                saved_blocks = saved_article.get_content_blocks()
                print("保存后的内容块:", json.dumps(saved_blocks, ensure_ascii=False, indent=2))
                
                # 检查要点是否正确保存
                for i, block in enumerate(saved_blocks):
                    if block.get('type') == 'key_takeaways':
                        print(f"保存后的要点内容块 {i}:", json.dumps(block, ensure_ascii=False, indent=2))
            except Exception as e:
                print("获取保存后的内容块出错:", str(e))
        
        return jsonify({
            'status': 'success',
            'message': '文章导入成功',
            'article_id': article.id,
            'article_slug': article.slug
        })
    
    except Exception as e:
        db.session.rollback()
        print("导入文章出错:", str(e))
        import traceback
        traceback.print_exc()
        return jsonify({'status': 'error', 'message': f'处理请求时出错: {str(e)}'}), 500 