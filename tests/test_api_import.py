#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import sys
import os

# 测试数据
test_article = {
    "url": "https://www.babycenter.com/baby/baby-development/baby-sleep-basics-birth-to-3-months_7654",
    "scraped_at": "2025-06-24 08:30:00",
    "title": "新生儿睡眠指南：0-3个月",
    "content_blocks": [
        {
            "type": "paragraph",
            "text": "新生儿的睡眠模式对许多新手父母来说是一个挑战。本文将帮助您了解0-3个月婴儿的睡眠特点和如何建立良好的睡眠习惯。"
        },
        {
            "type": "image",
            "src": "https://assets.babycenter.com/ims/2015/01/Bby-and-Mom-Generic-18_wide.jpg",
            "alt": "妈妈抱着宝宝，两人都在笑"
        },
        {
            "type": "heading",
            "level": 2,
            "text": "新生儿睡眠的基本特点"
        },
        {
            "type": "list",
            "style": "unordered",
            "items": [
                "新生儿每天睡眠时间约为14-17小时",
                "睡眠周期短，通常为2-4小时",
                "睡眠模式尚未形成，日夜节律不明显",
                "可能会在睡眠中发出声音或抽动",
                "REM睡眠占比较高，有助于大脑发育"
            ]
        },
        {
            "type": "subheading",
            "level": 3,
            "text": "如何帮助新生儿建立良好的睡眠习惯"
        },
        {
            "type": "paragraph",
            "text": "创建安静、舒适的睡眠环境，保持一致的睡前流程，学会识别宝宝的困倦信号，这些都有助于帮助新生儿逐渐建立规律的睡眠模式。"
        }
    ]
}

def test_import_article(api_url, api_token, article_data):
    """测试文章导入API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_token}"
    }
    
    response = requests.post(api_url, json=article_data, headers=headers)
    
    print(f"状态码: {response.status_code}")
    print(f"响应内容: {response.text}")
    
    return response.json() if response.status_code == 200 or response.status_code == 201 else None

def get_api_token():
    """获取API token"""
    # 检查是否有已存在的管理员用户
    import sqlite3
    try:
        conn = sqlite3.connect('instance/articles.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, username FROM user WHERE is_admin = 1 LIMIT 1")
        admin = cursor.fetchone()
        
        if admin:
            admin_id, admin_username = admin
            print(f"找到管理员用户: {admin_username} (ID: {admin_id})")
            
            # 检查是否已有API token
            cursor.execute("SELECT api_token FROM user WHERE id = ?", (admin_id,))
            token = cursor.fetchone()
            
            if token and token[0]:
                return token[0]
            
            # 生成新token
            import uuid
            new_token = str(uuid.uuid4())
            cursor.execute("UPDATE user SET api_token = ? WHERE id = ?", (new_token, admin_id))
            conn.commit()
            print(f"已为用户 {admin_username} 生成新的API token")
            return new_token
    except Exception as e:
        print(f"获取API token时出错: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()
    
    return None

if __name__ == "__main__":
    # 从环境变量或命令行参数获取API URL和token
    api_url = sys.argv[1] if len(sys.argv) > 1 else "http://127.0.0.1:5000/api/articles/import"
    api_token = sys.argv[2] if len(sys.argv) > 2 else os.environ.get("API_TOKEN") or get_api_token() or "your_api_token_here"
    
    print(f"测试API: {api_url}")
    result = test_import_article(api_url, api_token, test_article)
    
    if result and result.get("status") == "success":
        print(f"测试成功! 文章ID: {result.get('article_id')}, 别名: {result.get('article_slug')}")
    else:
        print("测试失败!") 