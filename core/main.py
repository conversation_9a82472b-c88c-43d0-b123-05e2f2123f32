from flask import Blueprint, render_template, request, abort, redirect, url_for, flash, current_app, jsonify, g
from flask_login import current_user, login_required
from datetime import datetime, timedelta
import json
from sqlalchemy import func, desc

from .models import db, Article, Category, Tag, Comment, User, PrimaryCategory, SecondaryCategory
from .forms import CommentForm
from utils.utils import render_content_blocks, extract_toc, generate_toc_html

main = Blueprint('main', __name__)

@main.route('/')
def index():
    """首页"""
    # 获取最新文章
    latest_articles = Article.query.filter_by(published=True).order_by(
        Article.published_at.desc()
    ).limit(10).all()

    # 获取热门文章
    popular_articles = Article.query.filter_by(published=True).order_by(
        Article.view_count.desc()
    ).limit(5).all()

    # 获取推荐文章
    featured_articles = Article.query.filter_by(
        published=True,
        featured=True
    ).order_by(Article.published_at.desc()).limit(3).all()

    # 为文章处理图片URL
    for article in latest_articles + popular_articles + featured_articles:
        if not article.featured_image:
            # 尝试从content_blocks中获取第一个图片
            try:
                content_blocks = article.get_content_blocks()
                for block in content_blocks:
                    if block.get('type') == 'image' and block.get('content'):
                        article.featured_image = block.get('content')
                        break
            except:
                # 如果解析失败，使用默认图片
                article.featured_image = url_for('static', filename='img/placeholder.jpg')

    # 获取专业医疗团队数据（示例数据）
    experts = [
        {
            'name': '李医生',
            'title': '妇产科主任医师',
            'description': '20年妇产科临床经验，专注于高危妊娠管理',
            'avatar': url_for('static', filename='img/expert1.jpg'),
            'articles': {'count': lambda: 15},
            'rating': 4.9
        },
        {
            'name': '王医生',
            'title': '儿科专家',
            'description': '儿童生长发育专家，新生儿护理权威',
            'avatar': url_for('static', filename='img/expert2.jpg'),
            'articles': {'count': lambda: 12},
            'rating': 4.8
        },
        {
            'name': '张医生',
            'title': '营养师',
            'description': '孕期营养指导专家，母婴健康顾问',
            'avatar': url_for('static', filename='img/expert3.jpg'),
            'articles': {'count': lambda: 18},
            'rating': 4.9
        }
    ]

    # 定义可用的计算器工具
    calculator_tools = [
        {
            'name': 'growth',
            'title': '生长发育计算器',
            'description': '基于WHO标准评估宝宝身高体重发育情况',
            'icon': 'fas fa-chart-line',
            'url': '/tools/growth-calculator',
            'calculator_class': 'GrowthCalculator'
        },
        {
            'name': 'due-date',
            'title': '预产期计算器',
            'description': '根据末次月经时间准确计算预产期',
            'icon': 'fas fa-calendar-alt',
            'url': '/tools/due-date-calculator',
            'calculator_class': 'DueDateCalculator'
        },
        {
            'name': 'conception',
            'title': '受孕日期计算器',
            'description': '计算最佳受孕时间和排卵期',
            'icon': 'fas fa-heart',
            'url': '/tools/conception-calculator',
            'calculator_class': 'ConceptionCalculator'
        },
        {
            'name': 'pregnancy-weight',
            'title': '孕期体重计算器',
            'description': '监测孕期体重增长是否正常',
            'icon': 'fas fa-weight',
            'url': '/tools/pregnancy-weight-calculator',
            'calculator_class': 'PregnancyWeightCalculator'
        },
        {
            'name': 'baby-cost',
            'title': '婴儿花费计算器',
            'description': '估算育儿成本，做好财务规划',
            'icon': 'fas fa-dollar-sign',
            'url': '/tools/baby-cost-calculator',
            'calculator_class': 'BabyCostCalculator'
        },
        {
            'name': 'height-predictor',
            'title': '身高预测器',
            'description': '预测宝宝未来身高发育趋势',
            'icon': 'fas fa-ruler-vertical',
            'url': '/tools/height-predictor',
            'calculator_class': 'HeightPredictor'
        }
    ]

    return render_template('public/index.html',
                         latest_articles=latest_articles,
                         popular_articles=popular_articles,
                         featured_articles=featured_articles,
                         experts=experts,
                         calculator_tools=calculator_tools)

@main.route('/articles')
def articles():
    """文章列表页"""
    page = request.args.get('page', 1, type=int)
    category_slug = request.args.get('category')
    tag_name = request.args.get('tag')
    
    # 基础查询
    query = Article.query.filter_by(published=True)
    
    # 应用分类过滤
    if category_slug:
        category = Category.query.filter_by(slug=category_slug).first_or_404()
        query = query.filter_by(category_id=category.id)
    
    # 应用标签过滤
    if tag_name:
        tag = Tag.query.filter_by(name=tag_name).first_or_404()
        query = query.filter(Article.tags.contains(tag))
    
    # 分页
    pagination = query.order_by(Article.published_at.desc()).paginate(
        page=page, per_page=12, error_out=False
    )
    
    # 获取所有分类
    categories = Category.query.all()
    
    # 获取最新文章
    recent_articles = Article.query.filter_by(published=True).order_by(
        Article.published_at.desc()
    ).limit(5).all()
    
    return render_template('public/articles.html',
                          articles=pagination.items,
                          pagination=pagination,
                          categories=categories,
                          recent_articles=recent_articles,
                          title='文章列表')

@main.route('/article/<slug>')
def article_detail(slug):
    """文章详情页"""
    article = Article.query.filter_by(slug=slug).first_or_404()
    
    # 增加浏览量
    article.increment_view_count()
    
    # 获取相关文章
    related_articles = Article.query.filter(
        Article.category_id == article.category_id,
        Article.id != article.id,
        Article.published == True
    ).order_by(Article.published_at.desc()).limit(3).all()
    
    # 渲染内容和提取目录
    content_html = ""
    toc = ""
    toc_html = ""
    if hasattr(article, 'content_blocks') and article.content_blocks:
        # 渲染内容
        content_html = render_content_blocks(article.content_blocks)
        # 应用自动内链，传入当前文章slug以避免自我链接
        from utils.autolink import apply_autolinks
        content_html = apply_autolinks(content_html, article.slug)
        # 提取目录
        toc_items = extract_toc(content_html)
        # 生成HTML格式的目录
        toc_html = generate_toc_html(toc_items)
    
    # 获取评论
    comments = []
    if hasattr(article, 'comments'):
        comments = article.comments.filter_by(
            approved=True,
            parent_id=None
        ).order_by(Comment.created_at.desc()).all()
    
    return render_template('public/article.html',
                         article=article,
                         toc=toc_items,
                         toc_html=toc_html,
                         content_html=content_html,
                         comments=comments,
                         related_articles=related_articles)

@main.route('/comment/<int:article_id>', methods=['POST'])
@login_required
def comment(article_id):
    """添加评论"""
    article = Article.query.filter_by(id=article_id, published=True).first_or_404()
    
    if not current_app.config.get('ALLOW_COMMENTS', True):
        return jsonify({'status': 'error', 'message': '评论功能已关闭'}), 403
    
    content = request.form.get('content')
    if not content:
        return jsonify({'status': 'error', 'message': '评论内容不能为空'}), 400
    
    # 创建评论
    comment = Comment()
    comment.content = content
    comment.article_id = article_id
    comment.author_id = current_user.id
    comment.approved = current_user.is_admin or not current_app.config.get('REQUIRE_COMMENT_APPROVAL', True)
    
    db.session.add(comment)
    db.session.commit()
    
    # Get user for response
    user = User.query.get(current_user.id)
    username = user.username if user else 'Unknown'
    avatar = user.get_avatar_url() if user else '/static/img/default-avatar.png'
    
    return jsonify({
        'status': 'success',
        'message': '评论已发布' if comment.approved else '评论已提交，等待审核',
        'comment': {
            'content': comment.content,
            'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M'),
            'author': {
                'username': username,
                'avatar': avatar
            },
            'approved': comment.approved
        }
    })

@main.route('/comment/<int:comment_id>', methods=['DELETE'])
@login_required
def delete_comment(comment_id):
    """删除评论"""
    comment = Comment.query.get_or_404(comment_id)
    
    # 检查权限
    if comment.user_id != current_user.id and not current_user.is_admin:
        return jsonify({'status': 'error', 'message': '没有权限删除此评论'}), 403
    
    db.session.delete(comment)
    db.session.commit()
    
    return jsonify({'status': 'success', 'message': '评论已删除'})

@main.route('/category/<slug>')
def category_detail(slug):
    """分类详情页"""
    category = Category.query.filter_by(slug=slug).first_or_404()
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 12
    
    # 获取文章列表
    articles = Article.query.filter_by(
        category_id=category.id,
        published=True
    ).order_by(Article.published_at.desc()).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    
    return render_template('public/category.html',
                         category=category,
                         articles=articles)

@main.route('/tag/<slug>')
def tag_detail(slug):
    """标签详情页"""
    tag = Tag.query.filter_by(slug=slug).first_or_404()
    
    # 获取分页参数
    page = request.args.get('page', 1, type=int)
    per_page = 12
    
    # 获取文章列表
    articles = tag.articles.filter_by(published=True).order_by(
        Article.published_at.desc()
    ).paginate(page=page, per_page=per_page, error_out=False)
    
    return render_template('public/tag.html',
                         tag=tag,
                         articles=articles)

@main.route('/search')
def search():
    """搜索页"""
    # 获取搜索参数
    q = request.args.get('q', '').strip()
    page = request.args.get('page', 1, type=int)
    per_page = 12
    
    if not q:
        return redirect(url_for('main.index'))
    
    # 搜索文章
    articles = Article.query.filter(
        Article.published == True,
        Article.title.ilike(f'%{q}%')
    ).order_by(Article.published_at.desc()).paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )
    
    return render_template('public/search.html',
                         q=q,
                         articles=articles)

@main.route('/tools')
def tools():
    """工具页"""
    return render_template('public/tools.html')

@main.route('/api/calculate-due-date', methods=['POST'])
def calculate_due_date():
    """计算预产期"""
    data = request.get_json()
    
    # 获取最后一次月经日期
    last_period = datetime.strptime(data['lastPeriod'], '%Y-%m-%d')
    
    # 计算预产期（最后一次月经日期 + 280天）
    due_date = last_period + timedelta(days=280)
    
    # 计算当前孕周
    today = datetime.now()
    days_pregnant = (today - last_period).days
    weeks = days_pregnant // 7
    days = days_pregnant % 7
    
    return jsonify({
        'dueDate': due_date.strftime('%Y-%m-%d'),
        'weeksPregnant': weeks,
        'daysPregnant': days
    })

@main.route('/api/calculate-ovulation', methods=['POST'])
def calculate_ovulation():
    """计算排卵日期"""
    data = request.get_json()
    
    # 获取最后一次月经日期和月经周期
    last_period = datetime.strptime(data['lastPeriod'], '%Y-%m-%d')
    cycle_length = int(data['cycleLength'])
    
    # 计算下一次月经日期
    next_period = last_period + timedelta(days=cycle_length)
    
    # 计算排卵日期（下一次月经前14天）
    ovulation_date = next_period - timedelta(days=14)
    
    # 计算排卵窗口（排卵日前后5天）
    fertile_start = ovulation_date - timedelta(days=5)
    fertile_end = ovulation_date + timedelta(days=1)
    
    return jsonify({
        'ovulationDate': ovulation_date.strftime('%Y-%m-%d'),
        'fertileStart': fertile_start.strftime('%Y-%m-%d'),
        'fertileEnd': fertile_end.strftime('%Y-%m-%d')
    })

@main.route('/api/calculate-bmi', methods=['POST'])
def calculate_bmi():
    """计算BMI"""
    data = request.get_json()
    
    # 获取身高和体重
    height = float(data['height']) / 100  # 转换为米
    weight = float(data['weight'])
    
    # 计算BMI
    bmi = weight / (height * height)
    
    # 判断BMI范围
    if bmi < 18.5:
        category = '偏瘦'
        advice = '建议适当增加营养摄入，保持均衡饮食'
    elif bmi < 24:
        category = '正常'
        advice = '继续保持健康的生活方式'
    elif bmi < 28:
        category = '偏胖'
        advice = '建议控制饮食，增加运动量'
    else:
        category = '肥胖'
        advice = '建议在医生指导下进行减重'
    
    return jsonify({
        'bmi': round(bmi, 1),
        'category': category,
        'advice': advice
    })

@main.route('/comment/add', methods=['POST'])
@login_required
def add_comment():
    """添加评论"""
    article_id = request.form.get('article_id')
    content = request.form.get('content')
    parent_id = request.form.get('parent_id')
    
    if not content:
        flash('评论内容不能为空', 'danger')
        return redirect(request.referrer)
    
    article = Article.query.get_or_404(article_id)
    
    comment = Comment()
    comment.content = content
    comment.article_id = article_id
    comment.author_id = current_user.id
    comment.parent_id = parent_id if parent_id else None
    comment.approved = current_user.is_admin
    
    db.session.add(comment)
    db.session.commit()
    
    # 更新评论数
    if hasattr(article, 'update_comment_count'):
        article.update_comment_count()
    
    flash('评论已提交，等待审核', 'success')
    return redirect(request.referrer)

@main.route('/article/like/<int:id>', methods=['POST'])
@login_required
def like_article(id):
    """点赞文章"""
    article = Article.query.get_or_404(id)
    
    if article in current_user.liked_articles:
        current_user.liked_articles.remove(article)
        message = '取消点赞'
    else:
        current_user.liked_articles.append(article)
        message = '点赞成功'
    
    db.session.commit()
    article.update_like_count()
    
    return jsonify({
        'message': message,
        'likes': article.like_count
    })

@main.route('/about')
def about():
    """关于页面"""
    return render_template('public/about.html', title='关于我们')

@main.route('/contact', methods=['GET', 'POST'])
def contact():
    """联系页面"""
    if request.method == 'POST':
        name = request.form.get('name')
        email = request.form.get('email')
        subject = request.form.get('subject')
        message = request.form.get('message')
        
        # 这里可以添加发送电子邮件的逻辑
        # 作为示例，我们只显示一个成功消息
        
        flash('您的消息已发送，我们会尽快回复您', 'success')
        return redirect(url_for('main.contact'))
    
    return render_template('public/contact.html', title='联系我们')

# 创建一个辅助函数，在所有请求中获取导航数据
@main.before_request
def get_navigation_data():
    """
    在请求处理前获取导航数据并存储在g对象中，供所有模板使用
    """
    g.primary_categories = PrimaryCategory.query.filter_by(is_visible=True).order_by(PrimaryCategory.order.asc()).all()

# 更新上下文处理器，使导航数据在所有模板中可用
@main.context_processor
def inject_common_data():
    """向所有模板注入通用数据"""
    # 获取通用分类数据用于侧边栏等
    categories = Category.query.filter_by(is_visible=True, parent_id=None).order_by(Category.order.asc()).all()
    
    # 获取热门标签
    popular_tags = Tag.query.limit(10).all()
    
    # 添加导航数据
    primary_categories = g.get('primary_categories', [])
    
    return {
        'categories': categories,
        'popular_tags': popular_tags,
        'primary_categories': primary_categories
    }

# 添加二级分类页面路由
@main.route('/category/<string:primary_slug>/<string:secondary_slug>')
def secondary_category(primary_slug, secondary_slug):
    """显示二级分类页面，列出该分类下所有三级分类及其文章"""
    primary = PrimaryCategory.query.filter_by(slug=primary_slug).first_or_404()
    secondary = SecondaryCategory.query.filter_by(slug=secondary_slug, primary_category_id=primary.id).first_or_404()
    
    # 获取该二级分类下的所有三级分类
    categories = Category.query.filter_by(secondary_category_id=secondary.id, is_visible=True).order_by(Category.order.asc()).all()
    
    # 获取所有三级分类下的文章，按分类分组
    articles_by_category = {}
    for category in categories:
        category_articles = Article.query.filter(
            Article.category_id == category.id, 
            Article.published == True
        ).order_by(desc(Article.created_at)).all()
        articles_by_category[category.id] = category_articles
    
    # 获取最新文章
    latest_articles = Article.query.join(Category).filter(
        Category.secondary_category_id == secondary.id,
        Article.published == True
    ).order_by(desc(Article.created_at)).limit(8).all()

    # 为所有文章处理图片URL（与首页逻辑一致）
    all_articles = []
    for category_articles in articles_by_category.values():
        all_articles.extend(category_articles)
    all_articles.extend(latest_articles)

    for article in all_articles:
        if not article.featured_image:
            # 尝试从content_blocks中获取第一个图片
            try:
                content_blocks = article.get_content_blocks()
                for block in content_blocks:
                    if block.get('type') == 'image' and block.get('content'):
                        article.featured_image = block.get('content')
                        break
            except:
                # 如果解析失败，使用默认图片
                article.featured_image = url_for('static', filename='img/placeholder.jpg')

    # 获取所有一级分类用于顶部导航
    primary_categories = PrimaryCategory.query.filter_by(is_visible=True).order_by(PrimaryCategory.order.asc()).all()
    
    return render_template(
        'main/secondary_category.html',
        primary_category=primary,
        secondary_category=secondary,
        categories=categories,
        articles_by_category=articles_by_category,
        latest_articles=latest_articles,
        primary_categories=primary_categories
    ) 