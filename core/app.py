import os
from flask import Flask, render_template, redirect, url_for, flash
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, current_user
from flask_migrate import Migrate
import secrets
from datetime import datetime

from core.models import db, User, Article, Category, Tag, Comment
from api.api import api
from auth.auth import auth
from core.main import main
from core.admin import admin as admin_bp
from utils.utils import date_filter, datetime_filter
from utils.autolink import init_autolink

def create_app(config=None):
    app = Flask(__name__)
    
    # 配置
    app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY') or secrets.token_hex(16)
    app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL') or 'sqlite:///articles.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['UPLOAD_FOLDER'] = os.path.join(app.root_path, 'static', 'uploads')
    app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'doc', 'docx'}
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB
    
    # 网站设置
    app.config['SITE_NAME'] = '孕育知识库'
    app.config['SITE_DESCRIPTION'] = '专业的孕育知识分享平台'
    app.config['ALLOW_COMMENTS'] = True
    app.config['REQUIRE_COMMENT_APPROVAL'] = True
    
    # 应用自定义配置
    if config:
        app.config.update(config)
    
    # 初始化扩展
    db.init_app(app)
    migrate = Migrate(app, db)
    
    # 注册过滤器
    app.jinja_env.filters['date'] = date_filter
    app.jinja_env.filters['datetime'] = datetime_filter
    
    # 初始化自动内链功能
    init_autolink(app)
    
    # 确保上传目录存在
    if not os.path.exists(app.config['UPLOAD_FOLDER']):
        os.makedirs(app.config['UPLOAD_FOLDER'])
    
    # 注册蓝图
    app.register_blueprint(api, url_prefix='/api')
    app.register_blueprint(auth)
    app.register_blueprint(main)
    app.register_blueprint(admin_bp, url_prefix='/admin')
    
    # 设置登录管理器
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'  # type: ignore
    login_manager.login_message = '请先登录'
    login_manager.login_message_category = 'info'
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # 错误处理
    @app.errorhandler(404)
    def page_not_found(e):
        return render_template('public/404.html'), 404
    
    @app.errorhandler(500)
    def server_error(e):
        return render_template('public/500.html'), 500
    
    # 全局上下文处理器
    @app.context_processor
    def inject_globals():
        return {
            'site_name': app.config.get('SITE_NAME', '孕育知识库'),
            'site_description': app.config.get('SITE_DESCRIPTION', '专业的孕育知识分享平台'),
            'current_year': datetime.now().year,
            'categories': Category.query.order_by(Category.order).all(),
            'recent_articles': Article.query.filter_by(published=True).order_by(Article.id.desc()).limit(5).all(),
            'featured_articles': Article.query.filter_by(published=True, featured=True).order_by(Article.id.desc()).limit(3).all()
        }
    
    # 创建数据库
    with app.app_context():
        db.create_all()
        
        # 如果没有管理员，创建一个默认管理员
        if not User.query.filter_by(is_admin=True).first():
            admin_user = User()
            admin_user.username = 'admin'
            admin_user.email = '<EMAIL>'
            admin_user.is_admin = True
            admin_user.set_password('admin123')
            db.session.add(admin_user)
            db.session.commit()
    
    return app

if __name__ == '__main__':
    app = create_app()
    app.run(port=5001) 