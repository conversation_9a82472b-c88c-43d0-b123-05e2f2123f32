from datetime import datetime
import json
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from bs4 import BeautifulSoup

db = SQLAlchemy()

# 文章标签关联表
article_tags = db.<PERSON>('article_tags',
    db.<PERSON>umn('article_id', db.Integer, db.<PERSON>('article.id'), primary_key=True),
    db.<PERSON>('tag_id', db.In<PERSON>ger, db.<PERSON>('tag.id'), primary_key=True)
)

# 文章点赞关联表
article_likes = db.Table('article_likes',
    db.<PERSON>umn('article_id', db.Integer, db.<PERSON>('article.id'), primary_key=True),
    db.<PERSON>umn('user_id', db.Integer, db.<PERSON>('user.id'), primary_key=True)
)

# 文章收藏关联表
article_bookmarks = db.Table('article_bookmarks',
    db.<PERSON>('article_id', db.Integer, db.<PERSON>('article.id'), primary_key=True),
    db.<PERSON>('user_id', db.In<PERSON>ger, db.<PERSON>('user.id'), primary_key=True)
)

# 用户表
class User(UserMixin, db.Model):
    __tablename__ = 'user'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(128))
    is_admin = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    api_token = db.Column(db.String(100), unique=True)
    
    # 用户资料
    display_name = db.Column(db.String(80))
    avatar = db.Column(db.String(200))
    bio = db.Column(db.Text)
    title = db.Column(db.String(50))  # 如：准妈妈、过来人等
    location = db.Column(db.String(100))
    website = db.Column(db.String(200))
    
    # 社交媒体链接
    social_links = db.Column(db.JSON)
    
    # 用户偏好设置
    preferences = db.Column(db.JSON)
    
    # 关系
    articles = db.relationship('Article', backref='author', lazy='dynamic',
                             foreign_keys='Article.author_id')
    comments = db.relationship('Comment', backref='author', lazy='dynamic',
                             foreign_keys='Comment.author_id')
    reviewed_articles = db.relationship('Article', back_populates='reviewer', lazy='dynamic',
                                      foreign_keys='Article.reviewer_id')
    liked_articles = db.relationship('Article', secondary=article_likes,
                                   backref=db.backref('liked_by', lazy='dynamic'))
    bookmarked_articles = db.relationship('Article', secondary=article_bookmarks,
                                        backref=db.backref('bookmarked_by', lazy='dynamic'))
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
        
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def get_display_name(self):
        return self.display_name or self.username
    
    def get_avatar_url(self):
        if self.avatar:
            return self.avatar
        # 返回默认头像
        return '/static/img/default-avatar.png'
    
    def to_dict(self):
        return {
            'id': self.id,
            'username': self.username,
            'display_name': self.display_name,
            'avatar': self.get_avatar_url(),
            'title': self.title,
            'bio': self.bio
        }
    
    def has_liked(self, article):
        """检查用户是否点赞了文章"""
        return article in self.liked_articles
    
    def has_bookmarked(self, article):
        """检查用户是否收藏了文章"""
        return article in self.bookmarked_articles
    
    def __repr__(self):
        return f'<User {self.username}>'

# 分类表
class PrimaryCategory(db.Model):
    """一级分类表，参考BabyCenter导航结构"""
    __tablename__ = 'primary_category'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    slug = db.Column(db.String(64), unique=True, nullable=False)
    description = db.Column(db.Text)
    icon = db.Column(db.String(128))
    image = db.Column(db.String(255))
    order = db.Column(db.Integer, default=0)
    is_visible = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    secondary_categories = db.relationship('SecondaryCategory', back_populates='primary_category', lazy='dynamic')
    
    def __repr__(self):
        return f'<PrimaryCategory {self.name}>'

class SecondaryCategory(db.Model):
    """二级分类表，参考BabyCenter导航结构"""
    __tablename__ = 'secondary_category'
    
    id = db.Column(db.Integer, primary_key=True)
    primary_category_id = db.Column(db.Integer, db.ForeignKey('primary_category.id'), index=True, nullable=False)
    name = db.Column(db.String(64), nullable=False)
    slug = db.Column(db.String(64), index=True, nullable=False)
    description = db.Column(db.Text)
    icon = db.Column(db.String(128))
    image = db.Column(db.String(255))
    order = db.Column(db.Integer, default=0)
    is_visible = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    primary_category = db.relationship('PrimaryCategory', back_populates='secondary_categories')
    categories = db.relationship('Category', back_populates='secondary_category', lazy='dynamic')
    
    def __repr__(self):
        return f'<SecondaryCategory {self.name}>'

class Category(db.Model):
    __tablename__ = 'category'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    order = db.Column(db.Integer, default=0)
    icon = db.Column(db.String(50))
    is_visible = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    articles = db.relationship('Article', backref='category', lazy='dynamic')
    subcategories = db.relationship('Category', 
                                  backref=db.backref('parent', remote_side=[id]),
                                  foreign_keys=[parent_id])
    
    # 添加二级分类关联
    secondary_category_id = db.Column(db.Integer, db.ForeignKey('secondary_category.id'), index=True)
    secondary_category = db.relationship('SecondaryCategory', back_populates='categories')
    
    def __str__(self):
        return self.name
    
    def get_absolute_url(self):
        return f'/category/{self.slug}'
    
    def get_breadcrumbs(self):
        breadcrumbs = []
        category = self
        while category:
            breadcrumbs.append(category)
            category = category.parent  # type: ignore
        return list(reversed(breadcrumbs))

# 标签表
class Tag(db.Model):
    __tablename__ = 'tag'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    slug = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    article_count = db.Column(db.Integer, default=0)
    
    def __str__(self):
        return self.name
    
    def get_absolute_url(self):
        return f'/tag/{self.slug}'

# 文章表
class Article(db.Model):
    __tablename__ = 'article'
    
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    slug = db.Column(db.String(200), unique=True, nullable=False)
    content = db.Column(db.Text)
    content_blocks = db.Column(db.JSON)  # 结构化内容块
    summary = db.Column(db.Text)
    featured_image = db.Column(db.String(200))
    
    # 元数据
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'))
    tags = db.relationship('Tag', secondary=article_tags, backref=db.backref('articles', lazy='dynamic'))
    reviewer_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    source_url = db.Column(db.String(500))  # 文章来源URL
    
    # 状态
    published = db.Column(db.Boolean, default=False)
    featured = db.Column(db.Boolean, default=False)
    allow_comments = db.Column(db.Boolean, default=True)
    
    # 时间戳
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = db.Column(db.DateTime)
    
    # SEO
    meta_title = db.Column(db.String(200))
    meta_description = db.Column(db.Text)
    meta_keywords = db.Column(db.String(200))
    
    # 统计
    view_count = db.Column(db.Integer, default=0)
    like_count = db.Column(db.Integer, default=0)
    comment_count = db.Column(db.Integer, default=0)
    
    # 医学审核
    reviewed = db.Column(db.Boolean, default=False)
    review_notes = db.Column(db.Text)
    reviewed_at = db.Column(db.DateTime)
    
    # 关系
    comments = db.relationship('Comment', backref='article', lazy='dynamic')
    reviewer = db.relationship('User', foreign_keys=[reviewer_id], back_populates='reviewed_articles')
    
    def __str__(self):
        return self.title
    
    def get_absolute_url(self):
        return f'/article/{self.slug}'
    
    def increment_view_count(self):
        self.view_count += 1
        db.session.commit()
    
    def update_comment_count(self):
        self.comment_count = self.comments.filter_by(approved=True).count()
        db.session.commit()
    
    def update_like_count(self):
        self.like_count = self.liked_by.count()  # type: ignore
        db.session.commit()
    
    def set_content_blocks(self, blocks):
        """设置文章的内容块结构"""
        if isinstance(blocks, list) or isinstance(blocks, dict):
            self.content_blocks = json.dumps(blocks)
        else:
            self.content_blocks = blocks
        
    def publish(self):
        """发布文章"""
        self.published = True
        self.published_at = datetime.utcnow()
        
    def unpublish(self):
        """取消发布文章"""
        self.published = False
    
    def get_content_blocks(self):
        """获取文章的内容块结构"""
        if not self.content_blocks:
            return []
            
        # 如果是JSON字符串，转换为Python对象
        if isinstance(self.content_blocks, str):
            try:
                blocks = json.loads(self.content_blocks)
            except json.JSONDecodeError:
                return []
        else:
            blocks = self.content_blocks
            
        return blocks
    
    def increment_view(self):
        """增加文章浏览量"""
        self.view_count += 1
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'slug': self.slug,
            'summary': self.summary,
            'featured_image': self.featured_image,
            'author': self.author.to_dict(),  # type: ignore
            'category': self.category.name if self.category else None,  # type: ignore
            'tags': [tag.name for tag in self.tags],  # type: ignore
            'published': self.published,
            'published_at': self.published_at.isoformat() if self.published_at else None,
            'view_count': self.view_count,
            'like_count': self.like_count,
            'comment_count': self.comment_count
        }

# 评论表
class Comment(db.Model):
    __tablename__ = 'comment'
    
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    author_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    article_id = db.Column(db.Integer, db.ForeignKey('article.id'), nullable=False)
    parent_id = db.Column(db.Integer, db.ForeignKey('comment.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    approved = db.Column(db.Boolean, default=False)
    
    # 关系
    replies = db.relationship('Comment', backref=db.backref('parent', remote_side=[id]))
    
    def __str__(self):
        return f'Comment by {self.author.username} on {self.article.title}'  # type: ignore
    
    def to_dict(self):
        return {
            'id': self.id,
            'content': self.content,
            'author': self.author.to_dict(),  # type: ignore
            'created_at': self.created_at.isoformat(),
            'parent_id': self.parent_id,
            'approved': self.approved
        }

# 媒体文件表
class Media(db.Model):
    __tablename__ = 'media'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    filepath = db.Column(db.String(255), nullable=False)
    filetype = db.Column(db.String(64), nullable=False)
    filesize = db.Column(db.Integer, nullable=False)
    alt_text = db.Column(db.String(255))
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    # 关联
    user = db.relationship('User', backref=db.backref('uploads', lazy=True))
    
    def __repr__(self):
        return f'<Media {self.filename}>'

class ToolUsage(db.Model):
    __tablename__ = 'tool_usage'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    tool_name = db.Column(db.String(50), nullable=False)
    input_data = db.Column(db.JSON)
    result = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    user = db.relationship('User', backref=db.backref('tool_usage', lazy='dynamic'))
    
    def __str__(self):
        return f'{self.tool_name} used by {self.user.username}' 