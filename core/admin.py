from flask import Blueprint, render_template, request, redirect, url_for, flash, current_app, jsonify
from flask_login import login_required, current_user
from werkzeug.security import generate_password_hash
import os
import json
from datetime import datetime
from functools import wraps
from .models import db, User, Article, Category, Tag, Comment, Media, PrimaryCategory, SecondaryCategory
from .forms import ArticleForm, CategoryForm, TagForm, CommentForm, MediaUploadForm
from utils.utils import save_uploaded_file, allowed_file, generate_slug

# 创建管理蓝图
admin = Blueprint('admin', __name__)

# 权限检查装饰器
def admin_required(func):
    @wraps(func)
    @login_required
    def decorated_view(*args, **kwargs):
        if not current_user.is_admin:
            flash('您没有访问此页面的权限', 'danger')
            return redirect(url_for('main.index'))
        return func(*args, **kwargs)
    return decorated_view

@admin.route('/')
@admin_required
def index():
    """管理后台首页"""
    stats = {
        'article_count': Article.query.count(),
        'published_count': Article.query.filter_by(published=True).count(),
        'draft_count': Article.query.filter_by(published=False).count(),
        'category_count': Category.query.count(),
        'tag_count': Tag.query.count(),
        'comment_count': Comment.query.count(),
        'pending_comments': Comment.query.filter_by(approved=False).count(),
        'user_count': User.query.count(),
        'media_count': Media.query.count()
    }
    
    # 最新文章
    latest_articles = Article.query.order_by(Article.created_at.desc()).limit(5).all()
    # 待审核评论
    pending_comments = Comment.query.filter_by(approved=False).order_by(Comment.created_at.desc()).limit(5).all()
    # 最近注册用户
    latest_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    
    return render_template('admin/index.html', stats=stats, latest_articles=latest_articles,
                          pending_comments=pending_comments, latest_users=latest_users)

# 文章管理
@admin.route('/articles')
@admin_required
def articles():
    """文章列表页"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'all')
    category_id = request.args.get('category', None, type=int)
    tag_id = request.args.get('tag', None, type=int)
    search = request.args.get('search', '')
    
    query = Article.query
    
    if status == 'published':
        query = query.filter_by(published=True)
    elif status == 'draft':
        query = query.filter_by(published=False)
    
    if category_id:
        query = query.filter_by(category_id=category_id)
    
    if tag_id:
        tag = Tag.query.get(tag_id)
        if tag:
            query = query.filter(Article.tags.contains(tag))
    
    if search:
        query = query.filter(Article.title.contains(search))
    
    pagination = query.order_by(Article.created_at.desc()).paginate(page=page, per_page=15, error_out=False)
    articles = pagination.items
    
    # 获取所有分类和标签，用于筛选
    categories = Category.query.all()
    tags = Tag.query.all()
    
    return render_template('admin/articles.html', articles=articles, pagination=pagination,
                          status=status, category_id=category_id, tag_id=tag_id, search=search,
                          categories=categories, tags=tags)

@admin.route('/articles/create', methods=['GET', 'POST'])
@admin_required
def create_article():
    """创建文章"""
    form = ArticleForm()
    
    if form.validate_on_submit():
        # 创建文章
        article = Article()
        article.title = form.title.data
        article.slug = form.slug.data
        article.summary = form.summary.data
        article.meta_title = form.meta_title.data
        article.meta_description = form.meta_description.data
        article.author_id = current_user.id
        article.category_id = form.category_id.data
        
        # 处理内容块
        if form.content_blocks.data:
            try:
                article.content_blocks = json.loads(form.content_blocks.data)
            except:
                article.content_blocks = form.content_blocks.data
        
        # 处理封面图片
        if form.cover_image.data:
            cover_file = form.cover_image.data
            if cover_file and allowed_file(cover_file.filename):
                filename = save_uploaded_file(cover_file)
                article.featured_image = filename
        
        # 处理标签
        tag_names = [tag.strip() for tag in (form.tags.data or '').split(',') if tag.strip()]
        for tag_name in tag_names:
            tag = Tag.query.filter_by(name=tag_name).first()
            if not tag:
                tag = Tag()
                tag.name = tag_name
                db.session.add(tag)
            article.tags.append(tag)
        
        # 处理发布状态
        if form.published.data:
            article.published = True
            article.published_at = datetime.utcnow()
        
        # 处理推荐状态
        article.featured = form.featured.data
        
        db.session.add(article)
        db.session.commit()
        
        # 如果文章已发布，刷新自动内链缓存
        if article.published:
            from utils.autolink import refresh_keyword_cache
            refresh_keyword_cache()
        
        flash('文章创建成功！', 'success')
        return redirect(url_for('admin.articles'))
    
    # 如果是GET请求或表单未通过验证
    # 为slug字段生成一个初始值
    if not form.slug.data and form.title.data:
        form.slug.data = generate_slug(form.title.data)
    
    return render_template('admin/edit_article.html', form=form, article=None, is_edit=False)

@admin.route('/articles/edit/<int:id>', methods=['GET', 'POST'])
@admin_required
def edit_article(id):
    """编辑文章"""
    article = Article.query.get_or_404(id)
    form = ArticleForm(obj=article)
    
    if form.validate_on_submit():
        # 更新文章基本信息
        article.title = form.title.data
        article.slug = form.slug.data
        article.summary = form.summary.data
        article.meta_title = form.meta_title.data
        article.meta_description = form.meta_description.data
        article.category_id = form.category_id.data
        article.source_url = form.source_url.data
        
        # 处理内容块
        if form.content_blocks.data:
            try:
                # 尝试解析JSON格式的内容块
                article.content_blocks = json.loads(form.content_blocks.data)
            except:
                # 如果解析失败，直接使用表单数据
                article.content_blocks = form.content_blocks.data
        
        # 处理封面图片
        if form.cover_image.data:
            cover_file = form.cover_image.data
            if cover_file and allowed_file(cover_file.filename):
                filename = save_uploaded_file(cover_file)
                article.featured_image = filename
        
        # 处理标签
        article.tags.clear()
        tag_names = [tag.strip() for tag in (form.tags.data or '').split(',') if tag.strip()]
        for tag_name in tag_names:
            tag = Tag.query.filter_by(name=tag_name).first()
            if not tag:
                tag = Tag()
                tag.name = tag_name
                db.session.add(tag)
            article.tags.append(tag)
        
        # 处理发布状态
        if form.published.data and not article.published:
            article.publish()
        elif not form.published.data and article.published:
            article.unpublish()
        
        # 处理推荐状态
        article.featured = form.featured.data
        
        db.session.commit()
        
        # 如果文章已发布，刷新自动内链缓存
        if article.published:
            from utils.autolink import refresh_keyword_cache
            refresh_keyword_cache()
        
        flash('文章更新成功！', 'success')
        return redirect(url_for('admin.articles'))
    
    # 如果是GET请求
    if request.method == 'GET':
        # 填充标签字段
        form.tags.data = ', '.join([tag.name for tag in article.tags])
        
        # 填充内容块
        if article.content_blocks:
            # 确保内容块是JSON格式
            if isinstance(article.content_blocks, str):
                try:
                    content_blocks = json.loads(article.content_blocks)
                    form.content_blocks.data = json.dumps(content_blocks)
                except:
                    form.content_blocks.data = article.content_blocks
            else:
                # 已经是JSON对象，直接转换为字符串
                form.content_blocks.data = json.dumps(article.content_blocks)
    
    return render_template('admin/edit_article.html', form=form, article=article, is_edit=True)

@admin.route('/articles/delete/<int:id>', methods=['POST'])
@admin_required
def delete_article(id):
    """删除文章"""
    article = Article.query.get_or_404(id)
    db.session.delete(article)
    db.session.commit()
    
    flash('文章已删除！', 'success')
    return redirect(url_for('admin.articles'))

# 分类管理
@admin.route('/categories')
@admin_required
def categories():
    """分类列表页"""
    categories = Category.query.all()
    return render_template('admin/categories.html', categories=categories)

@admin.route('/categories/create', methods=['GET', 'POST'])
@admin_required
def create_category():
    """创建分类"""
    form = CategoryForm()
    
    if form.validate_on_submit():
        category = Category()
        category.name = form.name.data
        category.slug = form.slug.data
        category.description = form.description.data
        category.secondary_category_id = form.secondary_category_id.data
        db.session.add(category)
        db.session.commit()
        
        flash('分类创建成功！', 'success')
        return redirect(url_for('admin.categories'))
    
    return render_template('admin/edit_category.html', form=form, is_edit=False)

@admin.route('/categories/edit/<int:id>', methods=['GET', 'POST'])
@admin_required
def edit_category(id):
    """编辑分类"""
    category = Category.query.get_or_404(id)
    form = CategoryForm(obj=category)
    form.data['id'] = category.id  # 用于slug验证
    
    if form.validate_on_submit():
        category.name = form.name.data
        category.slug = form.slug.data
        category.description = form.description.data
        category.secondary_category_id = form.secondary_category_id.data
        
        db.session.commit()
        
        flash('分类更新成功！', 'success')
        return redirect(url_for('admin.categories'))
    
    return render_template('admin/edit_category.html', form=form, category=category, is_edit=True)

@admin.route('/categories/delete/<int:id>', methods=['POST'])
@admin_required
def delete_category(id):
    """删除分类"""
    category = Category.query.get_or_404(id)
    
    # 检查是否有使用此分类的文章
    if category.articles.count() > 0:
        flash('无法删除！该分类下有关联的文章', 'danger')
        return redirect(url_for('admin.categories'))
    
    db.session.delete(category)
    db.session.commit()
    
    flash('分类已删除！', 'success')
    return redirect(url_for('admin.categories'))

# 标签管理
@admin.route('/tags')
@admin_required
def tags():
    """标签列表页"""
    tags = Tag.query.all()
    return render_template('admin/tags.html', tags=tags)

@admin.route('/tags/create', methods=['GET', 'POST'])
@admin_required
def create_tag():
    """创建标签"""
    form = TagForm()
    
    if form.validate_on_submit():
        tag = Tag()
        tag.name = form.name.data
        db.session.add(tag)
        db.session.commit()
        
        flash('标签创建成功！', 'success')
        return redirect(url_for('admin.tags'))
    
    return render_template('admin/edit_tag.html', form=form, is_edit=False)

@admin.route('/tags/edit/<int:id>', methods=['GET', 'POST'])
@admin_required
def edit_tag(id):
    """编辑标签"""
    tag = Tag.query.get_or_404(id)
    form = TagForm(obj=tag)
    
    if form.validate_on_submit():
        tag.name = form.name.data
        db.session.commit()
        
        flash('标签更新成功！', 'success')
        return redirect(url_for('admin.tags'))
    
    return render_template('admin/edit_tag.html', form=form, tag=tag, is_edit=True)

@admin.route('/tags/delete/<int:id>', methods=['POST'])
@admin_required
def delete_tag(id):
    """删除标签"""
    tag = Tag.query.get_or_404(id)
    
    # 从所有文章中移除此标签关联
    for article in tag.articles:
        article.tags.remove(tag)
    
    db.session.delete(tag)
    db.session.commit()
    
    flash('标签已删除！', 'success')
    return redirect(url_for('admin.tags'))

# 评论管理
@admin.route('/comments')
@admin_required
def comments():
    """评论列表页"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', 'all')
    
    query = Comment.query
    
    if status == 'approved':
        query = query.filter_by(approved=True)
    elif status == 'pending':
        query = query.filter_by(approved=False)
    
    pagination = query.order_by(Comment.created_at.desc()).paginate(page=page, per_page=20, error_out=False)
    comments = pagination.items
    
    return render_template('admin/comments.html', comments=comments, pagination=pagination, status=status)

@admin.route('/comments/approve/<int:id>', methods=['POST'])
@admin_required
def approve_comment(id):
    """审核评论"""
    comment = Comment.query.get_or_404(id)
    comment.approved = True
    db.session.commit()
    
    flash('评论已审核通过！', 'success')
    return redirect(url_for('admin.comments'))

@admin.route('/comments/reject/<int:id>', methods=['POST'])
@admin_required
def reject_comment(id):
    """驳回评论"""
    comment = Comment.query.get_or_404(id)
    db.session.delete(comment)
    db.session.commit()
    
    flash('评论已驳回！', 'success')
    return redirect(url_for('admin.comments'))

# 媒体管理
@admin.route('/media')
@admin_required
def media():
    """媒体列表页"""
    page = request.args.get('page', 1, type=int)
    
    pagination = Media.query.order_by(Media.uploaded_at.desc()).paginate(page=page, per_page=24, error_out=False)
    media_files = pagination.items
    
    return render_template('admin/media.html', media_files=media_files, pagination=pagination)

@admin.route('/media/upload', methods=['GET', 'POST'])
@admin_required
def upload_media():
    """上传媒体"""
    form = MediaUploadForm()
    
    if form.validate_on_submit():
        file = form.file.data
        filename = save_uploaded_file(file)
        
        media = Media()
        media.filename = os.path.basename(filename)
        media.filepath = filename
        media.filetype = file.content_type
        media.filesize = os.path.getsize(os.path.join(current_app.config['UPLOAD_FOLDER'], filename))
        media.alt_text = form.alt_text.data
        media.user_id = current_user.id
        
        db.session.add(media)
        db.session.commit()
        
        flash('文件上传成功！', 'success')
        return redirect(url_for('admin.media'))
    
    return render_template('admin/upload_media.html', form=form)

@admin.route('/media/delete/<int:id>', methods=['POST'])
@admin_required
def delete_media(id):
    """删除媒体"""
    media = Media.query.get_or_404(id)
    
    # 删除文件
    try:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], media.filepath)
        if os.path.exists(file_path):
            os.remove(file_path)
    except Exception as e:
        flash(f'删除文件时出错：{str(e)}', 'warning')
    
    # 删除数据库记录
    db.session.delete(media)
    db.session.commit()
    
    flash('媒体文件已删除！', 'success')
    return redirect(url_for('admin.media'))

# 用户管理
@admin.route('/users')
@admin_required
def users():
    """用户列表页"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = User.query
    
    if search:
        query = query.filter((User.username.contains(search)) | (User.email.contains(search)))
    
    pagination = query.order_by(User.created_at.desc()).paginate(page=page, per_page=15, error_out=False)
    users = pagination.items
    
    return render_template('admin/users.html', users=users, pagination=pagination, search=search)

@admin.route('/users/edit/<int:id>', methods=['GET', 'POST'])
@admin_required
def edit_user(id):
    """编辑用户"""
    user = User.query.get_or_404(id)
    
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        is_admin = 'is_admin' in request.form
        password = request.form.get('password')
        
        # 检查username是否已被使用
        if username != user.username and User.query.filter_by(username=username).first():
            flash('用户名已被使用', 'danger')
            return redirect(url_for('admin.edit_user', id=id))
        
        # 检查email是否已被使用
        if email != user.email and User.query.filter_by(email=email).first():
            flash('邮箱已被使用', 'danger')
            return redirect(url_for('admin.edit_user', id=id))
        
        # 更新用户信息
        user.username = username
        user.email = email
        user.is_admin = is_admin
        
        # 如果提供了新密码，则更新密码
        if password:
            user.set_password(password)
        
        db.session.commit()
        
        flash('用户信息更新成功！', 'success')
        return redirect(url_for('admin.users'))
    
    return render_template('admin/edit_user.html', user=user)

@admin.route('/users/delete/<int:id>', methods=['POST'])
@admin_required
def delete_user(id):
    """删除用户"""
    user = User.query.get_or_404(id)
    
    # 不能删除自己
    if user.id == current_user.id:
        flash('不能删除自己的账号！', 'danger')
        return redirect(url_for('admin.users'))
    
    db.session.delete(user)
    db.session.commit()
    
    flash('用户已删除！', 'success')
    return redirect(url_for('admin.users'))

@admin.route('/api_token/generate/<int:id>', methods=['POST'])
@admin_required
def generate_api_token(id):
    """为用户生成API访问令牌"""
    user = User.query.get_or_404(id)
    
    # 生成一个简单的令牌，实际应用中可以使用更安全的方法
    import secrets
    token = secrets.token_hex(16)
    user.api_token = token
    db.session.commit()
    
    flash(f'API令牌生成成功: {token}', 'success')
    return redirect(url_for('admin.edit_user', id=id))

@admin.route('/api_token/revoke/<int:id>', methods=['POST'])
@admin_required
def revoke_api_token(id):
    """撤销用户的API访问令牌"""
    user = User.query.get_or_404(id)
    user.api_token = None
    db.session.commit()
    
    flash('API令牌已撤销', 'success')
    return redirect(url_for('admin.edit_user', id=id))

# 设置
@admin.route('/settings', methods=['GET', 'POST'])
@admin_required
def settings():
    """网站设置"""
    from flask import current_app
    
    if request.method == 'POST':
        site_name = request.form.get('site_name')
        site_description = request.form.get('site_description')
        allow_comments = 'allow_comments' in request.form
        require_approval = 'require_approval' in request.form
        
        # 更新设置
        current_app.config['SITE_NAME'] = site_name
        current_app.config['SITE_DESCRIPTION'] = site_description
        current_app.config['ALLOW_COMMENTS'] = allow_comments
        current_app.config['REQUIRE_COMMENT_APPROVAL'] = require_approval
        
        # 在实际应用中，你应该将这些设置保存到数据库或配置文件中
        
        flash('设置已更新！', 'success')
        return redirect(url_for('admin.settings'))
    
    # 获取当前设置
    settings = {
        'site_name': current_app.config.get('SITE_NAME', 'Article CMS'),
        'site_description': current_app.config.get('SITE_DESCRIPTION', '一个简单的文章内容管理系统'),
        'allow_comments': current_app.config.get('ALLOW_COMMENTS', True),
        'require_approval': current_app.config.get('REQUIRE_COMMENT_APPROVAL', True)
    }
    
    return render_template('admin/settings.html', settings=settings)

@admin.route('/refresh-autolinks', methods=['POST'])
@admin_required
def refresh_autolinks():
    """刷新自动内链缓存"""
    from utils.autolink import refresh_keyword_cache
    refresh_keyword_cache()
    flash('自动内链缓存已刷新', 'success')
    return redirect(url_for('admin.settings'))

# 添加一级分类管理路由
@admin.route('/primary_categories')
@login_required
@admin_required
def primary_categories():
    """显示一级分类列表"""
    primary_categories = PrimaryCategory.query.order_by(PrimaryCategory.order).all()
    return render_template('admin/primary_categories.html', primary_categories=primary_categories)

@admin.route('/primary_categories/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_primary_category():
    """创建新的一级分类"""
    if request.method == 'POST':
        name = request.form.get('name')
        slug = request.form.get('slug')
        description = request.form.get('description')
        icon = request.form.get('icon')
        image = request.form.get('image')
        order = request.form.get('order', 0, type=int)
        is_visible = request.form.get('is_visible') == 'on'
        
        if not name or not slug:
            flash('名称和别名不能为空', 'danger')
            return redirect(url_for('admin.create_primary_category'))
            
        existing = PrimaryCategory.query.filter_by(slug=slug).first()
        if existing:
            flash('该别名已存在，请使用其他别名', 'danger')
            return redirect(url_for('admin.create_primary_category'))
        
        primary_category = PrimaryCategory(
            name=name,
            slug=slug,
            description=description,
            icon=icon,
            image=image,
            order=order,
            is_visible=is_visible
        )
        
        db.session.add(primary_category)
        db.session.commit()
        
        flash('一级分类创建成功', 'success')
        return redirect(url_for('admin.primary_categories'))
    
    return render_template('admin/create_primary_category.html')

@admin.route('/primary_categories/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_primary_category(id):
    """编辑一级分类"""
    primary_category = PrimaryCategory.query.get_or_404(id)
    
    if request.method == 'POST':
        name = request.form.get('name')
        slug = request.form.get('slug')
        description = request.form.get('description')
        icon = request.form.get('icon')
        image = request.form.get('image')
        order = request.form.get('order', 0, type=int)
        is_visible = request.form.get('is_visible') == 'on'
        
        if not name or not slug:
            flash('名称和别名不能为空', 'danger')
            return redirect(url_for('admin.edit_primary_category', id=id))
            
        existing = PrimaryCategory.query.filter_by(slug=slug).first()
        if existing and existing.id != id:
            flash('该别名已存在，请使用其他别名', 'danger')
            return redirect(url_for('admin.edit_primary_category', id=id))
        
        primary_category.name = name
        primary_category.slug = slug
        primary_category.description = description
        primary_category.icon = icon
        primary_category.image = image
        primary_category.order = order
        primary_category.is_visible = is_visible
        
        db.session.commit()
        
        flash('一级分类更新成功', 'success')
        return redirect(url_for('admin.primary_categories'))
    
    return render_template('admin/edit_primary_category.html', primary_category=primary_category)

@admin.route('/primary_categories/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_primary_category(id):
    """删除一级分类"""
    primary_category = PrimaryCategory.query.get_or_404(id)
    
    # 检查是否有关联的二级分类
    if primary_category.secondary_categories.count() > 0:
        flash('无法删除有二级分类关联的一级分类', 'danger')
        return redirect(url_for('admin.primary_categories'))
    
    db.session.delete(primary_category)
    db.session.commit()
    
    flash('一级分类删除成功', 'success')
    return redirect(url_for('admin.primary_categories'))

# 添加二级分类管理路由
@admin.route('/secondary_categories')
@login_required
@admin_required
def secondary_categories():
    """显示二级分类列表"""
    secondary_categories = SecondaryCategory.query.join(PrimaryCategory).order_by(
        PrimaryCategory.order, SecondaryCategory.order
    ).all()
    return render_template('admin/secondary_categories.html', secondary_categories=secondary_categories)

@admin.route('/secondary_categories/create', methods=['GET', 'POST'])
@login_required
@admin_required
def create_secondary_category():
    """创建新的二级分类"""
    primary_categories = PrimaryCategory.query.order_by(PrimaryCategory.name).all()
    
    if len(primary_categories) == 0:
        flash('请先创建至少一个一级分类', 'warning')
        return redirect(url_for('admin.create_primary_category'))
    
    if request.method == 'POST':
        primary_category_id = request.form.get('primary_category_id', type=int)
        name = request.form.get('name')
        slug = request.form.get('slug')
        description = request.form.get('description')
        icon = request.form.get('icon')
        image = request.form.get('image')
        order = request.form.get('order', 0, type=int)
        is_visible = request.form.get('is_visible') == 'on'
        
        if not name or not slug or not primary_category_id:
            flash('名称、别名和一级分类不能为空', 'danger')
            return redirect(url_for('admin.create_secondary_category'))
        
        primary = PrimaryCategory.query.get(primary_category_id)
        if not primary:
            flash('所选一级分类不存在', 'danger')
            return redirect(url_for('admin.create_secondary_category'))
        
        existing = SecondaryCategory.query.filter_by(
            primary_category_id=primary_category_id, slug=slug
        ).first()
        
        if existing:
            flash('该一级分类下已有同名别名的二级分类', 'danger')
            return redirect(url_for('admin.create_secondary_category'))
        
        secondary_category = SecondaryCategory(
            primary_category_id=primary_category_id,
            name=name,
            slug=slug,
            description=description,
            icon=icon,
            image=image,
            order=order,
            is_visible=is_visible
        )
        
        db.session.add(secondary_category)
        db.session.commit()
        
        flash('二级分类创建成功', 'success')
        return redirect(url_for('admin.secondary_categories'))
    
    return render_template('admin/create_secondary_category.html', primary_categories=primary_categories)

@admin.route('/secondary_categories/edit/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_secondary_category(id):
    """编辑二级分类"""
    secondary_category = SecondaryCategory.query.get_or_404(id)
    primary_categories = PrimaryCategory.query.order_by(PrimaryCategory.name).all()
    
    if request.method == 'POST':
        primary_category_id = request.form.get('primary_category_id', type=int)
        name = request.form.get('name')
        slug = request.form.get('slug')
        description = request.form.get('description')
        icon = request.form.get('icon')
        image = request.form.get('image')
        order = request.form.get('order', 0, type=int)
        is_visible = request.form.get('is_visible') == 'on'
        
        if not name or not slug or not primary_category_id:
            flash('名称、别名和一级分类不能为空', 'danger')
            return redirect(url_for('admin.edit_secondary_category', id=id))
        
        primary = PrimaryCategory.query.get(primary_category_id)
        if not primary:
            flash('所选一级分类不存在', 'danger')
            return redirect(url_for('admin.edit_secondary_category', id=id))
        
        existing = SecondaryCategory.query.filter_by(
            primary_category_id=primary_category_id, slug=slug
        ).first()
        
        if existing and existing.id != id:
            flash('该一级分类下已有同名别名的二级分类', 'danger')
            return redirect(url_for('admin.edit_secondary_category', id=id))
        
        secondary_category.primary_category_id = primary_category_id
        secondary_category.name = name
        secondary_category.slug = slug
        secondary_category.description = description
        secondary_category.icon = icon
        secondary_category.image = image
        secondary_category.order = order
        secondary_category.is_visible = is_visible
        
        db.session.commit()
        
        flash('二级分类更新成功', 'success')
        return redirect(url_for('admin.secondary_categories'))
    
    return render_template(
        'admin/edit_secondary_category.html',
        secondary_category=secondary_category,
        primary_categories=primary_categories
    )

@admin.route('/secondary_categories/delete/<int:id>', methods=['POST'])
@login_required
@admin_required
def delete_secondary_category(id):
    """删除二级分类"""
    secondary_category = SecondaryCategory.query.get_or_404(id)
    
    # 检查是否有关联的三级分类
    categories_count = Category.query.filter_by(secondary_category_id=id).count()
    if categories_count > 0:
        flash('无法删除有三级分类关联的二级分类', 'danger')
        return redirect(url_for('admin.secondary_categories'))
    
    db.session.delete(secondary_category)
    db.session.commit()
    
    flash('二级分类删除成功', 'success')
    return redirect(url_for('admin.secondary_categories')) 