from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, PasswordField, BooleanField, SubmitField, SelectField, FileField, HiddenField
from wtforms.validators import DataRequired, Length, Email, EqualTo, ValidationError, URL, Optional
from flask_wtf.file import FileAllowed
from .models import User, Category, SecondaryCategory, PrimaryCategory

class LoginForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(), Length(1, 64)])
    password = PasswordField('密码', validators=[DataRequired()])
    remember_me = BooleanField('记住我')
    submit = SubmitField('登录')

class RegistrationForm(FlaskForm):
    username = StringField('用户名', validators=[DataRequired(), Length(1, 64)])
    email = StringField('电子邮箱', validators=[DataRequired(), Email()])
    password = PasswordField('密码', validators=[DataRequired(), Length(min=8)])
    password2 = PasswordField('确认密码', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('注册')
    
    def validate_username(self, field):
        if User.query.filter_by(username=field.data).first():
            raise ValidationError('用户名已被使用')
    
    def validate_email(self, field):
        if User.query.filter_by(email=field.data).first():
            raise ValidationError('邮箱已被使用')

class CategoryForm(FlaskForm):
    name = StringField('分类名称', validators=[DataRequired(), Length(1, 64)])
    slug = StringField('URL别名', validators=[DataRequired(), Length(1, 64)])
    description = TextAreaField('描述')
    secondary_category_id = SelectField('二级分类', coerce=int, validators=[DataRequired()])
    submit = SubmitField('提交')
    
    def __init__(self, *args, **kwargs):
        super(CategoryForm, self).__init__(*args, **kwargs)
        self.secondary_category_id.choices = [(sc.id, f"{sc.primary_category.name} - {sc.name}") 
                                             for sc in SecondaryCategory.query.join(
                                                 PrimaryCategory, 
                                                 SecondaryCategory.primary_category_id == PrimaryCategory.id).order_by(
                                                     PrimaryCategory.name, SecondaryCategory.name).all()]
    
    def validate_slug(self, field):
        category = Category.query.filter_by(slug=field.data).first()
        if category and category.id != getattr(self, 'id', None):
            raise ValidationError('此URL别名已被使用')

class TagForm(FlaskForm):
    name = StringField('标签名称', validators=[DataRequired(), Length(1, 64)])
    submit = SubmitField('提交')

class ArticleForm(FlaskForm):
    """文章表单"""
    title = StringField('标题', validators=[DataRequired(), Length(1, 255)])
    slug = StringField('URL别名', validators=[DataRequired(), Length(1, 255)])
    summary = TextAreaField('摘要')
    content_blocks = TextAreaField('内容块 (JSON格式)')
    cover_image = FileField('封面图', validators=[FileAllowed(['jpg', 'png', 'jpeg', 'gif'], '仅支持图片')])
    category_id = SelectField('分类', coerce=int)
    tags = StringField('标签 (用逗号分隔)')
    reviewer = StringField('审核者', validators=[Optional(), Length(0, 64)])
    source_url = StringField('来源URL', validators=[Optional(), URL()])
    meta_title = StringField('SEO标题', validators=[Optional(), Length(0, 255)])
    meta_description = StringField('SEO描述', validators=[Optional(), Length(0, 255)])
    published = BooleanField('发布')
    featured = BooleanField('推荐')
    submit = SubmitField('保存')
    
    # 添加一个帮助文本，说明Key Takeaways的使用方法
    def __init__(self, *args, **kwargs):
        super(ArticleForm, self).__init__(*args, **kwargs)
        self.content_blocks.description = """
        文章内容块的JSON格式。支持的类型有：paragraph, heading, subheading, image, list, quote, code, html, table, key_takeaways。
        
        Key Takeaways示例:
        {
            "type": "key_takeaways",
            "items": ["要点1", "要点2", "要点3"]
        }
        """
        self.category_id.choices = [(c.id, c.name) for c in Category.query.order_by(Category.name).all()]

class CommentForm(FlaskForm):
    content = TextAreaField('评论内容', validators=[DataRequired()])
    submit = SubmitField('提交评论')

class MediaUploadForm(FlaskForm):
    file = FileField('文件', validators=[DataRequired(), FileAllowed(['jpg', 'png', 'jpeg', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'], '不支持的文件类型')])
    alt_text = StringField('替代文本')
    submit = SubmitField('上传') 