#!/usr/bin/env python3
"""
基础计算器类 - 所有计算器工具的基类
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime
import json


class BaseCalculator(ABC):
    """
    计算器基类，定义所有计算器的通用接口和功能
    """
    
    def __init__(self):
        self.name = self.__class__.__name__
        self.errors = []
        self.warnings = []
    
    @abstractmethod
    def calculate(self, **kwargs) -> Dict[str, Any]:
        """
        执行计算的抽象方法，子类必须实现
        
        Args:
            **kwargs: 计算所需的参数
            
        Returns:
            Dict[str, Any]: 计算结果字典
        """
        pass
    
    @abstractmethod
    def validate_input(self, **kwargs) -> bool:
        """
        验证输入参数的抽象方法，子类必须实现
        
        Args:
            **kwargs: 需要验证的参数
            
        Returns:
            bool: 验证是否通过
        """
        pass
    
    def add_error(self, message: str):
        """添加错误信息"""
        self.errors.append(message)
    
    def add_warning(self, message: str):
        """添加警告信息"""
        self.warnings.append(message)
    
    def clear_messages(self):
        """清空错误和警告信息"""
        self.errors = []
        self.warnings = []
    
    def has_errors(self) -> bool:
        """检查是否有错误"""
        return len(self.errors) > 0
    
    def get_result_with_metadata(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        返回包含元数据的完整结果
        
        Args:
            result: 计算结果
            
        Returns:
            Dict[str, Any]: 包含元数据的完整结果
        """
        return {
            'success': not self.has_errors(),
            'result': result if not self.has_errors() else None,
            'errors': self.errors,
            'warnings': self.warnings,
            'calculator': self.name,
            'timestamp': datetime.now().isoformat()
        }
    
    def safe_calculate(self, **kwargs) -> Dict[str, Any]:
        """
        安全计算方法，包含错误处理
        
        Args:
            **kwargs: 计算参数
            
        Returns:
            Dict[str, Any]: 计算结果或错误信息
        """
        self.clear_messages()
        
        try:
            # 验证输入
            if not self.validate_input(**kwargs):
                return self.get_result_with_metadata({})
            
            # 执行计算
            result = self.calculate(**kwargs)
            return self.get_result_with_metadata(result)
            
        except Exception as e:
            self.add_error(f"计算过程中发生错误: {str(e)}")
            return self.get_result_with_metadata({})
    
    @staticmethod
    def parse_date(date_str: str) -> Optional[datetime]:
        """
        解析日期字符串
        
        Args:
            date_str: 日期字符串 (YYYY-MM-DD格式)
            
        Returns:
            datetime: 解析后的日期对象，失败返回None
        """
        try:
            return datetime.strptime(date_str, '%Y-%m-%d')
        except (ValueError, TypeError):
            return None
    
    @staticmethod
    def calculate_age_in_days(birth_date: datetime, target_date: datetime) -> int:
        """
        计算两个日期之间的天数差
        
        Args:
            birth_date: 出生日期
            target_date: 目标日期
            
        Returns:
            int: 天数差
        """
        return (target_date - birth_date).days
    
    @staticmethod
    def calculate_age_in_months(birth_date: datetime, target_date: datetime) -> float:
        """
        计算两个日期之间的月数差（近似）
        
        Args:
            birth_date: 出生日期
            target_date: 目标日期
            
        Returns:
            float: 月数差
        """
        days = (target_date - birth_date).days
        return days / 30.44  # 平均每月天数
    
    @staticmethod
    def format_percentile(value: float) -> str:
        """
        格式化百分位数显示
        
        Args:
            value: 百分位数值
            
        Returns:
            str: 格式化后的字符串
        """
        if value < 3:
            return f"第{value:.1f}百分位 (偏低)"
        elif value > 97:
            return f"第{value:.1f}百分位 (偏高)"
        elif 25 <= value <= 75:
            return f"第{value:.1f}百分位 (正常范围)"
        else:
            return f"第{value:.1f}百分位"
