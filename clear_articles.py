#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清空数据库中的文章数据
保留表结构，只删除数据
"""

import sqlite3
import os
import sys

def clear_articles_data():
    """删除数据库中的所有文章数据"""
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🗑️ 开始清空文章数据...")
        
        # 获取删除前的统计信息
        cursor.execute("SELECT COUNT(*) FROM article")
        article_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comment")
        comment_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM article_tags")
        article_tags_count = cursor.fetchone()[0]
        
        print(f"📊 删除前统计:")
        print(f"   文章数量: {article_count}")
        print(f"   评论数量: {comment_count}")
        print(f"   文章标签关联: {article_tags_count}")
        
        if article_count == 0:
            print("✅ 数据库中没有文章数据")
            return True
        
        # 确认删除
        confirm = input(f"\n⚠️ 确认要删除 {article_count} 篇文章及相关数据吗？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return False
        
        print("\n🗑️ 正在删除数据...")
        
        # 删除相关数据（按依赖关系顺序）
        
        # 1. 删除评论
        if comment_count > 0:
            cursor.execute("DELETE FROM comment")
            print(f"   ✅ 删除了 {comment_count} 条评论")
        
        # 2. 删除文章标签关联
        if article_tags_count > 0:
            cursor.execute("DELETE FROM article_tags")
            print(f"   ✅ 删除了 {article_tags_count} 条文章标签关联")
        
        # 3. 删除文章
        cursor.execute("DELETE FROM article")
        print(f"   ✅ 删除了 {article_count} 篇文章")
        
        # 重置自增ID（可选）
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sqlite_sequence'")
            if cursor.fetchone():
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='article'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='comment'")
                print("   ✅ 重置了自增ID")
            else:
                print("   ℹ️ 跳过自增ID重置（表不存在）")
        except Exception as e:
            print(f"   ⚠️ 重置自增ID时出错: {str(e)}")
            print("   ℹ️ 这不影响数据删除结果")
        
        # 提交更改
        conn.commit()
        
        # 验证删除结果
        cursor.execute("SELECT COUNT(*) FROM article")
        remaining_articles = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM comment")
        remaining_comments = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM article_tags")
        remaining_tags = cursor.fetchone()[0]
        
        print(f"\n📊 删除后统计:")
        print(f"   文章数量: {remaining_articles}")
        print(f"   评论数量: {remaining_comments}")
        print(f"   文章标签关联: {remaining_tags}")
        
        if remaining_articles == 0 and remaining_comments == 0 and remaining_tags == 0:
            print("\n✅ 所有文章数据已成功清空！")
            print("📋 表结构保持完整，可以重新导入数据")
            return True
        else:
            print("\n⚠️ 删除可能不完整，请检查")
            return False
            
    except Exception as e:
        print(f"❌ 删除数据时出错: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def show_table_info():
    """显示表结构信息"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📋 数据库表结构信息:")
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   📊 {table_name}: {count} 条记录")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询表信息时出错: {str(e)}")

if __name__ == "__main__":
    print("🗑️ 文章数据清理工具")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_table_info()
    else:
        clear_articles_data()
        print("\n" + "=" * 50)
        print("💡 提示:")
        print("   - 表结构已保留，可以重新导入数据")
        print("   - 使用 --info 参数查看当前数据统计")
        print("   - 运行 python clear_articles.py --info")
