from app import create_app
import traceback

app = create_app({'DEBUG': True})

if __name__ == '__main__':
    try:
        # 测试首页路由
        with app.test_client() as client:
            print("测试首页路由...")
            try:
                response = client.get('/')
                print(f"状态码: {response.status_code}")
                if response.status_code != 200:
                    print(f"错误: {response.data.decode('utf-8')}")
            except Exception as e:
                print(f"发生异常: {str(e)}")
                traceback.print_exc()
        
        # 测试文章列表路由
        with app.test_client() as client:
            print("\n测试文章列表路由...")
            try:
                response = client.get('/articles')
                print(f"状态码: {response.status_code}")
                if response.status_code != 200:
                    print(f"错误: {response.data.decode('utf-8')}")
            except Exception as e:
                print(f"发生异常: {str(e)}")
                traceback.print_exc()
        
        # 测试文章详情路由
        with app.test_client() as client:
            print("\n测试文章详情路由...")
            try:
                # 尝试访问第一篇文章
                from models import Article
                with app.app_context():
                    article = Article.query.first()
                    if article:
                        slug = article.slug
                        print(f"测试文章: {article.title} (slug: {slug})")
                        try:
                            response = client.get(f'/article/{slug}')
                            print(f"状态码: {response.status_code}")
                            if response.status_code != 200:
                                print(f"错误: {response.data.decode('utf-8')}")
                        except Exception as e:
                            print(f"访问文章详情页发生异常: {str(e)}")
                            traceback.print_exc()
                    else:
                        print("数据库中没有文章")
            except Exception as e:
                print(f"发生异常: {str(e)}")
                traceback.print_exc()
    
    except Exception as e:
        print(f"程序发生异常: {str(e)}")
        traceback.print_exc() 