# 📊 前端计算器类库集成指南

## 🎯 概述

本指南介绍如何将 `dist` 目录中的 Astro 静态网站工具封装为可复用的前端计算器类库，并在当前 Flask 项目中使用。

## 🏗️ 架构设计

### 📁 文件结构
```
static/js/calculators/
├── base-calculator.js          # 基础计算器类
├── growth-calculator.js        # 生长发育计算器
├── due-date-calculator.js      # 预产期计算器
├── conception-calculator.js    # 受孕日期计算器
├── pregnancy-weight-calculator.js  # 孕期体重计算器
├── baby-cost-calculator.js     # 婴儿花费计算器
├── height-predictor.js         # 身高预测器
├── gender-predictor.js         # 性别预测器
└── calculator-manager.js       # 计算器管理器
```

### 🔧 核心组件

#### 1. BaseCalculator (基础类)
- **功能**: 提供所有计算器的通用接口和功能
- **特性**: 
  - 表单验证和事件绑定
  - 错误处理和消息管理
  - 日期解析和格式化工具
  - 统一的结果显示接口

#### 2. CalculatorManager (管理器)
- **功能**: 统一管理所有计算器的初始化和使用
- **特性**:
  - 自动注册和初始化计算器
  - 根据页面路径自动加载对应计算器
  - 提供手动初始化接口

## 🚀 使用方法

### 1. 在模板中引入脚本

```html
<!-- 在模板底部引入计算器脚本 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/growth-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>
```

### 2. HTML 表单结构

```html
<!-- 计算器表单 -->
<form class="calculator-form" novalidate>
    <!-- 表单字段 -->
    <div class="mb-3">
        <label for="weight" class="form-label">体重 (千克)</label>
        <input type="number" class="form-control" id="weight" name="weight" required>
    </div>
    
    <button type="submit" class="btn btn-primary calculate-button" disabled>
        计算结果
    </button>
</form>

<!-- 结果显示区域 -->
<div class="results-container" style="display: none;">
    <div class="weight-percentile"></div>
    <div class="height-percentile"></div>
    <div class="head-percentile"></div>
</div>
```

### 3. 自动初始化

计算器管理器会根据页面 URL 自动初始化对应的计算器：

- `/tools/growth-calculator` → 生长发育计算器
- `/tools/due-date-calculator` → 预产期计算器
- `/tools/conception-calculator` → 受孕日期计算器

### 4. 手动初始化

```javascript
// 手动初始化特定计算器
window.calculatorManager.manualInit('growth', '.calculator-form', '.results-container');
```

## 🔧 创建新计算器

### 1. 继承基础类

```javascript
class NewCalculator extends BaseCalculator {
    constructor() {
        super('NewCalculator');
    }

    validateInput(data, showErrors = true) {
        this.clearMessages();
        
        // 验证逻辑
        if (!data.someField) {
            if (showErrors) this.addError('请输入必填字段');
        }
        
        return this.errors.length === 0;
    }

    calculate(data) {
        // 计算逻辑
        const result = {
            value: data.someField * 2,
            formatted: `结果: ${data.someField * 2}`
        };
        
        return result;
    }

    displayResult(result) {
        // 显示结果
        const resultElement = document.querySelector('.result-display');
        if (resultElement) {
            resultElement.textContent = result.formatted;
        }
    }
}

// 导出类
window.NewCalculator = NewCalculator;
```

### 2. 在管理器中注册

```javascript
// 在 calculator-manager.js 的 registerAllCalculators 方法中添加
if (window.NewCalculator) {
    this.register('new', new NewCalculator());
}
```

## 📋 已实现的计算器

### 1. 生长发育计算器 (GrowthCalculator)
- **功能**: 基于WHO标准计算婴儿体重、身高、头围百分位数
- **输入**: 性别、出生日期、测量日期、体重、身高、头围
- **输出**: 各项指标的百分位数和解释

### 2. 预产期计算器 (DueDateCalculator)
- **功能**: 根据末次月经计算预产期和孕周信息
- **输入**: 末次月经日期、月经周期长度
- **输出**: 预产期、当前孕周、孕期阶段、重要里程碑

## 🎨 样式定制

### CSS 类名约定

```css
/* 计算器容器 */
.calculator-form { }
.results-container { }

/* 表单元素 */
.calculate-button { }
.form-group { }
.form-label { }

/* 结果显示 */
.weight-percentile { }
.height-percentile { }
.head-percentile { }

/* 错误和警告 */
.error-message { }
.warning-message { }
```

## 🔄 从 dist 迁移步骤

### 1. 分析原有 JavaScript 逻辑
```javascript
// dist 中的原始代码
function calculateGrowth(weight, height, age) {
    // 计算逻辑
    return percentile;
}

// 转换为类方法
class GrowthCalculator extends BaseCalculator {
    calculate(data) {
        // 使用相同的计算逻辑
        const percentile = this.calculateGrowth(data.weight, data.height, data.age);
        return { percentile };
    }
}
```

### 2. 提取 WHO 标准数据
```javascript
// 将 dist 中的数据表转换为 JavaScript 对象
this.whoStandards = {
    boys: {
        weight: {
            0: [2.5, 2.9, 3.3, 3.9, 4.4],  // P3, P15, P50, P85, P97
            1: [3.4, 4.0, 4.5, 5.1, 5.8],
            // ...
        }
    }
};
```

### 3. 转换模板结构
```html
<!-- 从 Astro 模板转换为 Jinja2 模板 -->
<!-- Astro -->
<input type="date" id="birthDate" name="birthDate" required>

<!-- Jinja2 -->
<input type="date" class="form-control" id="birthDate" name="birthDate" required>
```

## 🚀 部署到静态网站

### 1. 生成静态 HTML
```python
# 使用 Flask-FlatPages 或类似工具生成静态页面
from flask_flatpages import FlatPages

app = Flask(__name__)
pages = FlatPages(app)

@app.route('/tools/<tool_name>')
def tool_page(tool_name):
    return render_template(f'tools/{tool_name}.html')
```

### 2. 构建脚本
```bash
#!/bin/bash
# build-static.sh

# 生成静态 HTML 文件
python generate_static.py

# 复制静态资源
cp -r static/ dist/
cp -r templates/ dist/

# 压缩 JavaScript
uglifyjs static/js/calculators/*.js -o dist/js/calculators.min.js
```

## 📊 性能优化

### 1. 懒加载
```javascript
// 只在需要时加载计算器
async function loadCalculator(name) {
    if (!window[name + 'Calculator']) {
        await import(`./calculators/${name}-calculator.js`);
    }
    return window[name + 'Calculator'];
}
```

### 2. 缓存计算结果
```javascript
class BaseCalculator {
    constructor() {
        this.cache = new Map();
    }
    
    calculate(data) {
        const key = JSON.stringify(data);
        if (this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        const result = this.doCalculate(data);
        this.cache.set(key, result);
        return result;
    }
}
```

## 🧪 测试

### 1. 单元测试
```javascript
// test/calculators/growth-calculator.test.js
describe('GrowthCalculator', () => {
    let calculator;
    
    beforeEach(() => {
        calculator = new GrowthCalculator();
    });
    
    test('should calculate percentile correctly', () => {
        const result = calculator.calculate({
            gender: 'boy',
            weight: 7.5,
            height: 68,
            ageInMonths: 6
        });
        
        expect(result.weight.percentile).toBeCloseTo(50, 1);
    });
});
```

### 2. 集成测试
```javascript
// 测试完整的用户交互流程
test('complete calculation flow', async () => {
    // 模拟用户输入
    document.querySelector('#weight').value = '7.5';
    document.querySelector('#height').value = '68';
    
    // 触发计算
    document.querySelector('.calculator-form').dispatchEvent(new Event('submit'));
    
    // 验证结果显示
    await waitFor(() => {
        expect(document.querySelector('.weight-percentile')).toHaveTextContent('7.5 千克');
    });
});
```

## 📝 总结

这个前端计算器类库设计具有以下优势：

1. **模块化**: 每个计算器独立封装，易于维护
2. **可复用**: 统一的接口设计，便于在不同页面使用
3. **静态友好**: 纯前端计算，适合静态网站部署
4. **易扩展**: 基于继承的设计，添加新计算器简单
5. **用户友好**: 实时验证和错误提示

通过这种方式，您可以轻松地将 dist 目录中的工具迁移到当前项目，并为未来的静态网站部署做好准备。
