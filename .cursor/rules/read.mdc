---
description: 
globs: 
alwaysApply: true
---
# 文章内容管理系统

这是一个基于Flask的文章内容管理系统，支持多用户、内容块结构化存储、API接口和完整的后台管理功能。

## 功能特点

- 用户管理：注册、登录、个人资料管理
- 文章管理：创建、编辑、发布、删除文章
- 内容结构化：使用JSON格式的内容块存储文章结构
- 分类与标签：对文章进行分类和标签管理
- 评论系统：支持用户评论和评论审核
- 后台管理：完整的管理界面，包括仪表盘、统计
- API接口：提供完整的RESTful API接口
- 响应式设计：支持各种设备浏览

## 技术栈

- 后端：Flask、SQLAlchemy、Jinja2
- 前端：Bootstrap 5、jQuery、Font Awesome
- 数据库：SQLite（可扩展至其他数据库）
- 工具库：Flask-Login、Flask-WTF、Flask-RESTful、Markdown等

## 安装

1. 克隆仓库
```
git clone <仓库地址>
cd article_system
```

2. 安装依赖
```
pip install -r requirements.txt
```

3. 运行应用
```
python run.py
```

## 配置

主要配置项在`run.py`文件中的`create_app`函数内，包括：

- `SECRET_KEY`：应用密钥
- `SQLALCHEMY_DATABASE_URI`：数据库连接字符串
- `UPLOAD_FOLDER`：上传文件存储路径
- `ALLOWED_EXTENSIONS`：允许上传的文件类型
- `MAX_CONTENT_LENGTH`：上传文件大小限制

## 目录结构

```
├── run.py                         # 应用主入口
├── check_db.py                    # 数据库检查工具
├── create_migration.py            # 创建数据库迁移脚本
├── create_test_data.py            # 生成测试数据
├── debug.py                       # 调试工具
├── batch_import_articles.py       # 批量导入文章工具
├── README_batch_import.md         # 批量导入说明文档
├── api/                           # API模块
│   └── __init__.py                # API蓝图定义
├── auth/                          # 认证模块
│   ├── __init__.py                # 认证蓝图定义
│   ├── forms.py                   # 认证表单
│   └── routes.py                  # 认证路由
├── core/                          # 核心模块
│   ├── __init__.py                # 核心初始化
│   ├── admin.py                   # 后台管理蓝图
│   ├── forms.py                   # 核心表单
│   ├── main.py                    # 主站路由
│   └── models.py                  # 数据模型定义
├── instance/                      # 实例配置和数据库
│   └── articles.db                # SQLite数据库文件
├── migrations/                    # 数据库迁移文件
├── scraped_articles/              # 爬取的文章存储
│   ├── html/                      # HTML格式文章
│   ├── keyjson/                   # JSON格式文章
│   └── imported/                  # 已导入文章
├── scrapers/                      # 爬虫脚本
├── static/                        # 静态文件
│   ├── css/                       # CSS样式
│   ├── js/                        # JavaScript脚本
│   ├── img/                       # 图片资源
│   └── uploads/                   # 上传文件存储路径
├── templates/                     # 模板文件
│   ├── admin/                     # 管理后台模板
│   ├── auth/                      # 认证相关模板
│   └── public/                    # 前台页面模板
├── tests/                         # 测试代码
├── utils/                         # 工具函数
│   ├── __init__.py                # 工具包初始化
│   ├── autolink.py                # 自动链接处理
│   └── utils.py                   # 通用工具函数
└── requirements.txt               # 依赖包列表
```

## 使用说明

### 管理后台

- 访问路径：`/admin`
- 默认管理员账号：`admin`
- 默认密码：`admin123`

### API使用

API基础路径：`/api`

主要端点：
- `GET /api/articles`：获取文章列表
- `GET /api/articles/<slug>`：获取单篇文章
- `POST /api/articles`：创建文章（需要认证）
- `PUT /api/articles/<id>`：更新文章（需要认证）
- `DELETE /api/articles/<id>`：删除文章（需要认证）

API认证：使用Bearer令牌认证，在请求头中添加`Authorization: Bearer <token>`

## 贡献

欢迎对项目提出改进建议和Bug反馈，可以通过Issue或Pull Request参与贡献。

## 许可证

本项目基于MIT许可证开源。 

## 数据库迁移

需要运行数据库迁移命令来应用这些变更：
```
flask db upgrade
```

然后可以通过后台管理界面添加分类数据，构建完整的导航结构。 