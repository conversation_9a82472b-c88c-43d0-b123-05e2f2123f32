from app import create_app
from models import db, User, Article, Category, Tag
from datetime import datetime
import json

app = create_app()

with app.app_context():
    # 检查是否已有测试数据
    if Article.query.count() > 0:
        print("数据库中已有文章，跳过测试数据创建")
        exit()
    
    # 确保有管理员用户
    admin = User.query.filter_by(username='admin').first()
    if not admin:
        admin = User()
        admin.username = 'admin'
        admin.email = '<EMAIL>'
        admin.is_admin = True
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("创建了管理员用户")
    
    # 创建分类
    categories = [
        {'name': '孕期指南', 'slug': 'pregnancy-guide'},
        {'name': '育儿百科', 'slug': 'parenting'},
        {'name': '产后恢复', 'slug': 'postpartum-recovery'}
    ]
    
    for cat_data in categories:
        if not Category.query.filter_by(slug=cat_data['slug']).first():
            category = Category()
            category.name = cat_data['name']
            category.slug = cat_data['slug']
            db.session.add(category)
    
    db.session.commit()
    print("创建了分类数据")
    
    # 创建标签
    tags = [
        {'name': '孕早期', 'slug': 'first-trimester'},
        {'name': '孕中期', 'slug': 'second-trimester'},
        {'name': '孕晚期', 'slug': 'third-trimester'},
        {'name': '营养', 'slug': 'nutrition'},
        {'name': '产检', 'slug': 'prenatal-care'}
    ]
    
    for tag_data in tags:
        if not Tag.query.filter_by(slug=tag_data['slug']).first():
            tag = Tag()
            tag.name = tag_data['name']
            tag.slug = tag_data['slug']
            db.session.add(tag)
    
    db.session.commit()
    print("创建了标签数据")
    
    # 获取创建的分类和标签
    pregnancy_guide = Category.query.filter_by(slug='pregnancy-guide').first()
    parenting = Category.query.filter_by(slug='parenting').first()
    
    first_trimester = Tag.query.filter_by(slug='first-trimester').first()
    second_trimester = Tag.query.filter_by(slug='second-trimester').first()
    nutrition = Tag.query.filter_by(slug='nutrition').first()
    
    # 创建文章
    articles = [
        {
            'title': '孕早期营养指南',
            'slug': 'first-trimester-nutrition-guide',
            'summary': '孕早期是胎儿发育的关键时期，合理的营养摄入对胎儿的健康发育至关重要。',
            'content_blocks': json.dumps([
                {
                    'type': 'heading',
                    'level': 1,
                    'content': '孕早期营养指南'
                },
                {
                    'type': 'paragraph',
                    'content': '孕早期是胎儿发育的关键时期，合理的营养摄入对胎儿的健康发育至关重要。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '孕早期需要注意的营养素'
                },
                {
                    'type': 'list',
                    'style': 'unordered',
                    'items': [
                        '叶酸：预防神经管缺陷',
                        '铁质：预防贫血',
                        '钙质：促进骨骼发育',
                        '蛋白质：提供必要的氨基酸'
                    ]
                },
                {
                    'type': 'paragraph',
                    'content': '孕早期可能会出现孕吐现象，影响食欲和营养摄入，建议少食多餐，选择易消化的食物。'
                }
            ]),
            'category': pregnancy_guide,
            'tags': [first_trimester, nutrition],
            'featured': True
        },
        {
            'title': '孕中期胎动知识',
            'slug': 'second-trimester-fetal-movement',
            'summary': '了解胎动的规律和意义，掌握正确的胎动计数方法。',
            'content_blocks': json.dumps([
                {
                    'type': 'heading',
                    'level': 1,
                    'content': '孕中期胎动知识'
                },
                {
                    'type': 'paragraph',
                    'content': '胎动是胎儿在宫内活动的表现，也是胎儿健康状况的重要指标。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '什么时候开始有胎动'
                },
                {
                    'type': 'paragraph',
                    'content': '一般在孕16-20周时，孕妈妈可以感受到胎动，初产妇可能会晚一些。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '如何计数胎动'
                },
                {
                    'type': 'paragraph',
                    'content': '选择胎动活跃的时间段，记录1小时内胎动的次数，正常情况下应不少于3-5次。'
                }
            ]),
            'category': pregnancy_guide,
            'tags': [second_trimester],
            'featured': True
        },
        {
            'title': '新生儿护理要点',
            'slug': 'newborn-care-essentials',
            'summary': '新手父母必读的新生儿护理知识，包括喂养、洗澡、睡眠等方面的实用技巧。',
            'content_blocks': json.dumps([
                {
                    'type': 'heading',
                    'level': 1,
                    'content': '新生儿护理要点'
                },
                {
                    'type': 'paragraph',
                    'content': '新生儿的护理需要格外细心和耐心，本文将介绍新生儿护理的几个重要方面。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '喂养'
                },
                {
                    'type': 'paragraph',
                    'content': '母乳是最理想的食物，建议按需哺乳，通常新生儿每2-3小时需要喂养一次。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '洗澡'
                },
                {
                    'type': 'paragraph',
                    'content': '水温应保持在37-38℃，洗澡时间不宜过长，避免着凉。'
                },
                {
                    'type': 'heading',
                    'level': 2,
                    'content': '睡眠'
                },
                {
                    'type': 'paragraph',
                    'content': '新生儿每天睡眠时间约16-20小时，应让宝宝仰卧睡眠，预防猝死综合征。'
                }
            ]),
            'category': parenting,
            'tags': [],
            'featured': False
        }
    ]
    
    for article_data in articles:
        if not Article.query.filter_by(slug=article_data['slug']).first():
            article = Article()
            article.title = article_data['title']
            article.slug = article_data['slug']
            article.summary = article_data['summary']
            article.content_blocks = article_data['content_blocks']
            article.author_id = admin.id
            article.category_id = article_data['category'].id
            article.published = True
            article.featured = article_data['featured']
            article.published_at = datetime.utcnow()
            article.featured_image = '/static/img/placeholder.jpg'
            
            for tag in article_data['tags']:
                article.tags.append(tag)
            
            db.session.add(article)
    
    db.session.commit()
    print("创建了文章数据")
    
    # 验证创建的数据
    articles = Article.query.all()
    print(f"数据库中现在有 {len(list(articles))} 篇文章") 