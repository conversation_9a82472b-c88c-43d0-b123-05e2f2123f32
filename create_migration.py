#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库迁移脚本
添加 User.api_token 和 Article.source_url 字段
"""

import sqlite3
import os

def migrate_database():
    """执行数据库迁移"""
    db_path = 'instance/articles.db'
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在!")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查user表是否有api_token字段
        cursor.execute("PRAGMA table_info(user)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'api_token' not in column_names:
            print("添加 api_token 字段到 user 表...")
            cursor.execute("ALTER TABLE user ADD COLUMN api_token VARCHAR(100)")
        else:
            print("user 表已有 api_token 字段")
        
        # 检查article表是否有source_url字段
        cursor.execute("PRAGMA table_info(article)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'source_url' not in column_names:
            print("添加 source_url 字段到 article 表...")
            cursor.execute("ALTER TABLE article ADD COLUMN source_url VARCHAR(500)")
        else:
            print("article 表已有 source_url 字段")
        
        # 提交更改
        conn.commit()
        print("数据库迁移完成!")
        return True
        
    except Exception as e:
        print(f"迁移数据库时出错: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    migrate_database() 