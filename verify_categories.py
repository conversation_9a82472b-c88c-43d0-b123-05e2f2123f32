#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证分类分配结果的脚本
"""

import sqlite3
import os

def verify_category_assignments():
    """验证文章的分类分配情况"""
    
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📊 分类分配验证报告")
        print("=" * 60)
        
        # 查询所有文章及其分类
        cursor.execute("""
            SELECT 
                a.id,
                a.title,
                c.name as category_name,
                c.id as category_id,
                parent.name as parent_category_name
            FROM article a
            LEFT JOIN category c ON a.category_id = c.id
            LEFT JOIN category parent ON c.parent_id = parent.id
            ORDER BY c.id, a.id
        """)
        
        articles = cursor.fetchall()
        
        # 统计分类分配情况
        category_stats = {}
        no_category_count = 0
        
        for article in articles:
            article_id, title, category_name, category_id, parent_category_name = article
            
            if category_name:
                full_category = f"{parent_category_name} > {category_name}" if parent_category_name else category_name
                if full_category not in category_stats:
                    category_stats[full_category] = []
                category_stats[full_category].append((article_id, title))
            else:
                no_category_count += 1
        
        # 显示结果
        print(f"📈 总文章数: {len(articles)}")
        print(f"✅ 已分类文章: {len(articles) - no_category_count}")
        print(f"❌ 未分类文章: {no_category_count}")
        print()
        
        if category_stats:
            print("📂 分类分配详情:")
            for category, articles_list in category_stats.items():
                print(f"\n🏷️ {category} ({len(articles_list)} 篇)")
                for article_id, title in articles_list:
                    print(f"   📄 [{article_id}] {title}")
        
        if no_category_count > 0:
            print(f"\n⚠️ 未分类文章:")
            for article in articles:
                if not article[2]:  # category_name is None
                    print(f"   📄 [{article[0]}] {article[1]}")
        
        # 显示分类层次结构
        print(f"\n🌳 分类层次结构:")
        cursor.execute("""
            SELECT 
                c.id,
                c.name,
                c.parent_id,
                parent.name as parent_name,
                COUNT(a.id) as article_count
            FROM category c
            LEFT JOIN category parent ON c.parent_id = parent.id
            LEFT JOIN article a ON c.id = a.category_id
            GROUP BY c.id, c.name, c.parent_id, parent.name
            ORDER BY c.parent_id, c.id
        """)
        
        categories = cursor.fetchall()
        
        # 构建层次结构
        root_categories = [cat for cat in categories if cat[2] is None]  # parent_id is None
        child_categories = [cat for cat in categories if cat[2] is not None]
        
        for root_cat in root_categories:
            cat_id, cat_name, parent_id, parent_name, article_count = root_cat
            print(f"📁 {cat_name} (ID: {cat_id}) - {article_count} 篇文章")
            
            # 显示子分类
            for child_cat in child_categories:
                child_id, child_name, child_parent_id, child_parent_name, child_article_count = child_cat
                if child_parent_id == cat_id:
                    print(f"  └── {child_name} (ID: {child_id}) - {child_article_count} 篇文章")
        
        conn.close()
        
        print("\n" + "=" * 60)
        print("✅ 验证完成！")
        
    except Exception as e:
        print(f"❌ 验证时出错: {str(e)}")

if __name__ == "__main__":
    verify_category_assignments()
