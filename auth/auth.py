from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime

from core.models import db, User
from core.forms import LoginForm, RegistrationForm

auth = Blueprint('auth', __name__)

@auth.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    # 如果用户已登录，则重定向到首页
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = LoginForm()
    
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        
        # 检查用户是否存在及密码是否正确
        if user and user.check_password(form.password.data):
            login_user(user, remember=form.remember_me.data)
            
            # 处理next参数，安全重定向
            next_page = request.args.get('next')
            if next_page and next_page.startswith('/'):
                return redirect(next_page)
            
            # 如果是管理员，重定向到管理后台
            if user.is_admin:
                return redirect(url_for('admin.index'))
            
            return redirect(url_for('main.index'))
        
        flash('用户名或密码错误', 'danger')
    
    return render_template('auth/login.html', form=form, title='登录')

@auth.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('您已成功登出', 'success')
    return redirect(url_for('main.index'))

@auth.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    # 如果用户已登录，则重定向到首页
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    form = RegistrationForm()
    
    if form.validate_on_submit():
        user = User()
        user.username = form.username.data
        user.email = form.email.data
        user.created_at = datetime.utcnow()
        user.set_password(form.password.data)
        
        db.session.add(user)
        db.session.commit()
        
        flash('注册成功！请登录', 'success')
        return redirect(url_for('auth.login'))
    
    return render_template('auth/register.html', form=form, title='注册')

@auth.route('/profile')
@login_required
def profile():
    """用户个人资料页"""
    return render_template('auth/profile.html', title='个人资料')

@auth.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    """编辑个人资料"""
    if request.method == 'POST':
        # 获取表单数据
        email = request.form.get('email')
        current_password = request.form.get('current_password')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')
        
        # 验证电子邮箱是否已存在
        if email != current_user.email:
            user_exists = User.query.filter_by(email=email).first()
            if user_exists:
                flash('此电子邮箱已被使用', 'danger')
                return redirect(url_for('auth.edit_profile'))
            
            current_user.email = email
        
        # 如果用户想要更改密码
        if current_password and new_password:
            # 验证当前密码
            if not current_user.check_password(current_password):
                flash('当前密码不正确', 'danger')
                return redirect(url_for('auth.edit_profile'))
            
            # 验证新密码和确认密码是否一致
            if new_password != confirm_password:
                flash('新密码和确认密码不一致', 'danger')
                return redirect(url_for('auth.edit_profile'))
            
            # 更新密码
            current_user.set_password(new_password)
        
        db.session.commit()
        
        flash('个人资料已更新', 'success')
        return redirect(url_for('auth.profile'))
    
    return render_template('auth/edit_profile.html', title='编辑个人资料')

@auth.route('/password/reset', methods=['GET', 'POST'])
def reset_password_request():
    """请求重置密码"""
    # 如果用户已登录，则重定向到首页
    if current_user.is_authenticated:
        return redirect(url_for('main.index'))
    
    if request.method == 'POST':
        email = request.form.get('email')
        user = User.query.filter_by(email=email).first()
        
        if user:
            # 在实际应用中，这里应该发送重置密码的邮件
            # 这里仅作为示例，显示一个消息
            flash('如果此邮箱已注册，我们已发送密码重置链接', 'info')
        else:
            # 即使用户不存在，也显示相同的消息，避免信息泄露
            flash('如果此邮箱已注册，我们已发送密码重置链接', 'info')
        
        return redirect(url_for('auth.login'))
    
    return render_template('auth/reset_password_request.html', title='重置密码') 