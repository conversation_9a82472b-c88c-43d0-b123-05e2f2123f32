import os
import re
import uuid
from datetime import datetime
from flask import current_app
from werkzeug.utils import secure_filename
import markdown
import bleach
import json
from bs4 import BeautifulSoup

def generate_slug(text):
    """生成URL友好的别名"""
    # 将文本转为小写
    slug = text.lower()
    # 将空格替换为短横线
    slug = re.sub(r'\s+', '-', slug)
    # 替换所有非字母数字短横线的字符
    slug = re.sub(r'[^a-z0-9\-]', '', slug)
    # 删除多余的短横线
    slug = re.sub(r'\-+', '-', slug)
    # 删除开头和结尾的短横线
    slug = slug.strip('-')
    
    return slug

def allowed_file(filename):
    """检查文件是否是允许的类型"""
    allowed_extensions = current_app.config.get('ALLOWED_EXTENSIONS', {'jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx'})
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def save_uploaded_file(file):
    """保存上传的文件，返回文件名"""
    filename = secure_filename(file.filename)
    # 添加时间戳和UUID，确保唯一性
    filename = f"{datetime.now().strftime('%Y%m%d%H%M%S')}_{uuid.uuid4().hex[:8]}_{filename}"
    
    if not os.path.exists(current_app.config['UPLOAD_FOLDER']):
        os.makedirs(current_app.config['UPLOAD_FOLDER'])
    
    file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], filename)
    file.save(file_path)
    
    return filename

def format_datetime(value, format='%Y-%m-%d %H:%M'):
    """格式化日期时间"""
    if value is None:
        return ""
    return value.strftime(format)

def excerpt(content, length=150):
    """截取内容摘要"""
    if not content:
        return ""
    
    # 移除HTML标签
    text = bleach.clean(content, tags=[], strip=True)
    
    if len(text) <= length:
        return text
    
    return text[:length] + "..."

def markdown_to_html(text):
    """将Markdown文本转换为HTML"""
    if not text:
        return ""
    
    # 转换Markdown为HTML
    html = markdown.markdown(
        text,
        extensions=[
            'markdown.extensions.fenced_code',
            'markdown.extensions.tables',
            'markdown.extensions.nl2br',
            'markdown.extensions.sane_lists'
        ]
    )
    
    # 清理HTML，只允许安全标签
    allowed_tags = [
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'p', 'br', 'hr',
        'a', 'abbr', 'b', 'blockquote', 'code', 'em', 'i', 'li', 'ol', 'ul',
        'strong', 'pre', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'img',
        'span', 'div', 'small'
    ]
    
    allowed_attrs = {
        'a': ['href', 'title', 'target', 'rel'],
        'img': ['src', 'alt', 'title', 'width', 'height'],
        'table': ['class'],
        '*': ['class', 'id']
    }
    
    return bleach.clean(html, tags=allowed_tags, attributes=allowed_attrs)

def render_content_blocks(blocks):
    """渲染内容块为HTML"""
    if isinstance(blocks, str):
        try:
            blocks = json.loads(blocks)
            print("JSON字符串解析为:", json.dumps(blocks, ensure_ascii=False, indent=2))
        except json.JSONDecodeError as e:
            print("JSON解析错误:", str(e))
            return f"<p>内容解析错误: {str(e)}</p>"
    
    html = []
    for i, block in enumerate(blocks):
        print(f"渲染内容块 {i}:", json.dumps(block, ensure_ascii=False, indent=2))
        
        block_type = block.get('type')
        content = block.get('content', '')
        
        if block_type == 'paragraph':
            html.append(f'<p>{content}</p>')
        elif block_type == 'heading':
            level = block.get('level', 2)
            heading_id = normalize_heading_id(content)
            html.append(f'<h{level} id="{heading_id}">{content}</h{level}>')
        elif block_type == 'subheading':
            heading_id = normalize_heading_id(content)
            html.append(f'<h3 id="{heading_id}">{content}</h3>')
        elif block_type == 'image':
            caption = block.get('caption', '')
            alt_text = caption or '图片'
            html.append(f'<figure class="figure">')
            html.append(f'  <img src="{content}" alt="{alt_text}" class="figure-img img-fluid rounded">')
            if caption:
                html.append(f'  <figcaption class="figure-caption">{caption}</figcaption>')
            html.append(f'</figure>')
        elif block_type == 'list':
            # 检查是否有items字段，如果没有则从content中解析
            items = block.get('items', [])
            if not items and 'content' in block:
                items = [line.strip() for line in block['content'].split('\n') if line.strip()]
                
            list_type = block.get('style', 'unordered')
            if list_type == 'ordered':
                html.append('<ol>')
                for item in items:
                    html.append(f'  <li>{item}</li>')
                html.append('</ol>')
            else:
                html.append('<ul>')
                for item in items:
                    html.append(f'  <li>{item}</li>')
                html.append('</ul>')
        elif block_type == 'key_takeaways':
            # 渲染Key Takeaways内容块
            items = []
            
            # 尝试从不同字段获取要点列表
            if 'items' in block and isinstance(block['items'], list):
                items = block['items']
                print(f"从items字段获取要点列表: {items}")
            elif 'key_points' in block and isinstance(block['key_points'], list):
                items = block['key_points']
                print(f"从key_points字段获取要点列表: {items}")
            elif 'content' in block:
                if isinstance(block['content'], str):
                    items = [line.strip() for line in block['content'].split('\n') if line.strip()]
                    print(f"从content字符串解析要点列表: {items}")
                elif isinstance(block['content'], list):
                    items = block['content']
                    print(f"从content列表获取要点列表: {items}")
            
            # 确保items是列表类型且不为空
            if not isinstance(items, list) or not items:
                print(f"要点列表为空或不是列表类型，尝试使用默认内容")
                # 如果要点列表为空，使用默认内容
                items = ["暂无要点内容"]
            
            # 生成HTML
            html.append('<div class="key-takeaways">')
            html.append('  <h4>要点总结</h4>')
            html.append('  <ul>')
            for item in items:
                html.append(f'    <li>{item}</li>')
            html.append('  </ul>')
            html.append('</div>')
            print(f"渲染要点HTML完成，共 {len(items)} 个要点")
        elif block_type == 'quote':
            source = block.get('source', '')
            html.append('<blockquote class="blockquote">')
            html.append(f'  <p>{content}</p>')
            if source:
                html.append(f'  <footer class="blockquote-footer">{source}</footer>')
            html.append('</blockquote>')
        elif block_type == 'code':
            language = block.get('language', '')
            html.append(f'<pre><code class="language-{language}">')
            html.append(content)
            html.append('</code></pre>')
        elif block_type == 'html':
            html.append(content)
        elif block_type == 'table':
            html.append('<div class="table-responsive">')
            html.append('<table class="table">')
            # 支持header和headers两种字段命名
            headers = block.get('headers') or block.get('header', [])
            if headers:
                html.append('<thead>')
                html.append('  <tr>')
                for cell in headers:
                    html.append(f'    <th>{cell}</th>')
                html.append('  </tr>')
                html.append('</thead>')
            html.append('<tbody>')
            for row in block.get('rows', []):
                html.append('  <tr>')
                for cell in row:
                    html.append(f'    <td>{cell}</td>')
                html.append('  </tr>')
            html.append('</tbody>')
            html.append('</table>')
            html.append('</div>')
    
    return '\n'.join(html)

def normalize_heading_id(text):
    """生成标准化的标题ID，确保它们在JavaScript和CSS中是安全的"""
    # 转为小写
    heading_id = text.lower()
    # 移除特殊字符
    heading_id = re.sub(r'[^\w\s-]', '', heading_id)
    # 将空格替换为短横线
    heading_id = re.sub(r'\s+', '-', heading_id)
    # 修剪头尾空白和短横线
    heading_id = heading_id.strip('-')
    return heading_id

def extract_toc(html_content):
    """从HTML内容中提取目录"""
    soup = BeautifulSoup(html_content, 'html.parser')
    toc = []
    
    for heading in soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
        level = int(heading.name[1])
        text = heading.get_text()
        heading_id = heading.get('id', '')
        if not heading_id:
            # 使用更安全的标准化ID生成方法
            heading_id = text.lower()
            # 移除特殊字符
            heading_id = re.sub(r'[^\w\s-]', '', heading_id)
            # 将空格替换为短横线
            heading_id = re.sub(r'\s+', '-', heading_id)
            # 修剪头尾空白和短横线
            heading_id = heading_id.strip('-')
            
            # 设置ID到标题元素
            heading['id'] = heading_id
        
        toc.append({
            'level': level,
            'text': text,
            'id': heading_id
        })
    
    return toc

def generate_toc_html(toc_items):
    """生成HTML格式的目录"""
    if not toc_items:
        return ""
    
    html = ['<ul class="toc-list">']
    current_level = 2  # 通常文章内容从h2开始
    
    for item in toc_items:
        if item['level'] > current_level:
            # 当前标题级别更深，开始一个新的子列表
            html.append('<ul>')
            current_level = item['level']
        elif item['level'] < current_level:
            # 当前标题级别更浅，结束之前的子列表
            for _ in range(current_level - item['level']):
                html.append('</ul>')
            current_level = item['level']
        
        # 确保锚点href不包含问号等特殊字符
        clean_id = item["id"].replace('?', '')
        html.append(f'<li><a href="#{clean_id}">{item["text"]}</a></li>')
    
    # 关闭所有打开的ul标签
    for _ in range(current_level - 1):
        html.append('</ul>')
    
    html.append('</ul>')
    return '\n'.join(html)

def slugify(text):
    """生成URL友好的slug"""
    text = text.lower()
    text = ''.join(c for c in text if c.isalnum() or c == ' ')
    text = text.replace(' ', '-')
    return text

def date_filter(date):
    """日期格式化过滤器"""
    if not date:
        return ''
    return date.strftime('%Y-%m-%d')

def datetime_filter(date):
    """日期时间格式化过滤器"""
    if not date:
        return ''
    return date.strftime('%Y-%m-%d %H:%M') 