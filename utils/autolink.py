"""
Auto-linking Module for Flask

This module provides automatic internal linking functionality for article content.
It allows dynamic keyword replacement with links to relevant articles.
"""
from flask import current_app
import re
from functools import lru_cache
from bs4 import BeautifulSoup


@lru_cache(maxsize=1)
def get_keyword_mapping():
    """
    Retrieves all article titles and URLs from the database
    and creates a mapping of keywords to URLs.
    
    Returns:
        dict: Mapping of keywords to article URLs
    """
    # 在函数内部导入，避免循环导入
    from core.models import Article
    
    # Get all published articles with their titles and slugs
    articles = Article.query.filter_by(published=True).with_entities(
        Article.title, Article.slug
    ).all()
    
    keyword_map = {}
    for title, slug in articles:
        # Skip very short titles (less than 3 characters) to avoid ambiguous links
        if len(title) < 3:
            continue
        # Create the URL for the article
        url = f'/article/{slug}'
        # Add the title as a keyword
        keyword_map[title] = url
        
        # Add variations of the title if needed
        # For example: remove parentheses content
        clean_title = re.sub(r'\s*\([^)]*\)', '', title).strip()
        if clean_title != title and len(clean_title) >= 3:
            keyword_map[clean_title] = url
    
    return keyword_map


def refresh_keyword_cache():
    """
    Force refresh the keyword mapping cache
    """
    # Clear the cache by calling the function with different arguments
    get_keyword_mapping.cache_clear()
    # Pre-populate the cache
    get_keyword_mapping()


def apply_autolinks(content, current_article_slug=None):
    """
    Apply automatic linking to HTML content
    
    Args:
        content (str): HTML content to process
        current_article_slug (str, optional): Slug of the current article to avoid self-linking
        
    Returns:
        str: Processed HTML content with automatic links
    """
    if not content:
        return content
    
    # Parse the HTML content
    soup = BeautifulSoup(content, 'html.parser')
    
    # Get the keyword mapping
    keyword_map = get_keyword_mapping()
    
    # Track which keywords have been linked in this content
    linked_keywords = set()
    
    # Process each text node in the HTML
    for text_node in soup.find_all(text=True):
        # Skip if parent is a link, script, style, or code/pre element
        if text_node.parent.name in ['a', 'script', 'style', 'code', 'pre']:
            continue
        
        # Create a copy of the original text
        original_text = str(text_node)
        new_text = original_text
        
        # Sort keywords by length (longer first) to avoid partial replacements
        sorted_keywords = sorted(keyword_map.keys(), key=len, reverse=True)
        
        # Replace keywords with links
        for keyword in sorted_keywords:
            # Skip if keyword already linked in this content or too short
            if keyword in linked_keywords or len(keyword) < 3:
                continue
            
            # Case-insensitive search but preserve original case in link text
            pattern = re.compile(re.escape(keyword), re.IGNORECASE)
            
            # Check if the keyword exists in the text
            if pattern.search(new_text):
                # Create the replacement link
                url = keyword_map[keyword]
                
                # 检查是否是当前文章自身的链接
                if current_article_slug and f'/article/{current_article_slug}' == url:
                    # 跳过自我链接
                    continue
                
                # Replace the first occurrence only
                def replace_once(match):
                    # Mark this keyword as linked
                    linked_keywords.add(keyword)
                    # Get the matched text to preserve original case
                    matched_text = match.group(0)
                    # Return the link
                    return f'<a href="{url}" class="auto-link">{matched_text}</a>'
                
                # Apply replacement (only once per keyword per document)
                new_text = pattern.sub(replace_once, new_text, count=1)
                
                # Break after one replacement per text node to avoid nested links
                break
        
        # If text was modified, replace the original with the new linked version
        if new_text != original_text:
            # Create a new element with the linked text
            new_element = BeautifulSoup(new_text, 'html.parser')
            text_node.replace_with(new_element)
    
    # Return the modified HTML content
    return str(soup)


def init_autolink(app):
    """
    Initialize the auto-linking module with Flask app
    
    Args:
        app: Flask application instance
    """
    # Create a template filter for auto-linking
    @app.template_filter('autolink')
    def autolink_filter(content, article_slug=None):
        return apply_autolinks(content, article_slug) 