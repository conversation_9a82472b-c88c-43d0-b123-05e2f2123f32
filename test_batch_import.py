#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试批量导入功能的脚本
创建示例目录结构和JSON文件来测试分类自动创建功能
"""

import os
import json
import shutil
from datetime import datetime

def create_test_structure():
    """创建测试目录结构和示例JSON文件"""
    
    # 测试目录结构
    test_dirs = [
        "test_articles/怀孕期/孕早期",
        "test_articles/怀孕期/孕中期", 
        "test_articles/怀孕期/孕晚期",
        "test_articles/育儿/新生儿护理",
        "test_articles/育儿/婴儿喂养",
        "test_articles/育儿/幼儿教育",
        "test_articles/健康/营养指南",
        "test_articles/健康/运动健身",
        "test_articles/生活/家庭理财",
        "test_articles/生活/亲子活动"
    ]
    
    # 示例文章数据
    sample_articles = [
        {
            "title": "孕早期注意事项",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "text": "孕早期是怀孕的关键时期，需要特别注意营养和休息。"
                },
                {
                    "type": "paragraph",
                    "text": "建议孕妇多吃富含叶酸的食物，避免接触有害物质。"
                }
            ],
            "summary": "介绍孕早期的重要注意事项",
            "author_id": 1,
            "published": True
        },
        {
            "title": "孕中期营养指南",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "text": "孕中期胎儿发育迅速，孕妇需要补充更多营养。"
                },
                {
                    "type": "paragraph",
                    "text": "重点补充蛋白质、钙质和维生素。"
                }
            ],
            "summary": "孕中期营养补充指南",
            "author_id": 1,
            "published": True
        },
        {
            "title": "新生儿护理基础",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "text": "新生儿护理包括喂养、睡眠、清洁等多个方面。"
                },
                {
                    "type": "paragraph",
                    "text": "要特别注意保持新生儿的体温和卫生。"
                }
            ],
            "summary": "新生儿护理的基本知识",
            "author_id": 1,
            "published": True
        },
        {
            "title": "婴儿辅食添加时间表",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "text": "婴儿6个月后可以开始添加辅食，需要循序渐进。"
                },
                {
                    "type": "paragraph",
                    "text": "从单一食物开始，逐渐增加食物种类。"
                }
            ],
            "summary": "婴儿辅食添加的时间和方法",
            "author_id": 1,
            "published": True
        },
        {
            "title": "幼儿早期教育方法",
            "content_blocks": [
                {
                    "type": "paragraph",
                    "text": "幼儿早期教育对孩子的发展至关重要。"
                },
                {
                    "type": "paragraph",
                    "text": "通过游戏和互动来促进孩子的认知发展。"
                }
            ],
            "summary": "幼儿早期教育的方法和技巧",
            "author_id": 1,
            "published": True
        }
    ]
    
    print("🏗️ 创建测试目录结构...")
    
    # 清理旧的测试目录
    if os.path.exists("test_articles"):
        shutil.rmtree("test_articles")
    
    # 创建目录结构
    for test_dir in test_dirs:
        os.makedirs(test_dir, exist_ok=True)
        print(f"   📁 创建目录: {test_dir}")
    
    print("\n📝 创建示例JSON文件...")
    
    # 在每个目录中创建1-2个示例文件
    file_counter = 1
    for i, test_dir in enumerate(test_dirs):
        # 每个目录创建1-2个文件
        num_files = 1 if i % 2 == 0 else 2
        
        for j in range(num_files):
            article_data = sample_articles[file_counter % len(sample_articles)].copy()
            article_data["title"] = f"{article_data['title']} - {file_counter}"
            article_data["created_at"] = datetime.now().isoformat()
            
            filename = f"article_{file_counter:03d}.json"
            filepath = os.path.join(test_dir, filename)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(article_data, f, ensure_ascii=False, indent=2)
            
            print(f"   📄 创建文件: {filepath}")
            file_counter += 1
    
    print(f"\n✅ 测试结构创建完成!")
    print(f"📊 总共创建了 {file_counter - 1} 个JSON文件")
    print(f"📁 分布在 {len(test_dirs)} 个目录中")
    print("\n🚀 现在可以运行以下命令测试批量导入:")
    print("python batch_import_articles.py test_articles")

def show_directory_structure():
    """显示创建的目录结构"""
    print("\n📂 创建的目录结构:")
    print("test_articles/")
    
    for root, dirs, files in os.walk("test_articles"):
        level = root.replace("test_articles", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}├── {os.path.basename(root)}/")
        
        subindent = " " * 2 * (level + 1)
        for file in files:
            if file.endswith('.json'):
                print(f"{subindent}├── {file}")

if __name__ == "__main__":
    create_test_structure()
    show_directory_structure()
