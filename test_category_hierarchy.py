#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的分类层次结构创建功能
"""

import os
import sqlite3
import shutil
from datetime import datetime

def create_test_structure():
    """创建测试目录结构"""
    test_dirs = [
        "test_hierarchy/怀孕期/孕早期",
        "test_hierarchy/怀孕期/孕中期", 
        "test_hierarchy/育儿/新生儿护理",
        "test_hierarchy/育儿/婴儿喂养",
        "test_hierarchy/健康/营养指南"
    ]
    
    # 清理旧的测试目录
    if os.path.exists("test_hierarchy"):
        shutil.rmtree("test_hierarchy")
    
    # 创建目录结构
    for test_dir in test_dirs:
        os.makedirs(test_dir, exist_ok=True)
        print(f"📁 创建目录: {test_dir}")
    
    return test_dirs

def test_category_hierarchy():
    """测试分类层次结构创建"""
    print("🧪 测试分类层次结构创建功能")
    print("=" * 60)
    
    # 创建测试目录
    test_dirs = create_test_structure()
    
    # 导入修复后的函数
    import sys
    sys.path.insert(0, os.path.dirname(__file__))
    from batch_import_articles import get_or_create_category_hierarchy
    
    print("\n🔧 测试分类创建...")
    
    # 测试每个目录的分类创建
    for test_dir in test_dirs:
        print(f"\n📂 处理目录: {test_dir}")
        category_id = get_or_create_category_hierarchy(test_dir, "test_hierarchy")
        print(f"   返回的分类ID: {category_id}")
    
    # 验证数据库中的分类结构
    print(f"\n📊 验证数据库中的分类结构...")
    verify_database_structure()
    
    # 清理测试数据
    cleanup_test_data()

def verify_database_structure():
    """验证数据库中的分类结构"""
    try:
        db_path = os.path.join('instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🏢 一级分类:")
        cursor.execute("SELECT id, name, slug FROM primary_category ORDER BY id")
        primary_cats = cursor.fetchall()
        for cat in primary_cats:
            print(f"   [{cat[0]}] {cat[1]} ({cat[2]})")
        
        print("\n🏬 二级分类:")
        cursor.execute("""
            SELECT sc.id, sc.name, sc.slug, pc.name as primary_name
            FROM secondary_category sc
            JOIN primary_category pc ON sc.primary_category_id = pc.id
            ORDER BY sc.id
        """)
        secondary_cats = cursor.fetchall()
        for cat in secondary_cats:
            print(f"   [{cat[0]}] {cat[1]} ({cat[2]}) -> 属于: {cat[3]}")
        
        print("\n🏪 三级分类:")
        cursor.execute("""
            SELECT 
                c.id, 
                c.name, 
                c.slug,
                sc.name as secondary_name,
                pc.name as primary_name
            FROM category c
            LEFT JOIN secondary_category sc ON c.secondary_category_id = sc.id
            LEFT JOIN primary_category pc ON sc.primary_category_id = pc.id
            WHERE c.id > (SELECT COALESCE(MAX(id), 0) FROM category WHERE created_at < datetime('now', '-1 minute'))
            ORDER BY c.id
        """)
        categories = cursor.fetchall()
        
        for cat in categories:
            cat_id, name, slug, sec_name, pri_name = cat
            hierarchy = []
            if pri_name:
                hierarchy.append(pri_name)
            if sec_name:
                hierarchy.append(sec_name)
            hierarchy.append(name)
            
            hierarchy_str = ' > '.join(hierarchy)
            print(f"   [{cat_id}] {hierarchy_str}")
        
        # 验证关联关系
        print(f"\n🔗 验证关联关系:")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_categories,
                COUNT(c.secondary_category_id) as linked_to_secondary,
                COUNT(sc.primary_category_id) as secondary_linked_to_primary
            FROM category c
            LEFT JOIN secondary_category sc ON c.secondary_category_id = sc.id
            WHERE c.created_at > datetime('now', '-1 minute')
        """)
        stats = cursor.fetchone()
        print(f"   新创建的三级分类: {stats[0]}")
        print(f"   关联到二级分类: {stats[1]}")
        print(f"   二级分类关联到一级分类: {stats[2]}")
        
        if stats[0] == stats[1] and stats[1] == stats[2]:
            print("   ✅ 所有关联关系正确!")
        else:
            print("   ❌ 存在关联关系问题!")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证数据库结构时出错: {str(e)}")

def cleanup_test_data():
    """清理测试数据"""
    try:
        # 清理测试目录
        if os.path.exists("test_hierarchy"):
            shutil.rmtree("test_hierarchy")
            print(f"\n🧹 清理测试目录完成")
        
        # 注意：不清理数据库中的测试数据，以便查看结果
        print(f"💡 数据库中的测试分类已保留，可以手动查看验证")
        
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {str(e)}")

if __name__ == "__main__":
    test_category_hierarchy()
