#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import sys
import os
import glob
import time
import sqlite3
import uuid
import re
from pathlib import Path
from urllib.parse import quote
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def get_api_token():
    """获取API token"""
    # 检查是否有已存在的管理员用户
    import sqlite3
    try:
        # 使用新的数据库路径
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT id, username FROM user WHERE is_admin = 1 LIMIT 1")
        admin = cursor.fetchone()
        
        if admin:
            admin_id, admin_username = admin
            print(f"找到管理员用户: {admin_username} (ID: {admin_id})")
            
            # 检查是否已有API token
            cursor.execute("SELECT api_token FROM user WHERE id = ?", (admin_id,))
            token = cursor.fetchone()
            
            if token and token[0]:
                return token[0]
            
            # 生成新token
            import uuid
            new_token = str(uuid.uuid4())
            cursor.execute("UPDATE user SET api_token = ? WHERE id = ?", (new_token, admin_id))
            conn.commit()
            print(f"已为用户 {admin_username} 生成新的API token")
            return new_token
    except Exception as e:
        print(f"获取API token时出错: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()
    
    return None

def generate_slug(name):
    """生成URL友好的slug"""
    # 移除特殊字符，保留中文、英文、数字
    slug = re.sub(r'[^\w\u4e00-\u9fff\s-]', '', name)
    # 替换空格为短横线
    slug = re.sub(r'\s+', '-', slug.strip())
    # 移除多余的短横线
    slug = re.sub(r'-+', '-', slug)
    # 移除开头和结尾的短横线
    slug = slug.strip('-')
    return slug.lower()

def get_or_create_primary_category(cursor, name, slug):
    """获取或创建一级分类"""
    # 查找是否已存在
    cursor.execute("SELECT id FROM primary_category WHERE name = ?", (name,))
    existing = cursor.fetchone()

    if existing:
        return existing[0]

    # 检查slug是否已存在，如果存在则生成新的
    original_slug = slug
    counter = 1
    while True:
        cursor.execute("SELECT id FROM primary_category WHERE slug = ?", (slug,))
        if not cursor.fetchone():
            break
        slug = f"{original_slug}-{counter}"
        counter += 1

    # 创建新的一级分类
    cursor.execute("""
        INSERT INTO primary_category (name, slug, created_at, is_visible, "order")
        VALUES (?, ?, ?, ?, ?)
    """, (name, slug, datetime.utcnow(), True, 0))

    return cursor.lastrowid

def get_or_create_secondary_category(cursor, name, slug, primary_category_id):
    """获取或创建二级分类"""
    # 查找是否已存在（同名且属于同一一级分类）
    cursor.execute("""
        SELECT id FROM secondary_category
        WHERE name = ? AND primary_category_id = ?
    """, (name, primary_category_id))
    existing = cursor.fetchone()

    if existing:
        return existing[0]

    # 检查slug是否已存在，如果存在则生成新的
    original_slug = slug
    counter = 1
    while True:
        cursor.execute("SELECT id FROM secondary_category WHERE slug = ?", (slug,))
        if not cursor.fetchone():
            break
        slug = f"{original_slug}-{counter}"
        counter += 1

    # 创建新的二级分类
    cursor.execute("""
        INSERT INTO secondary_category (name, slug, primary_category_id, created_at, is_visible, "order")
        VALUES (?, ?, ?, ?, ?, ?)
    """, (name, slug, primary_category_id, datetime.utcnow(), True, 0))

    return cursor.lastrowid

def get_or_create_final_category(cursor, name, slug, secondary_category_id):
    """获取或创建三级分类（最终分类）"""
    # 查找是否已存在（同名且属于同一二级分类）
    if secondary_category_id:
        cursor.execute("""
            SELECT id FROM category
            WHERE name = ? AND secondary_category_id = ?
        """, (name, secondary_category_id))
    else:
        # 如果没有二级分类，查找独立的三级分类
        cursor.execute("""
            SELECT id FROM category
            WHERE name = ? AND secondary_category_id IS NULL AND parent_id IS NULL
        """, (name,))

    existing = cursor.fetchone()

    if existing:
        # 检查并修复关联关系
        if secondary_category_id:
            cursor.execute("""
                UPDATE category
                SET secondary_category_id = ?
                WHERE id = ? AND secondary_category_id IS NULL
            """, (secondary_category_id, existing[0]))
        return existing[0]

    # 检查name是否已存在，如果存在则生成新的
    original_name = name
    original_slug = slug
    counter = 1
    while True:
        cursor.execute("SELECT id FROM category WHERE name = ?", (name,))
        if not cursor.fetchone():
            break
        name = f"{original_name}-{counter}"
        slug = f"{original_slug}-{counter}"
        counter += 1

    # 再次检查slug是否已存在
    counter = 1
    while True:
        cursor.execute("SELECT id FROM category WHERE slug = ?", (slug,))
        if not cursor.fetchone():
            break
        slug = f"{original_slug}-{counter}"
        counter += 1

    # 创建新的三级分类
    cursor.execute("""
        INSERT INTO category (name, slug, secondary_category_id, created_at, is_visible, "order")
        VALUES (?, ?, ?, ?, ?, ?)
    """, (name, slug, secondary_category_id, datetime.utcnow(), True, 0))

    return cursor.lastrowid

def get_or_create_category_hierarchy(directory_path, base_directory):
    """
    根据目录结构创建正确的三层分类层次结构
    返回最终三级分类的ID
    """
    try:
        # 获取相对路径
        rel_path = os.path.relpath(directory_path, base_directory)
        if rel_path == '.':
            return None

        # 分割路径为各级目录
        path_parts = [part for part in rel_path.split(os.sep) if part]

        if len(path_parts) == 0:
            return None

        # 连接数据库
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        primary_category_id = None
        secondary_category_id = None
        final_category_id = None

        # 处理一级分类（如果有的话）
        if len(path_parts) >= 1:
            primary_name = path_parts[0].replace('_', ' ').replace('-', ' ').title()
            primary_slug = generate_slug(primary_name)

            primary_category_id = get_or_create_primary_category(cursor, primary_name, primary_slug)
            print(f"   一级分类: {primary_name} (ID: {primary_category_id})")

        # 处理二级分类（如果有的话）
        if len(path_parts) >= 2:
            secondary_name = path_parts[1].replace('_', ' ').replace('-', ' ').title()
            secondary_slug = generate_slug(secondary_name)

            secondary_category_id = get_or_create_secondary_category(
                cursor, secondary_name, secondary_slug, primary_category_id
            )
            print(f"   二级分类: {secondary_name} (ID: {secondary_category_id})")

        # 处理三级分类（最终分类）
        if len(path_parts) >= 3:
            # 如果有3级或更多，最后一级作为三级分类
            final_name = path_parts[-1].replace('_', ' ').replace('-', ' ').title()
        elif len(path_parts) == 2:
            # 如果只有2级，第二级作为三级分类（但名称要区别于二级分类）
            final_name = f"{path_parts[1].replace('_', ' ').replace('-', ' ').title()}"
        else:
            # 如果只有1级，第一级作为三级分类（但名称要区别于一级分类）
            final_name = f"{path_parts[0].replace('_', ' ').replace('-', ' ').title()}"

        final_slug = generate_slug(final_name)

        final_category_id = get_or_create_final_category(
            cursor, final_name, final_slug, secondary_category_id
        )
        print(f"   三级分类: {final_name} (ID: {final_category_id})")

        conn.commit()
        conn.close()
        return final_category_id

    except Exception as e:
        print(f"❌ 创建分类层次结构时出错: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return None

def find_json_files_recursive(directory):
    """递归查找所有JSON文件"""
    json_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过结果目录
        if 'imported' in root or 'failed' in root:
            continue

        for file in files:
            if file.endswith('.json'):
                json_files.append(os.path.join(root, file))

    return json_files

def import_article(api_url, api_token, article_data):
    """导入单篇文章到API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_token}"
    }
    
    try:
        response = requests.post(api_url, json=article_data, headers=headers)
        
        if response.status_code == 200 or response.status_code == 201:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 成功导入: {article_data.get('title')} (ID: {result.get('article_id')}, 别名: {result.get('article_slug')})")
                return True, result
            elif result.get("status") == "warning" and "已存在相似文章" in result.get("message", ""):
                print(f"⚠️ 文章已存在: {article_data.get('title')} (ID: {result.get('article_id')})")
                return False, result
            else:
                print(f"❌ 导入失败: {article_data.get('title')} - {result.get('message', '未知错误')}")
                return False, result
        else:
            print(f"❌ 请求错误 ({response.status_code}): {article_data.get('title')}")
            try:
                print(f"   错误详情: {response.json()}")
            except:
                print(f"   响应内容: {response.text[:100]}...")
            return False, None
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False, None

def batch_import_articles(api_url, api_token, directory):
    """批量导入指定目录下的所有JSON文件（递归遍历子目录）"""
    # 确保目录存在
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return False

    # 递归获取所有JSON文件
    json_files = find_json_files_recursive(directory)
    if not json_files:
        print(f"❌ 没有找到JSON文件: {directory}")
        return False

    print(f"找到 {len(json_files)} 个JSON文件（包含子目录）")
    
    # 创建导入结果目录
    imported_dir = os.path.join(directory, "imported")
    failed_dir = os.path.join(directory, "failed")
    os.makedirs(imported_dir, exist_ok=True)
    os.makedirs(failed_dir, exist_ok=True)
    
    # 导入统计
    stats = {
        "total": len(json_files),
        "success": 0,
        "exist": 0,
        "failed": 0
    }
    
    # 遍历所有JSON文件
    for i, json_file in enumerate(json_files):
        file_name = os.path.basename(json_file)
        file_dir = os.path.dirname(json_file)
        rel_path = os.path.relpath(file_dir, directory)

        print(f"\n[{i+1}/{stats['total']}] 处理: {file_name}")
        print(f"   目录: {rel_path}")

        try:
            # 读取JSON文件
            with open(json_file, 'r', encoding='utf-8') as f:
                article_data = json.load(f)

            # 根据目录结构获取或创建分类
            print(f"   正在分析目录结构...")
            category_id = get_or_create_category_hierarchy(file_dir, directory)
            if category_id:
                article_data['category_id'] = category_id
                print(f"   ✅ 分配分类ID: {category_id}")

                # 验证分类是否存在于数据库中
                try:
                    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM category WHERE id = ?", (category_id,))
                    category_name = cursor.fetchone()
                    if category_name:
                        print(f"   📂 分类名称: {category_name[0]}")
                    conn.close()
                except Exception as e:
                    print(f"   ⚠️ 验证分类时出错: {str(e)}")
            else:
                print(f"   ℹ️ 未分配分类（根目录文件）")

            # 导入文章
            success, result = import_article(api_url, api_token, article_data)
            
            # 移动文件到相应目录（保持目录结构）
            rel_file_path = os.path.relpath(json_file, directory)

            if success:
                stats["success"] += 1
                target_path = os.path.join(imported_dir, rel_file_path)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            elif result and result.get("status") == "warning":
                stats["exist"] += 1
                target_path = os.path.join(imported_dir, rel_file_path)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            else:
                stats["failed"] += 1
                target_path = os.path.join(failed_dir, rel_file_path)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            
            # 避免请求过于频繁
            time.sleep(0.5)
            
        except json.JSONDecodeError:
            print(f"❌ JSON解析错误: {file_name}")
            stats["failed"] += 1
            rel_file_path = os.path.relpath(json_file, directory)
            target_path = os.path.join(failed_dir, rel_file_path)
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            os.rename(json_file, target_path)
        except Exception as e:
            print(f"❌ 处理文件时出错: {str(e)}")
            stats["failed"] += 1
            try:
                rel_file_path = os.path.relpath(json_file, directory)
                target_path = os.path.join(failed_dir, rel_file_path)
                os.makedirs(os.path.dirname(target_path), exist_ok=True)
                os.rename(json_file, target_path)
            except:
                pass
    
    # 打印统计信息
    print("\n" + "="*60)
    print(f"📊 导入完成! 总计: {stats['total']} 篇文章")
    print(f"✅ 成功导入: {stats['success']} 篇")
    print(f"⚠️ 已存在: {stats['exist']} 篇")
    print(f"❌ 导入失败: {stats['failed']} 篇")
    print("="*60)
    print("📁 分类已根据目录结构自动创建和分配")
    print("📂 文件已按结果分类移动到 imported/ 和 failed/ 目录")
    print("="*60)

    return stats["success"] > 0

if __name__ == "__main__":
    print("🚀 智能文章批量导入工具")
    print("=" * 60)
    print("功能特性:")
    print("📁 递归遍历所有子目录中的JSON文件")
    print("🏷️ 根据目录结构自动创建分类层次")
    print("📂 自动移动文件到结果目录并保持目录结构")
    print("=" * 60)

    # 从环境变量或命令行参数获取API URL和token
    api_url = os.environ.get("API_URL") or "http://127.0.0.1:5002/api/articles/import"
    api_token = os.environ.get("API_TOKEN") or get_api_token() or "your_api_token_here"

    # 获取要导入的目录
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        # 更新默认目录
        directory = os.path.join(os.getcwd(), "scraped_articles")

    print(f"\n🔗 API URL: {api_url}")
    print(f"📁 导入目录: {directory}")
    print(f"🔑 API Token: {'已配置' if api_token and api_token != 'your_api_token_here' else '未配置'}")
    print("\n开始处理...")

    # 执行批量导入
    batch_import_articles(api_url, api_token, directory)