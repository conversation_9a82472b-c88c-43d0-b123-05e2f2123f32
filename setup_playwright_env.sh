#!/bin/bash

# Playwright Environment Setup Script
# 为Playwright创建完整的运行环境

set -e

echo "🚀 Setting up Playwright environment..."

# 1. 设置环境变量
export DISPLAY=:99
export PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright
export PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD=0
export PLAYWRIGHT_CHROMIUM_USE_HEADLESS_NEW=1
export CHROME_BIN=/usr/bin/google-chrome-stable
export CHROMIUM_BIN=/usr/bin/google-chrome-stable

# 2. 设置无头显示服务器
echo "📺 Setting up virtual display..."
if ! pgrep -x "Xvfb" > /dev/null; then
    Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
    export XVFB_PID=$!
    echo "Started Xvfb with PID: $XVFB_PID"
    sleep 2
else
    echo "Xvfb already running"
fi

# 3. 创建Playwright工作目录
echo "📁 Creating Playwright directories..."
mkdir -p /root/.cache/ms-playwright
mkdir -p /root/playwright-workspace
mkdir -p /root/playwright-workspace/screenshots
mkdir -p /root/playwright-workspace/downloads

# 4. 设置权限
chmod 755 /root/.cache/ms-playwright
chmod 755 /root/playwright-workspace

# 5. 清理旧的配置文件
echo "🧹 Cleaning old configurations..."
rm -rf /root/.cache/ms-playwright/mcp-chrome-profile
rm -rf /tmp/.org.chromium.Chromium.*

# 6. 安装/更新Playwright
echo "📦 Installing Playwright..."
cd /root/playwright-workspace

# 初始化package.json如果不存在
if [ ! -f package.json ]; then
    npm init -y
fi

# 安装Playwright
npm install playwright@latest
npm install @playwright/test@latest

# 7. 安装浏览器
echo "🌐 Installing browsers..."
npx playwright install chromium
npx playwright install-deps chromium

# 8. 创建Playwright配置文件
echo "⚙️ Creating Playwright configuration..."
cat > playwright.config.js << 'EOF'
module.exports = {
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    headless: true,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    acceptDownloads: true,
    launchOptions: {
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding'
      ]
    }
  },
  projects: [
    {
      name: 'chromium',
      use: { ...require('@playwright/test').devices['Desktop Chrome'] },
    }
  ],
  outputDir: 'test-results/',
  webServer: {
    command: 'npm run start',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
};
EOF

# 9. 创建测试示例
echo "🧪 Creating test example..."
mkdir -p tests
cat > tests/example.spec.js << 'EOF'
const { test, expect } = require('@playwright/test');

test('basic test', async ({ page }) => {
  await page.goto('https://playwright.dev/');
  
  // Expect a title "to contain" a substring.
  await expect(page).toHaveTitle(/Playwright/);
  
  // create a locator
  const getStarted = page.getByRole('link', { name: 'Get started' });
  
  // Expect an attribute "to be strictly equal" to the value.
  await expect(getStarted).toHaveAttribute('href', '/docs/intro');
  
  // Click the get started link.
  await getStarted.click();
  
  // Expects the URL to contain intro.
  await expect(page).toHaveURL(/.*intro/);
});

test('BabyCenter test', async ({ page }) => {
  await page.goto('https://www.babycenter.com/baby');
  
  // Take a screenshot
  await page.screenshot({ path: '../screenshots/babycenter.png' });
  
  // Check title
  await expect(page).toHaveTitle(/BabyCenter/);
  
  console.log('Page title:', await page.title());
});
EOF

# 10. 创建启动脚本
cat > run_playwright.sh << 'EOF'
#!/bin/bash
export DISPLAY=:99
export PLAYWRIGHT_BROWSERS_PATH=/root/.cache/ms-playwright
export PLAYWRIGHT_CHROMIUM_USE_HEADLESS_NEW=1
export CHROME_BIN=/usr/bin/google-chrome-stable

cd /root/playwright-workspace
npx playwright test "$@"
EOF

chmod +x run_playwright.sh

echo "✅ Playwright environment setup complete!"
echo ""
echo "📋 Environment Variables Set:"
echo "   DISPLAY=$DISPLAY"
echo "   PLAYWRIGHT_BROWSERS_PATH=$PLAYWRIGHT_BROWSERS_PATH"
echo "   CHROME_BIN=$CHROME_BIN"
echo ""
echo "🎯 Usage:"
echo "   cd /root/playwright-workspace"
echo "   ./run_playwright.sh                    # Run all tests"
echo "   ./run_playwright.sh tests/example.spec.js  # Run specific test"
echo "   npx playwright test --headed           # Run with browser visible"
echo "   npx playwright show-report             # Show test report"
echo ""
echo "📸 Screenshots will be saved to: /root/playwright-workspace/screenshots/"
echo "📊 Test results will be saved to: /root/playwright-workspace/test-results/"
