#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分类分配修复的脚本
"""

import os
import json
import sqlite3
import requests
from datetime import datetime

def get_api_token():
    """获取API token"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT api_token FROM user WHERE is_admin = 1 LIMIT 1")
        token = cursor.fetchone()
        conn.close()
        return token[0] if token and token[0] else None
    except Exception as e:
        print(f"获取API token失败: {str(e)}")
        return None

def create_test_category():
    """创建测试分类"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建测试分类
        cursor.execute("""
            INSERT INTO category (name, slug, created_at, is_visible, "order")
            VALUES (?, ?, ?, ?, ?)
        """, ("测试分类", "test-category", datetime.utcnow(), True, 0))
        
        category_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        print(f"✅ 创建测试分类成功，ID: {category_id}")
        return category_id
        
    except Exception as e:
        print(f"❌ 创建测试分类失败: {str(e)}")
        return None

def test_category_assignment():
    """测试分类分配功能"""
    print("🧪 开始测试分类分配功能")
    print("=" * 50)
    
    # 获取API token
    api_token = get_api_token()
    if not api_token:
        print("❌ 无法获取API token")
        return False
    
    # 创建测试分类
    category_id = create_test_category()
    if not category_id:
        return False
    
    # 准备测试文章数据
    test_article = {
        "title": "测试分类分配文章",
        "content_blocks": [
            {
                "type": "paragraph",
                "text": "这是一篇用于测试分类分配功能的文章。"
            }
        ],
        "category_id": category_id,  # 关键：设置category_id
        "published": True,
        "author_id": 1
    }
    
    # 发送API请求
    api_url = "http://127.0.0.1:5002/api/articles/import"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_token}"
    }
    
    print(f"📤 发送测试文章到API...")
    print(f"   分类ID: {category_id}")
    print(f"   文章标题: {test_article['title']}")
    
    try:
        response = requests.post(api_url, json=test_article, headers=headers)
        
        print(f"📥 API响应状态: {response.status_code}")
        
        if response.status_code in [200, 201]:
            result = response.json()
            print(f"✅ 文章导入成功")
            print(f"   文章ID: {result.get('article_id')}")
            print(f"   文章别名: {result.get('article_slug')}")
            
            # 验证数据库中的分类分配
            article_id = result.get('article_id')
            if article_id:
                return verify_category_assignment(article_id, category_id)
        else:
            print(f"❌ API请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

def verify_category_assignment(article_id, expected_category_id):
    """验证文章的分类分配是否正确"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查询文章的分类分配
        cursor.execute("""
            SELECT a.id, a.title, a.category_id, c.name as category_name
            FROM article a
            LEFT JOIN category c ON a.category_id = c.id
            WHERE a.id = ?
        """, (article_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            article_id, title, actual_category_id, category_name = result
            print(f"\n📊 数据库验证结果:")
            print(f"   文章ID: {article_id}")
            print(f"   文章标题: {title}")
            print(f"   实际分类ID: {actual_category_id}")
            print(f"   期望分类ID: {expected_category_id}")
            print(f"   分类名称: {category_name or '无分类'}")
            
            if actual_category_id == expected_category_id:
                print(f"✅ 分类分配正确！")
                return True
            else:
                print(f"❌ 分类分配错误！")
                return False
        else:
            print(f"❌ 未找到文章记录")
            return False
            
    except Exception as e:
        print(f"❌ 验证分类分配时出错: {str(e)}")
        return False

def cleanup_test_data():
    """清理测试数据"""
    try:
        db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 删除测试文章
        cursor.execute("DELETE FROM article WHERE title = '测试分类分配文章'")
        
        # 删除测试分类
        cursor.execute("DELETE FROM category WHERE name = '测试分类'")
        
        conn.commit()
        conn.close()
        
        print(f"🧹 清理测试数据完成")
        
    except Exception as e:
        print(f"⚠️ 清理测试数据时出错: {str(e)}")

if __name__ == "__main__":
    try:
        success = test_category_assignment()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 分类分配功能测试通过！")
        else:
            print("💥 分类分配功能测试失败！")
        print("=" * 50)
        
    finally:
        # 清理测试数据
        cleanup_test_data()
