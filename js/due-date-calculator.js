/**
 * Due Date Calculator
 * Implements five different calculation methods:
 * 1. Last menstrual period (LMP) - 40 weeks from LMP
 * 2. Conception date - 38 weeks from conception
 * 3. IVF transfer - 261 days from Day 5 transfer or 263 days from Day 3 transfer
 * 4. Ultrasound - Based on gestational age determined by ultrasound
 * 5. Known due date - Direct entry of due date
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const calculationMethods = document.querySelectorAll('input[name="calculation-method"]');
    const methodFields = document.querySelectorAll('.method-fields');
    const calculateBtn = document.getElementById('calculateBtn');
    const recalculateBtn = document.getElementById('recalculateBtn');
    const resultsContainer = document.getElementById('results');
    
    // Date inputs
    const lmpDateInput = document.getElementById('lmpDate');
    const conceptionDateInput = document.getElementById('conceptionDate');
    const ivfDateInput = document.getElementById('ivfDate');
    const ultrasoundDateInput = document.getElementById('ultrasoundDate');
    const knownDueDateInput = document.getElementById('knownDueDate');
    
    // Error messages
    const lmpDateError = document.getElementById('lmpDateError');
    const conceptionDateError = document.getElementById('conceptionDateError');
    const ivfDateError = document.getElementById('ivfDateError');
    const ultrasoundDateError = document.getElementById('ultrasoundDateError');
    const knownDueDateError = document.getElementById('knownDueDateError');
    
    // Set default dates
    const today = new Date();
    
    // Set date input min/max values
    const minDate = new Date(today);
    minDate.setFullYear(today.getFullYear() - 1); // 1 year ago
    
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() + 2); // 2 years ahead
    
    // Set min/max for date inputs
    [lmpDateInput, conceptionDateInput, ivfDateInput, ultrasoundDateInput, knownDueDateInput].forEach(input => {
        if (input) {
            input.min = formatDateForInput(minDate);
            input.max = formatDateForInput(maxDate);
        }
    });
    
    // Switch between calculation methods
    calculationMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Hide all method fields
            methodFields.forEach(field => {
                field.classList.remove('active');
            });
            
            // Show the selected method fields
            const selectedMethodId = this.value + '-fields';
            document.getElementById(selectedMethodId).classList.add('active');
        });
    });
    
    // Calculate button click event
    calculateBtn.addEventListener('click', calculateDueDate);
    
    // Recalculate button click event
    recalculateBtn.addEventListener('click', function() {
        resultsContainer.style.display = 'none';
    });
    
    /**
     * Calculate due date based on selected method
     */
    function calculateDueDate() {
        // Get selected calculation method
        const selectedMethod = document.querySelector('input[name="calculation-method"]:checked').value;
        
        // Reset error messages
        [lmpDateError, conceptionDateError, ivfDateError, ultrasoundDateError, knownDueDateError].forEach(error => {
            if (error) error.style.display = 'none';
        });
        
        let dueDate, lmpDate, conceptionDate;
        let isValid = true;
        
        // Calculate based on selected method
        switch (selectedMethod) {
            case 'lmp':
                // Last menstrual period method
                const lmpDateValue = lmpDateInput.value;
                const cycleLength = parseInt(document.getElementById('cycleLength').value);
                
                if (!lmpDateValue) {
                    lmpDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                lmpDate = new Date(lmpDateValue);
                
                // Calculate conception date (LMP + cycle length - 14 days)
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + (cycleLength - 14));
                
                // Calculate due date (LMP + 280 days, which is 40 weeks)
                dueDate = new Date(lmpDate);
                dueDate.setDate(lmpDate.getDate() + 280);
                break;
                
            case 'conception':
                // Conception date method
                const conceptionDateValue = conceptionDateInput.value;
                
                if (!conceptionDateValue) {
                    conceptionDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                conceptionDate = new Date(conceptionDateValue);
                
                // Calculate LMP date (conception date - cycle length + 14 days)
                // Assuming standard 28-day cycle
                lmpDate = new Date(conceptionDate);
                lmpDate.setDate(conceptionDate.getDate() - 14);
                
                // Calculate due date (conception date + 266 days, which is 38 weeks)
                dueDate = new Date(conceptionDate);
                dueDate.setDate(conceptionDate.getDate() + 266);
                break;
                
            case 'ivf':
                // IVF transfer method
                const ivfDateValue = ivfDateInput.value;
                const embryoAge = document.querySelector('input[name="embryoAge"]:checked').value;
                
                if (!ivfDateValue) {
                    ivfDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                const transferDate = new Date(ivfDateValue);
                
                // Calculate due date based on embryo age
                dueDate = new Date(transferDate);
                
                if (embryoAge === '3') {
                    // 3-day embryo: transfer date + 263 days
                    dueDate.setDate(transferDate.getDate() + 263);
                } else {
                    // 5-day embryo: transfer date + 261 days
                    dueDate.setDate(transferDate.getDate() + 261);
                }
                
                // Estimate conception date (transfer date - embryo age)
                conceptionDate = new Date(transferDate);
                conceptionDate.setDate(transferDate.getDate() - parseInt(embryoAge));
                
                // Estimate LMP date (conception date - 14 days)
                lmpDate = new Date(conceptionDate);
                lmpDate.setDate(conceptionDate.getDate() - 14);
                break;
                
            case 'ultrasound':
                // Ultrasound method
                const ultrasoundDateValue = ultrasoundDateInput.value;
                const gestationalWeeks = parseInt(document.getElementById('gestationalWeeks').value) || 0;
                const gestationalDays = parseInt(document.getElementById('gestationalDays').value) || 0;
                
                if (!ultrasoundDateValue) {
                    ultrasoundDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                const ultrasoundDate = new Date(ultrasoundDateValue);
                const totalGestationalDays = (gestationalWeeks * 7) + gestationalDays;
                
                // Calculate LMP date (ultrasound date - gestational age)
                lmpDate = new Date(ultrasoundDate);
                lmpDate.setDate(ultrasoundDate.getDate() - totalGestationalDays);
                
                // Calculate conception date (LMP + 14 days, assuming standard cycle)
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + 14);
                
                // Calculate due date (LMP + 280 days)
                dueDate = new Date(lmpDate);
                dueDate.setDate(lmpDate.getDate() + 280);
                break;
                
            case 'known':
                // Known due date method
                const knownDueDateValue = knownDueDateInput.value;
                
                if (!knownDueDateValue) {
                    knownDueDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                dueDate = new Date(knownDueDateValue);
                
                // Estimate LMP date (due date - 280 days)
                lmpDate = new Date(dueDate);
                lmpDate.setDate(dueDate.getDate() - 280);
                
                // Estimate conception date (LMP + 14 days, assuming standard cycle)
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + 14);
                break;
        }
        
        if (isValid) {
            displayResults(dueDate, lmpDate, conceptionDate);
        }
    }
    
    /**
     * Display the calculation results
     */
    function displayResults(dueDate, lmpDate, conceptionDate) {
        // Calculate other important dates
        const positiveTestDate = new Date(conceptionDate);
        positiveTestDate.setDate(conceptionDate.getDate() + 14);
        
        const heartbeatDate = new Date(conceptionDate);
        heartbeatDate.setDate(conceptionDate.getDate() + 42); // 6 weeks after conception
        
        const firstTrimesterEnd = new Date(lmpDate);
        firstTrimesterEnd.setDate(lmpDate.getDate() + 84); // 12 weeks after LMP
        
        const secondTrimesterEnd = new Date(lmpDate);
        secondTrimesterEnd.setDate(lmpDate.getDate() + 196); // 28 weeks after LMP
        
        // Update result elements
        document.getElementById('dueDateResult').textContent = formatDate(dueDate);
        document.getElementById('lmpDateDisplay').textContent = formatDate(lmpDate);
        document.getElementById('conceptionDateDisplay').textContent = formatDate(conceptionDate);
        document.getElementById('positiveTestDate').textContent = formatDate(positiveTestDate);
        document.getElementById('heartbeatDate').textContent = formatDate(heartbeatDate);
        document.getElementById('firstTrimesterEnd').textContent = formatDate(firstTrimesterEnd);
        document.getElementById('secondTrimesterEnd').textContent = formatDate(secondTrimesterEnd);
        document.getElementById('dueDateDisplay').textContent = formatDate(dueDate);
        
        // Show results
        resultsContainer.style.display = 'block';
    }
    
    /**
     * Format date for input elements (YYYY-MM-DD)
     */
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    /**
     * Format date for display (Month Day, Year)
     */
    function formatDate(date) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return date.toLocaleDateString('en-US', options);
    }
}); 