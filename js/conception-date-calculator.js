/**
 * 受孕日期计算器
 * 基于预产期计算受孕日期
 */

document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculateBtn');
    const recalculateBtn = document.getElementById('recalculateBtn');
    const dueDateInput = document.getElementById('dueDate');
    const resultsContainer = document.getElementById('results');
    const dueDateError = document.getElementById('dueDateError');

    // 设置日期输入的最小值和最大值
    const today = new Date();
    const minDate = new Date(today);
    minDate.setMonth(today.getMonth() - 9); // 9个月前
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() + 2); // 2年后
    
    dueDateInput.min = formatDateForInput(minDate);
    dueDateInput.max = formatDateForInput(maxDate);

    // 计算按钮点击事件
    calculateBtn.addEventListener('click', calculateConceptionDate);
    
    // 重新计算按钮点击事件
    recalculateBtn.addEventListener('click', function() {
        resultsContainer.style.display = 'none';
        dueDateInput.value = '';
    });

    /**
     * 计算受孕日期
     */
    function calculateConceptionDate() {
        const dueDate = dueDateInput.value;
        
        // 验证输入
        if (!dueDate) {
            dueDateError.style.display = 'block';
            return;
        }
        
        dueDateError.style.display = 'none';
        
        // 计算受孕日期（预产期 - 266天）
        const dueDateObj = new Date(dueDate);
        const conceptionDateObj = new Date(dueDateObj);
        conceptionDateObj.setDate(dueDateObj.getDate() - 266);
        
        // 计算其他重要日期
        const positiveTestDateObj = new Date(conceptionDateObj);
        positiveTestDateObj.setDate(conceptionDateObj.getDate() + 14);
        
        const heartbeatDateObj = new Date(conceptionDateObj);
        heartbeatDateObj.setDate(conceptionDateObj.getDate() + 42); // 6周后
        
        const firstTrimesterEndObj = new Date(conceptionDateObj);
        firstTrimesterEndObj.setDate(conceptionDateObj.getDate() + 84); // 12周后
        
        const secondTrimesterEndObj = new Date(conceptionDateObj);
        secondTrimesterEndObj.setDate(conceptionDateObj.getDate() + 182); // 26周后
        
        // 更新结果
        document.getElementById('conceptionDate').textContent = formatDate(conceptionDateObj);
        document.getElementById('conceptionDateDisplay').textContent = formatDate(conceptionDateObj);
        document.getElementById('positiveTestDate').textContent = formatDate(positiveTestDateObj);
        document.getElementById('heartbeatDate').textContent = formatDate(heartbeatDateObj);
        document.getElementById('firstTrimesterEnd').textContent = formatDate(firstTrimesterEndObj);
        document.getElementById('secondTrimesterEnd').textContent = formatDate(secondTrimesterEndObj);
        document.getElementById('dueDateDisplay').textContent = formatDate(dueDateObj);
        
        // 显示结果
        resultsContainer.style.display = 'block';
    }

    /**
     * 格式化日期为YYYY-MM-DD（用于input元素）
     */
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    /**
     * 格式化日期为人类可读格式
     */
    function formatDate(date) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return date.toLocaleDateString('zh-CN', options);
    }
}); 