/**
 * 孕期体重计算器
 * 基于BMI的体重增长建议计算
 */

// 输入验证函数
function validateInput(value, min, max, fieldName) {
    if (value === '' || isNaN(value)) {
        throw new Error(`请输入${fieldName}`);
    }
    if (value < min || value > max) {
        throw new Error(`${fieldName}必须在${min}到${max}之间`);
    }
    return true;
}

function calculate() {
    try {
        // 获取并验证输入值
        const preWeight = parseFloat(document.getElementById('preWeight').value);
        const currentWeight = parseFloat(document.getElementById('currentWeight').value);
        const height = parseFloat(document.getElementById('height').value);
        const week = parseInt(document.getElementById('week').value);
        const isTwins = document.getElementById('twins').checked;

        // 验证每个输入值
        validateInput(preWeight, 30, 200, '孕前体重');
        validateInput(currentWeight, 30, 200, '当前体重');
        validateInput(height, 140, 200, '身高');
        validateInput(week, 1, 42, '孕周');

        // 额外的逻辑验证
        if (currentWeight < preWeight - 10) {
            throw new Error('当前体重不应该比孕前体重小太多，请检查输入是否正确');
        }
        if (currentWeight > preWeight + 50) {
            throw new Error('体重增长似乎过大，请检查输入是否正确');
        }

        // 计算BMI (体重kg / 身高m的平方)
        const heightInMeters = height / 100;
        const bmi = preWeight / (heightInMeters * heightInMeters);

        // 确定BMI分类和建议体重增长范围
        let category, weightRange;
        if (isTwins) {
            if (bmi < 18.5) {
                category = '偏瘦';
                weightRange = [20, 28]; // 建议增加范围更大
            } else if (bmi >= 18.5 && bmi < 25) {
                category = '正常';
                weightRange = [17, 25];
            } else if (bmi >= 25 && bmi < 30) {
                category = '超重';
                weightRange = [14, 23];
            } else {
                category = '肥胖';
                weightRange = [11, 19];
            }
        } else {
            if (bmi < 18.5) {
                category = '偏瘦';
                weightRange = [12.5, 18];
            } else if (bmi >= 18.5 && bmi < 25) {
                category = '正常';
                weightRange = [11.5, 16];
            } else if (bmi >= 25 && bmi < 30) {
                category = '超重';
                weightRange = [7, 11.5];
            } else {
                category = '肥胖';
                weightRange = [5, 9];
            }
        }

        // 计算当前体重增长
        const currentGain = currentWeight - preWeight;

        // 根据孕周计算建议的当前体重增长
        // 前12周增重较少，之后每周增重0.35-0.5kg
        let expectedGainRange;
        if (week <= 12) {
            expectedGainRange = [0, 2]; // 前12周建议增重0-2kg
        } else {
            const additionalWeeks = week - 12;
            const minExpectedGain = 1 + (additionalWeeks * 0.35);
            const maxExpectedGain = 2 + (additionalWeeks * 0.5);
            expectedGainRange = [minExpectedGain, maxExpectedGain];
        }

        // 评估体重增长状态
        let status;
        if (currentGain < expectedGainRange[0]) {
            status = '体重增长偏少，建议适当增加营养摄入';
        } else if (currentGain > expectedGainRange[1]) {
            status = '体重增长偏快，建议注意控制';
        } else {
            status = '体重增长正常，继续保持';
        }

        // 显示结果
        document.getElementById('result').style.display = 'block';
        document.getElementById('bmi').textContent = bmi.toFixed(1);
        document.getElementById('bmiCategory').textContent = category;
        document.getElementById('weightRange').textContent = `${weightRange[0]}-${weightRange[1]}公斤`;
        document.getElementById('currentGain').textContent = `${currentGain.toFixed(1)}公斤`;
        document.getElementById('status').textContent = status;

    } catch (error) {
        alert(error.message);
        document.getElementById('result').style.display = 'none';
    }
}

// 添加实时输入验证
document.addEventListener('DOMContentLoaded', function() {
    const inputs = document.querySelectorAll('input[type="number"]');
    
    // 为每个数字输入框添加验证
    inputs.forEach(input => {
        // 添加失去焦点时的验证
        input.addEventListener('blur', function() {
            const value = this.value.trim();
            if (value === '') return; // 允许空值，但在计算时会检查

            const numValue = parseFloat(value);
            const min = parseFloat(this.min);
            const max = parseFloat(this.max);
            
            if (isNaN(numValue)) {
                alert('请输入有效的数字');
                this.value = '';
                return;
            }
            
            if (numValue < min) {
                alert(`数值不能小于 ${min}`);
                this.value = min;
            } else if (numValue > max) {
                alert(`数值不能大于 ${max}`);
                this.value = max;
            }
        });

        // 添加输入时的格式验证
        input.addEventListener('input', function() {
            // 移除非数字字符（保留小数点）
            this.value = this.value.replace(/[^\d.]/g, '');
            // 确保只有一个小数点
            const parts = this.value.split('.');
            if (parts.length > 2) {
                this.value = parts[0] + '.' + parts.slice(1).join('');
            }
            // 限制小数位数为1位
            if (parts[1] && parts[1].length > 1) {
                this.value = parts[0] + '.' + parts[1].slice(0, 1);
            }
        });
    });

    // 为孕周输入框添加特殊处理（只允许整数）
    const weekInput = document.getElementById('week');
    if (weekInput) {
        weekInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^\d]/g, ''); // 只允许数字
            const value = parseInt(this.value);
            if (value > 42) this.value = 42;
        });
    }
}); 