/**
 * 预产期计算器
 * 实现五种不同的计算方法：
 * 1. 末次月经日期 - 从末次月经开始计算40周
 * 2. 受孕日期 - 从受孕日期开始计算38周
 * 3. 试管婴儿移植 - 从5天胚胎移植开始计算261天或从3天胚胎移植开始计算263天
 * 4. 超声检查 - 基于超声检查确定的孕周
 * 5. 已知预产期 - 直接输入预产期
 */

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const calculationMethods = document.querySelectorAll('input[name="calculation-method"]');
    const methodFields = document.querySelectorAll('.method-fields');
    const calculateBtn = document.getElementById('calculateBtn');
    const recalculateBtn = document.getElementById('recalculateBtn');
    const resultsContainer = document.getElementById('results');
    
    // 日期输入
    const lmpDateInput = document.getElementById('lmpDate');
    const conceptionDateInput = document.getElementById('conceptionDate');
    const ivfDateInput = document.getElementById('ivfDate');
    const ultrasoundDateInput = document.getElementById('ultrasoundDate');
    const knownDueDateInput = document.getElementById('knownDueDate');
    
    // 错误信息
    const lmpDateError = document.getElementById('lmpDateError');
    const conceptionDateError = document.getElementById('conceptionDateError');
    const ivfDateError = document.getElementById('ivfDateError');
    const ultrasoundDateError = document.getElementById('ultrasoundDateError');
    const knownDueDateError = document.getElementById('knownDueDateError');
    
    // 设置默认日期
    const today = new Date();
    
    // 设置日期输入的最小值和最大值
    const minDate = new Date(today);
    minDate.setFullYear(today.getFullYear() - 1); // 1年前
    
    const maxDate = new Date(today);
    maxDate.setFullYear(today.getFullYear() + 2); // 2年后
    
    // 设置日期输入的最小值和最大值
    [lmpDateInput, conceptionDateInput, ivfDateInput, ultrasoundDateInput, knownDueDateInput].forEach(input => {
        if (input) {
            input.min = formatDateForInput(minDate);
            input.max = formatDateForInput(maxDate);
        }
    });
    
    // 切换计算方法
    calculationMethods.forEach(method => {
        method.addEventListener('change', function() {
            // 隐藏所有方法字段
            methodFields.forEach(field => {
                field.classList.remove('active');
            });
            
            // 显示选中的方法字段
            const selectedMethodId = this.value + '-fields';
            document.getElementById(selectedMethodId).classList.add('active');
        });
    });
    
    // 计算按钮点击事件
    calculateBtn.addEventListener('click', calculateDueDate);
    
    // 重新计算按钮点击事件
    recalculateBtn.addEventListener('click', function() {
        resultsContainer.style.display = 'none';
    });
    
    /**
     * 根据选择的方法计算预产期
     */
    function calculateDueDate() {
        // 获取选中的计算方法
        const selectedMethod = document.querySelector('input[name="calculation-method"]:checked').value;
        
        // 重置错误信息
        [lmpDateError, conceptionDateError, ivfDateError, ultrasoundDateError, knownDueDateError].forEach(error => {
            if (error) error.style.display = 'none';
        });
        
        let dueDate, lmpDate, conceptionDate;
        let isValid = true;
        
        // 根据选择的方法计算
        switch (selectedMethod) {
            case 'lmp':
                // 末次月经日期法
                const lmpDateValue = lmpDateInput.value;
                const cycleLength = parseInt(document.getElementById('cycleLength').value);
                
                if (!lmpDateValue) {
                    lmpDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                lmpDate = new Date(lmpDateValue);
                
                // 计算受孕日期（末次月经 + 周期长度 - 14天）
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + (cycleLength - 14));
                
                // 计算预产期（末次月经 + 280天，即40周）
                dueDate = new Date(lmpDate);
                dueDate.setDate(lmpDate.getDate() + 280);
                break;
                
            case 'conception':
                // 受孕日期法
                const conceptionDateValue = conceptionDateInput.value;
                
                if (!conceptionDateValue) {
                    conceptionDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                conceptionDate = new Date(conceptionDateValue);
                
                // 计算末次月经日期（受孕日期 - 周期长度 + 14天）
                // 假设标准28天周期
                lmpDate = new Date(conceptionDate);
                lmpDate.setDate(conceptionDate.getDate() - 14);
                
                // 计算预产期（受孕日期 + 266天，即38周）
                dueDate = new Date(conceptionDate);
                dueDate.setDate(conceptionDate.getDate() + 266);
                break;
                
            case 'ivf':
                // 试管婴儿移植法
                const ivfDateValue = ivfDateInput.value;
                const embryoAge = document.querySelector('input[name="embryoAge"]:checked').value;
                
                if (!ivfDateValue) {
                    ivfDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                const transferDate = new Date(ivfDateValue);
                
                // 根据胚胎年龄计算预产期
                dueDate = new Date(transferDate);
                
                if (embryoAge === '3') {
                    // 3天胚胎：移植日期 + 263天
                    dueDate.setDate(transferDate.getDate() + 263);
                } else {
                    // 5天胚胎：移植日期 + 261天
                    dueDate.setDate(transferDate.getDate() + 261);
                }
                
                // 估算受孕日期（移植日期 - 胚胎年龄）
                conceptionDate = new Date(transferDate);
                conceptionDate.setDate(transferDate.getDate() - parseInt(embryoAge));
                
                // 估算末次月经日期（受孕日期 - 14天）
                lmpDate = new Date(conceptionDate);
                lmpDate.setDate(conceptionDate.getDate() - 14);
                break;
                
            case 'ultrasound':
                // 超声检查法
                const ultrasoundDateValue = ultrasoundDateInput.value;
                const gestationalWeeks = parseInt(document.getElementById('gestationalWeeks').value) || 0;
                const gestationalDays = parseInt(document.getElementById('gestationalDays').value) || 0;
                
                if (!ultrasoundDateValue) {
                    ultrasoundDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                const ultrasoundDate = new Date(ultrasoundDateValue);
                const totalGestationalDays = (gestationalWeeks * 7) + gestationalDays;
                
                // 计算末次月经日期（超声检查日期 - 孕周天数）
                lmpDate = new Date(ultrasoundDate);
                lmpDate.setDate(ultrasoundDate.getDate() - totalGestationalDays);
                
                // 计算受孕日期（末次月经 + 14天，假设标准周期）
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + 14);
                
                // 计算预产期（末次月经 + 280天）
                dueDate = new Date(lmpDate);
                dueDate.setDate(lmpDate.getDate() + 280);
                break;
                
            case 'known':
                // 已知预产期法
                const knownDueDateValue = knownDueDateInput.value;
                
                if (!knownDueDateValue) {
                    knownDueDateError.style.display = 'block';
                    isValid = false;
                    break;
                }
                
                dueDate = new Date(knownDueDateValue);
                
                // 估算末次月经日期（预产期 - 280天）
                lmpDate = new Date(dueDate);
                lmpDate.setDate(dueDate.getDate() - 280);
                
                // 估算受孕日期（末次月经 + 14天，假设标准周期）
                conceptionDate = new Date(lmpDate);
                conceptionDate.setDate(lmpDate.getDate() + 14);
                break;
        }
        
        if (isValid) {
            displayResults(dueDate, lmpDate, conceptionDate);
        }
    }
    
    /**
     * 显示计算结果
     */
    function displayResults(dueDate, lmpDate, conceptionDate) {
        // 计算其他重要日期
        const positiveTestDate = new Date(conceptionDate);
        positiveTestDate.setDate(conceptionDate.getDate() + 14);
        
        const heartbeatDate = new Date(conceptionDate);
        heartbeatDate.setDate(conceptionDate.getDate() + 42); // 受孕后6周
        
        const firstTrimesterEnd = new Date(lmpDate);
        firstTrimesterEnd.setDate(lmpDate.getDate() + 84); // 末次月经后12周
        
        const secondTrimesterEnd = new Date(lmpDate);
        secondTrimesterEnd.setDate(lmpDate.getDate() + 196); // 末次月经后28周
        
        // 更新结果元素
        document.getElementById('dueDateResult').textContent = formatDate(dueDate);
        document.getElementById('lmpDateDisplay').textContent = formatDate(lmpDate);
        document.getElementById('conceptionDateDisplay').textContent = formatDate(conceptionDate);
        document.getElementById('positiveTestDate').textContent = formatDate(positiveTestDate);
        document.getElementById('heartbeatDate').textContent = formatDate(heartbeatDate);
        document.getElementById('firstTrimesterEnd').textContent = formatDate(firstTrimesterEnd);
        document.getElementById('secondTrimesterEnd').textContent = formatDate(secondTrimesterEnd);
        document.getElementById('dueDateDisplay').textContent = formatDate(dueDate);
        
        // 显示结果
        resultsContainer.style.display = 'block';
    }
    
    /**
     * 格式化日期为YYYY-MM-DD（用于input元素）
     */
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    /**
     * 格式化日期为人类可读格式
     */
    function formatDate(date) {
        const options = { year: 'numeric', month: 'long', day: 'numeric' };
        return date.toLocaleDateString('zh-CN', options);
    }
});