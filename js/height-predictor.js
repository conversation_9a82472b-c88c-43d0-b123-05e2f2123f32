/**
 * 儿童成年身高预测计算器 - 基于中亲身高法（鼓励型）
 * 
 * 特点：
 * - 使用中亲身高法作为基础
 * - 结合孩子当前身高与同龄平均身高的比例
 * - 偏"鼓励型"预测，结果稍高于中性预测
 */

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 填充年龄选择器
    const ageSelect = document.getElementById('age');
    const ages = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17];
    ages.forEach(age => {
        const option = document.createElement('option');
        option.value = age;
        option.textContent = age + '岁';
        ageSelect.appendChild(option);
    });

    // 表单提交处理
    document.getElementById('heightCalculator').addEventListener('submit', function(e) {
        e.preventDefault();
        calculateHeight();
    });
});

/**
 * 计算预测身高
 * 使用中亲身高法结合当前身高比例的鼓励型预测
 */
function calculateHeight() {
    try {
        // 获取输入值
        const sex = document.querySelector('input[name="sex"]:checked').value;
        const age = parseInt(document.getElementById('age').value);
        const childHeight = parseFloat(document.getElementById('height').value); // 厘米
        const motherHeight = parseFloat(document.getElementById('motherHeight').value); // 厘米
        const fatherHeight = parseFloat(document.getElementById('fatherHeight').value); // 厘米

        // 验证输入
        if (!sex || isNaN(age) || isNaN(childHeight) || isNaN(motherHeight) || isNaN(fatherHeight)) {
            alert('请填写所有必填项');
            return;
        }

        // 1. 计算中亲身高预测值
        let midParentHeight;
        if (sex === 'male') {
            // 男孩: (父亲身高 + 母亲身高 + 13) / 2
            midParentHeight = (fatherHeight + motherHeight + 13) / 2;
        } else {
            // 女孩: (父亲身高 + 母亲身高 - 13) / 2
            midParentHeight = (fatherHeight + motherHeight - 13) / 2;
        }

        // 2. 获取同龄儿童平均身高数据
        const avgHeight = getAverageHeight(sex, age);
        
        // 3. 计算当前身高与平均身高的比例
        const heightRatio = childHeight / avgHeight;
        
        // 4. 应用比例调整中亲身高预测
        let adjustedHeight = midParentHeight * heightRatio;
        
        // 5. 添加鼓励型调整（+3~7cm）
        // 根据比例决定增加多少，比例越高增加越少
        const encouragementBoost = 7 - Math.min(4, (heightRatio - 1) * 10);
        adjustedHeight += Math.max(3, encouragementBoost);
        
        // 6. 确保预测值在合理范围内
        // 男孩不超过父亲身高+10cm，女孩不超过母亲身高+15cm
        if (sex === 'male') {
            adjustedHeight = Math.min(adjustedHeight, fatherHeight + 10);
        } else {
            adjustedHeight = Math.min(adjustedHeight, motherHeight + 15);
        }

        // 显示结果
        const resultsDiv = document.getElementById('results');
        const predictionSpan = document.getElementById('prediction');
        const accuracySpan = document.getElementById('accuracy');

        // 四舍五入到一位小数
        const roundedHeight = Math.round(adjustedHeight * 10) / 10;

        // 判断趋势
        let trend = '';
        if (heightRatio > 1.05) {
            trend = '，属于偏高趋势';
        } else if (heightRatio < 0.95) {
            trend = '，属于偏低趋势，但有良好的成长空间';
        } else {
            trend = '，属于正常趋势';
        }

        predictionSpan.textContent = roundedHeight + ' 厘米' + trend;
        
        // 显示预测准确度说明
        accuracySpan.textContent = '该预测考虑了遗传因素和当前生长趋势，仅供参考';
        
        resultsDiv.style.display = 'block';
        
    } catch (error) {
        console.error('计算错误:', error);
        alert('计算出错，请检查输入值');
    }
}

/**
 * 获取特定年龄和性别的平均身高
 * 数据来源：WHO生长标准和中国儿童青少年生长参考数据
 * @param {string} sex - 'male'或'female'
 * @param {number} age - 年龄
 * @returns {number} 平均身高（厘米）
 */
function getAverageHeight(sex, age) {
    // 中国儿童青少年平均身高参考值（厘米）
    const averageHeights = {
        male: {
            4: 105.5,
            5: 112.0,
            6: 118.5,
            7: 124.5,
            8: 130.0,
            9: 135.5,
            10: 140.5,
            11: 146.0,
            12: 152.0,
            13: 159.0,
            14: 165.5,
            15: 170.5,
            16: 173.5,
            17: 174.5
        },
        female: {
            4: 104.0,
            5: 110.5,
            6: 117.0,
            7: 123.0,
            8: 128.5,
            9: 134.0,
            10: 140.0,
            11: 146.5,
            12: 152.0,
            13: 156.0,
            14: 158.5,
            15: 160.0,
            16: 160.5,
            17: 161.0
        }
    };
    
    return averageHeights[sex][age];
} 