// 计算器核心功能
class GrowthCalculator {
    constructor() {
        console.log('Initializing GrowthCalculator...');
        this.form = document.querySelector('.calculator-form');
        this.resultsContainer = document.querySelector('.results-container');
        this.calculateButton = document.querySelector('.calculate-button');
        this.charts = {
            weight: null,
            height: null,
            head: null
        };

        if (!this.form || !this.resultsContainer || !this.calculateButton) {
            console.error('Required elements not found:', {
                form: !!this.form,
                resultsContainer: !!this.resultsContainer,
                calculateButton: !!this.calculateButton
            });
            return;
        }

        this.bindEvents();
        this.setupFormValidation();
        console.log('GrowthCalculator initialized successfully');
    }

    bindEvents() {
        console.log('Binding events...');
        
        // 表单提交事件
        this.form.addEventListener('submit', (e) => {
            console.log('Form submitted');
            e.preventDefault();
            this.calculateResults();
        });

        // 日期输入和验证
        const birthDate = document.getElementById('birthDate');
        const measurementDate = document.getElementById('measurementDate');
        const dateError = document.querySelector('.date-error-message');

        // 日历按钮点击事件
        const datePickerButtons = document.querySelectorAll('.date-picker-button');
        datePickerButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const dateInput = e.target.closest('.date-input-container').querySelector('input[type="date"]');
                dateInput.showPicker();
            });
        });

        // 日期验证
        [birthDate, measurementDate].forEach(input => {
            if (!input) {
                console.error(`Date input not found: ${input.id}`);
                return;
            }
            
            input.addEventListener('change', () => {
                console.log(`Date changed: ${input.id} = ${input.value}`);
                if (birthDate.value && measurementDate.value) {
                    const age = this.calculateAgeInMonths(birthDate.value, measurementDate.value);
                    console.log(`Calculated age: ${age} months`);
                    if (age < 0 || age > 24) {
                        dateError.style.display = 'block';
                        measurementDate.setCustomValidity('Date must be between birth and second birthday');
                    } else {
                        dateError.style.display = 'none';
                        measurementDate.setCustomValidity('');
                    }
                }
                this.validateForm();
            });
        });

        // 监听所有输入字段的变化
        this.form.querySelectorAll('input').forEach(input => {
            input.addEventListener('input', () => {
                console.log(`Input changed: ${input.id} = ${input.value}`);
                this.validateForm();
            });
        });

        console.log('Events bound successfully');
    }

    setupFormValidation() {
        // 设置日期输入的最大值为今天
        const today = new Date().toISOString().split('T')[0];
        const dateInputs = this.form.querySelectorAll('input[type="date"]');
        dateInputs.forEach(input => {
            input.max = today;
        });

        // 初始验证
        this.validateForm();
    }

    validateForm() {
        console.log('Validating form...');
        const requiredInputs = this.form.querySelectorAll('input[required]');
        const dateError = document.querySelector('.date-error-message');
        let isValid = true;

        // 检查所有必填字段
        requiredInputs.forEach(input => {
            if (!input.value) {
                console.log(`Required field empty: ${input.id}`);
                isValid = false;
            }
        });

        // 检查性别选择
        const genderSelected = this.form.querySelector('input[name="gender"]:checked');
        if (!genderSelected) {
            console.log('No gender selected');
            isValid = false;
        }

        // 检查日期错误
        if (dateError.style.display === 'block') {
            console.log('Date error displayed');
            isValid = false;
        }

        console.log(`Form validation result: ${isValid}`);
        this.calculateButton.disabled = !isValid;
    }

    calculateAgeInMonths(birthDate, measurementDate) {
        const birth = new Date(birthDate);
        const measurement = new Date(measurementDate);
        const diffTime = Math.abs(measurement - birth);
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return Math.floor(diffDays / 30.44); // 平均每月天数
    }

    calculatePercentile(value, standardData) {
        try {
            if (!standardData || !Array.isArray(standardData) || standardData.length !== 5) {
                console.error('Invalid standard data for percentile calculation');
                return null;
            }

            value = Number(value);
            if (isNaN(value)) {
                console.error('Invalid value for percentile calculation');
                return null;
            }

            // 如果值小于最小标准值
            if (value <= standardData[0]) {
                return 3;
            }

            // 如果值大于最大标准值
            if (value > standardData[4]) {
                return 97;
            }

            // 在标准值之间进行插值
            for (let i = 0; i < standardData.length - 1; i++) {
                if (value > standardData[i] && value <= standardData[i + 1]) {
                    const percentiles = [3, 15, 50, 85, 97];
                    const lowerPercentile = percentiles[i];
                    const upperPercentile = percentiles[i + 1];
                    
                    // 线性插值计算具体百分位数
                    const ratio = (value - standardData[i]) / (standardData[i + 1] - standardData[i]);
                    const interpolatedPercentile = lowerPercentile + (upperPercentile - lowerPercentile) * ratio;
                    
                    return Math.round(interpolatedPercentile); // 四舍五入到最接近的整数
                }
            }

            // 如果没有找到合适的区间（不应该发生）
            console.error('Unable to calculate percentile');
            return null;
        } catch (error) {
            console.error('Error in calculatePercentile:', error);
            return null;
        }
    }

    interpolateData(age, data) {
        try {
            const ages = Object.keys(data).map(Number).sort((a, b) => a - b);
            
            // 如果年龄小于最小年龄，使用最小年龄的数据
            if (age <= ages[0]) {
                return data[ages[0]];
            }
            
            // 如果年龄大于最大年龄，使用最大年龄的数据
            if (age >= ages[ages.length - 1]) {
                return data[ages[ages.length - 1]];
            }
            
            // 找到年龄所在的区间
            let lowerAge = ages[0];
            let upperAge = ages[ages.length - 1];
            
            for (let i = 0; i < ages.length - 1; i++) {
                if (age >= ages[i] && age <= ages[i + 1]) {
                    lowerAge = ages[i];
                    upperAge = ages[i + 1];
                    break;
                }
            }
            
            // 计算插值比例
            const ratio = (age - lowerAge) / (upperAge - lowerAge);
            const lowerData = data[lowerAge];
            const upperData = data[upperAge];
            
            // 确保数据存在
            if (!lowerData || !upperData) {
                console.error('Missing data for interpolation');
                return null;
            }
            
            // 线性插值
            return lowerData.map((val, i) => {
                const interpolated = val + (upperData[i] - val) * ratio;
                return Number(interpolated.toFixed(2)); // 保留两位小数
            });
        } catch (error) {
            console.error('Error in interpolateData:', error);
            return null;
        }
    }

    formatPercentile(percentile) {
        if (percentile === null) {
            return '无法计算';
        }
        if (percentile === 3) {
            return '低于第3百分位';
        }
        if (percentile === 97) {
            return '高于第97百分位';
        }
        return `第${percentile}百分位`;
    }

    calculateResults() {
        console.log('Calculating results...');
        try {
            const genderInput = document.querySelector('input[name="gender"]:checked');
            if (!genderInput) {
                console.error('No gender selected');
                return;
            }
            
            // 将性别值转换为复数形式以匹配 WHO_DATA 的键名
            const gender = genderInput.value === 'boy' ? 'boys' : 'girls';
            console.log('Gender (normalized):', gender);

            const birthDate = document.getElementById('birthDate')?.value;
            const measurementDate = document.getElementById('measurementDate')?.value;
            const weight = document.getElementById('weight')?.value; // 直接使用千克
            const height = document.getElementById('height')?.value; // 直接使用厘米
            const headCircumference = document.getElementById('headCircumference')?.value;

            console.log('Input values:', {
                gender,
                birthDate,
                measurementDate,
                weight,
                height,
                headCircumference
            });

            // 验证输入
            if (!birthDate || !measurementDate || !weight || !height || !headCircumference) {
                console.error('Missing required inputs');
                return;
            }

            const ageInMonths = this.calculateAgeInMonths(birthDate, measurementDate);
            console.log('Age in months:', ageInMonths);
            
            if (ageInMonths < 0 || ageInMonths > 24) {
                console.error('Age must be between 0 and 24 months');
                return;
            }

            // 转换为数值类型
            const weightKg = parseFloat(weight);
            const heightCm = parseFloat(height);
            const headCm = parseFloat(headCircumference);

            console.log('Measurements:', { weightKg, heightCm, headCm });

            // 验证 WHO_DATA 数据
            if (!WHO_DATA || !WHO_DATA[gender]) {
                console.error('Invalid WHO_DATA:', WHO_DATA);
                return;
            }

            const standardData = WHO_DATA[gender];
            console.log('Using standard data for gender:', gender);
            
            // 计算各项百分位数
            console.log('Calculating standards...');
            const weightStandards = this.interpolateData(ageInMonths, standardData.weight);
            const heightStandards = this.interpolateData(ageInMonths, standardData.length);
            const headStandards = this.interpolateData(ageInMonths, standardData.headCircumference);

            console.log('Standards calculated:', {
                weightStandards,
                heightStandards,
                headStandards
            });

            // 验证标准数据
            if (!weightStandards || !heightStandards || !headStandards) {
                console.error('Failed to interpolate standard data');
                return;
            }

            console.log('Calculating percentiles...');
            const weightPercentile = this.calculatePercentile(weightKg, weightStandards);
            const heightPercentile = this.calculatePercentile(heightCm, heightStandards);
            const headPercentile = this.calculatePercentile(headCm, headStandards);

            console.log('Percentiles calculated:', {
                weightPercentile,
                heightPercentile,
                headPercentile
            });

            // 显示结果
            document.querySelector('.weight-percentile').textContent = this.formatPercentile(weightPercentile);
            document.querySelector('.height-percentile').textContent = this.formatPercentile(heightPercentile);
            document.querySelector('.head-percentile').textContent = this.formatPercentile(headPercentile);

            // 显示结果容器
            this.resultsContainer.style.display = 'block';

            // 绘制图表
            console.log('Drawing charts...');
            this.drawCharts(ageInMonths, {
                weight: { value: weightKg, standards: weightStandards, label: '体重 (千克)' },
                height: { value: heightCm, standards: heightStandards, label: '身高 (厘米)' },
                head: { value: headCm, standards: headStandards, label: '头围 (厘米)' }
            });

            // 滚动到结果
            this.resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
            console.log('Results displayed successfully');
        } catch (error) {
            console.error('Error calculating results:', error);
        }
    }

    drawCharts(age, measurements) {
        const chartConfig = (data, label) => ({
            type: 'line',
            data: {
                labels: ['3rd', '15th', '50th', '85th', '97th'],
                datasets: [
                    {
                        label: 'Standards',
                        data: data.standards,
                        borderColor: 'rgba(54, 162, 235, 0.5)',
                        backgroundColor: 'rgba(54, 162, 235, 0.1)',
                        fill: true,
                        tension: 0.4
                    },
                    {
                        label: 'Your child',
                        data: Array(5).fill(data.value),
                        borderColor: 'rgba(255, 99, 132, 1)',
                        borderWidth: 2,
                        pointRadius: 6,
                        pointBackgroundColor: 'rgba(255, 99, 132, 1)',
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: label,
                            font: {
                                weight: 'bold'
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Percentile',
                            font: {
                                weight: 'bold'
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    title: {
                        display: true,
                        text: `${label} at ${age} months`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    }
                }
            }
        });

        // 销毁旧图表
        Object.keys(this.charts).forEach(type => {
            if (this.charts[type]) {
                this.charts[type].destroy();
            }
        });

        // 创建新图表
        this.charts.weight = new Chart(
            document.getElementById('weightChart'),
            chartConfig(measurements.weight, measurements.weight.label)
        );
        this.charts.height = new Chart(
            document.getElementById('heightChart'),
            chartConfig(measurements.height, measurements.height.label)
        );
        this.charts.head = new Chart(
            document.getElementById('headChart'),
            chartConfig(measurements.head, measurements.head.label)
        );
    }
}

// 当 DOM 加载完成后初始化计算器
document.addEventListener('DOMContentLoaded', () => {
    // 确保 WHO_DATA 已加载
    if (typeof WHO_DATA === 'undefined') {
        console.error('WHO_DATA is not loaded. Please check who-data.js file.');
        return;
    }
    
    // 初始化计算器
    window.calculator = new GrowthCalculator();
}); 