// WHO生长标准数据
const WHO_DATA = {
    boys: {
        weight: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（千克）
            0: [2.5, 2.9, 3.3, 3.7, 4.0],
            1: [3.4, 3.9, 4.5, 5.1, 5.5],
            2: [4.3, 4.9, 5.6, 6.3, 6.8],
            3: [5.0, 5.7, 6.4, 7.2, 7.8],
            4: [5.6, 6.2, 7.0, 7.8, 8.4],
            5: [6.0, 6.7, 7.5, 8.4, 9.0],
            6: [6.4, 7.1, 7.9, 8.8, 9.5],
            12: [8.0, 8.8, 9.6, 10.5, 11.3],
            18: [9.1, 9.9, 10.9, 12.0, 12.9],
            24: [10.0, 10.8, 12.0, 13.2, 14.2]
        },
        length: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（厘米）
            0: [46.8, 48.0, 49.9, 51.8, 53.0],
            1: [51.1, 52.3, 54.3, 56.3, 57.5],
            2: [54.7, 56.0, 58.1, 60.2, 61.5],
            3: [57.7, 59.0, 61.2, 63.4, 64.7],
            4: [60.3, 61.7, 63.9, 66.1, 67.5],
            5: [62.4, 63.9, 66.2, 68.5, 69.9],
            6: [64.3, 65.8, 68.1, 70.4, 71.9],
            12: [71.3, 72.9, 75.5, 78.1, 79.7],
            18: [76.2, 77.9, 80.7, 83.5, 85.2],
            24: [80.3, 82.1, 85.0, 87.9, 89.7]
        },
        headCircumference: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（厘米）
            0: [32.4, 33.2, 34.5, 35.8, 36.6],
            1: [35.2, 36.0, 37.3, 38.6, 39.4],
            2: [37.0, 37.8, 39.1, 40.4, 41.2],
            3: [38.3, 39.1, 40.4, 41.7, 42.5],
            4: [39.4, 40.2, 41.5, 42.8, 43.6],
            5: [40.3, 41.1, 42.4, 43.7, 44.5],
            6: [41.1, 41.9, 43.2, 44.5, 45.3],
            12: [43.5, 44.3, 45.6, 46.9, 47.7],
            18: [44.8, 45.6, 46.9, 48.2, 49.0],
            24: [45.7, 46.5, 47.8, 49.1, 49.9]
        }
    },
    girls: {
        weight: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（千克）
            0: [2.4, 2.8, 3.2, 3.6, 3.9],
            1: [3.2, 3.6, 4.2, 4.8, 5.2],
            2: [4.0, 4.5, 5.1, 5.8, 6.3],
            3: [4.6, 5.2, 5.8, 6.6, 7.2],
            4: [5.1, 5.7, 6.4, 7.2, 7.8],
            5: [5.5, 6.1, 6.9, 7.7, 8.3],
            6: [5.8, 6.5, 7.3, 8.1, 8.8],
            12: [7.3, 8.0, 8.9, 9.8, 10.6],
            18: [8.4, 9.2, 10.2, 11.3, 12.2],
            24: [9.3, 10.1, 11.3, 12.5, 13.5]
        },
        length: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（厘米）
            0: [46.1, 47.2, 49.1, 51.0, 52.1],
            1: [50.3, 51.4, 53.4, 55.4, 56.5],
            2: [53.7, 54.9, 56.9, 58.9, 60.1],
            3: [56.5, 57.8, 59.9, 62.0, 63.3],
            4: [59.0, 60.3, 62.4, 64.5, 65.8],
            5: [61.1, 62.4, 64.6, 66.8, 68.1],
            6: [62.9, 64.3, 66.5, 68.7, 70.1],
            12: [69.8, 71.3, 73.8, 76.3, 77.8],
            18: [74.6, 76.2, 78.9, 81.6, 83.2],
            24: [78.6, 80.3, 83.2, 86.1, 87.8]
        },
        headCircumference: {
            // 月龄: [3rd, 15th, 50th, 85th, 97th] 百分位数值（厘米）
            0: [31.9, 32.7, 33.9, 35.1, 35.9],
            1: [34.6, 35.4, 36.6, 37.8, 38.6],
            2: [36.3, 37.1, 38.3, 39.5, 40.3],
            3: [37.5, 38.3, 39.5, 40.7, 41.5],
            4: [38.6, 39.4, 40.6, 41.8, 42.6],
            5: [39.4, 40.2, 41.4, 42.6, 43.4],
            6: [40.2, 41.0, 42.2, 43.4, 44.2],
            12: [42.4, 43.2, 44.4, 45.6, 46.4],
            18: [43.8, 44.6, 45.8, 47.0, 47.8],
            24: [44.7, 45.5, 46.7, 47.9, 48.7]
        }
    }
}; 