document.addEventListener('DOMContentLoaded', function() {
    // 填充年龄选择器
    const motherAgeSelect = document.getElementById('motherAge');
    for (let age = 18; age <= 45; age++) {
        const option = document.createElement('option');
        option.value = age;
        option.textContent = age;
        motherAgeSelect.appendChild(option);
    }

    // 表单提交处理
    const form = document.getElementById('genderCalculator');
    const resultsContainer = document.getElementById('results');
    const predictionElement = document.getElementById('prediction');
    const genderIconElement = document.getElementById('genderIcon');
    const genderResultElement = document.getElementById('genderResult');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 获取表单值
        const motherAge = parseInt(document.getElementById('motherAge').value);
        const conceptionMonth = parseInt(document.getElementById('conceptionMonth').value);
        
        // 验证表单
        if (isNaN(motherAge) || isNaN(conceptionMonth)) {
            alert('请选择年龄和月份');
            return;
        }
        
        // 预测性别
        const gender = predictGender(motherAge, conceptionMonth);
        
        // 显示结果
        if (gender === 'male') {
            predictionElement.textContent = '男孩';
            genderIconElement.textContent = '♂';
            genderResultElement.className = 'gender-result male-result';
        } else {
            predictionElement.textContent = '女孩';
            genderIconElement.textContent = '♀';
            genderResultElement.className = 'gender-result female-result';
        }
        
        resultsContainer.style.display = 'block';
        
        // 滚动到结果
        resultsContainer.scrollIntoView({ behavior: 'smooth' });
    });
    
    // 表单验证
    form.querySelectorAll('select').forEach(select => {
        select.addEventListener('change', validateForm);
    });
    
    function validateForm() {
        const motherAge = document.getElementById('motherAge').value;
        const conceptionMonth = document.getElementById('conceptionMonth').value;
        
        const isValid = motherAge && conceptionMonth;
        document.querySelector('.calculate-button').disabled = !isValid;
    }
    
    // 初始验证
    validateForm();
});

/**
 * 预测婴儿性别
 * 使用一致的哈希算法，确保相同的输入总是产生相同的输出
 * @param {number} motherAge - 母亲年龄
 * @param {number} conceptionMonth - 受孕月份
 * @returns {string} 'male' 或 'female'
 */
function predictGender(motherAge, conceptionMonth) {
    // 创建一个确定性的哈希值，基于年龄和月份
    // 使用简单的哈希算法，确保相同输入总是产生相同输出
    const hash = ((motherAge * 12 + conceptionMonth) * 9973) % 100;
    
    // 基于哈希值确定性别
    // 使用固定的阈值，确保相同输入总是产生相同结果
    return hash < 50 ? 'male' : 'female';
} 