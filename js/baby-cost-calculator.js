/**
 * 婴儿花费计算器 - 基于BabyCenter的计算器
 */

document.addEventListener('DOMContentLoaded', function() {
    // 为所有复选框添加事件监听器
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const inputId = this.id + 'Amount';
            const input = document.getElementById(inputId);
            if (input) {
                input.disabled = !this.checked;
                if (this.checked) {
                    input.value = input.getAttribute('data-default-value') || input.value;
                } else {
                    input.setAttribute('data-default-value', input.value);
                    input.value = '0';
                }
            }
            calculateTotals();
        });
    });

    // 为所有输入框添加事件监听器
    const inputs = document.querySelectorAll('input[type="number"], select');
    inputs.forEach(input => {
        input.addEventListener('change', calculateTotals);
        input.addEventListener('input', calculateTotals);
    });

    // 初始化计算
    calculateTotals();
});

/**
 * 计算所有费用并更新显示
 */
function calculateTotals() {
    // 计算托儿费用
    const childcareSubtotal = calculateChildcareSubtotal();
    document.getElementById('childcareSubtotal').textContent = formatCurrency(childcareSubtotal);

    // 计算尿布费用
    const diaperingSubtotal = calculateDiaperingSubtotal();
    document.getElementById('diaperingSubtotal').textContent = formatCurrency(diaperingSubtotal);

    // 计算喂养费用
    const feedingSubtotal = calculateFeedingSubtotal();
    document.getElementById('feedingSubtotal').textContent = formatCurrency(feedingSubtotal);

    // 计算衣物费用
    const clothingSubtotal = calculateClothingSubtotal();
    document.getElementById('clothingSubtotal').textContent = formatCurrency(clothingSubtotal);

    // 计算其他持续性费用
    const otherOngoingSubtotal = calculateOtherOngoingSubtotal();
    document.getElementById('otherOngoingSubtotal').textContent = formatCurrency(otherOngoingSubtotal);

    // 计算装备费用
    const gearSubtotal = calculateGearSubtotal();
    document.getElementById('gearSubtotal').textContent = formatCurrency(gearSubtotal);

    // 计算活动装备费用
    const activityEquipmentSubtotal = calculateActivityEquipmentSubtotal();
    document.getElementById('activityEquipmentSubtotal').textContent = formatCurrency(activityEquipmentSubtotal);

    // 计算婴儿房费用
    const nurserySubtotal = calculateNurserySubtotal();
    document.getElementById('nurserySubtotal').textContent = formatCurrency(nurserySubtotal);

    // 计算喂养装备费用
    const feedingEquipmentSubtotal = calculateFeedingEquipmentSubtotal();
    document.getElementById('feedingEquipmentSubtotal').textContent = formatCurrency(feedingEquipmentSubtotal);

    // 计算洗浴和梳理费用
    const bathingGroomingSubtotal = calculateBathingGroomingSubtotal();
    document.getElementById('bathingGroomingSubtotal').textContent = formatCurrency(bathingGroomingSubtotal);

    // 计算持续性花费总计
    const ongoingCostsTotal = childcareSubtotal + diaperingSubtotal + feedingSubtotal + clothingSubtotal + otherOngoingSubtotal;
    document.getElementById('ongoingCostsTotal').textContent = formatCurrency(ongoingCostsTotal);

    // 计算一次性花费总计
    const oneTimeCostsTotal = gearSubtotal + activityEquipmentSubtotal + nurserySubtotal + feedingEquipmentSubtotal + bathingGroomingSubtotal;
    document.getElementById('oneTimeCostsTotal').textContent = formatCurrency(oneTimeCostsTotal);

    // 计算总花费
    const totalCost = ongoingCostsTotal + oneTimeCostsTotal;
    document.getElementById('totalCost').textContent = `总花费：${formatCurrency(totalCost)}`;
}

/**
 * 计算托儿费用小计
 */
function calculateChildcareSubtotal() {
    let subtotal = 0;

    // 托儿费用
    if (document.getElementById('childcare').checked) {
        const amount = parseFloat(document.getElementById('childcareAmount').value) || 0;
        const months = parseFloat(document.getElementById('childcareMonths').value) || 0;
        subtotal += amount * months;
    }

    // 临时保姆
    if (document.getElementById('babysitter').checked) {
        const amount = parseFloat(document.getElementById('babysitterAmount').value) || 0;
        subtotal += amount * 12;
    }

    return subtotal;
}

/**
 * 计算尿布费用小计
 */
function calculateDiaperingSubtotal() {
    let subtotal = 0;

    // 尿布
    if (document.getElementById('diapers').checked) {
        const amount = parseFloat(document.getElementById('diaperType').value) || 0;
        subtotal += amount * 12;
    }

    // 湿巾
    if (document.getElementById('wipes').checked) {
        const amount = parseFloat(document.getElementById('wipesAmount').value) || 0;
        subtotal += amount * 12;
    }

    return subtotal;
}

/**
 * 计算喂养费用小计
 */
function calculateFeedingSubtotal() {
    let subtotal = 0;

    // 配方奶粉
    if (document.getElementById('formula').checked) {
        const amount = parseFloat(document.getElementById('formulaAmount').value) || 0;
        const months = parseFloat(document.getElementById('formulaMonths').value) || 0;
        subtotal += amount * months;
    }

    // 固体食物
    if (document.getElementById('solidFood').checked) {
        const amount = parseFloat(document.getElementById('solidFoodAmount').value) || 0;
        const months = parseFloat(document.getElementById('solidFoodMonths').value) || 0;
        subtotal += amount * months;
    }

    return subtotal;
}

/**
 * 计算衣物费用小计
 */
function calculateClothingSubtotal() {
    let subtotal = 0;

    // 衣物
    if (document.getElementById('clothing').checked) {
        const amount = parseFloat(document.getElementById('clothingAmount').value) || 0;
        subtotal += amount * 12;
    }

    return subtotal;
}

/**
 * 计算其他持续性费用小计
 */
function calculateOtherOngoingSubtotal() {
    let subtotal = 0;

    // 未来/教育储蓄
    if (document.getElementById('savings').checked) {
        const amount = parseFloat(document.getElementById('savingsAmount').value) || 0;
        subtotal += amount * 12;
    }

    // 医疗费用
    if (document.getElementById('healthcare').checked) {
        const amount = parseFloat(document.getElementById('healthcareAmount').value) || 0;
        subtotal += amount * 12;
    }

    // 洗护用品
    if (document.getElementById('toiletries').checked) {
        const amount = parseFloat(document.getElementById('toiletriesAmount').value) || 0;
        subtotal += amount * 12;
    }

    // 玩具、书籍和其他媒体
    if (document.getElementById('toys').checked) {
        const amount = parseFloat(document.getElementById('toysAmount').value) || 0;
        subtotal += amount * 12;
    }

    return subtotal;
}

/**
 * 计算装备费用小计
 */
function calculateGearSubtotal() {
    let subtotal = 0;

    // 婴儿汽车座椅
    if (document.getElementById('infantCarSeat').checked) {
        subtotal += parseFloat(document.getElementById('infantCarSeatAmount').value) || 0;
    }

    // 婴儿推车
    if (document.getElementById('stroller').checked) {
        subtotal += parseFloat(document.getElementById('strollerAmount').value) || 0;
    }

    // 婴儿游戏围栏
    if (document.getElementById('playyard').checked) {
        subtotal += parseFloat(document.getElementById('playyardAmount').value) || 0;
    }

    // 婴儿背带
    if (document.getElementById('carrier').checked) {
        subtotal += parseFloat(document.getElementById('carrierAmount').value) || 0;
    }

    // 尿布包
    if (document.getElementById('diaperBag').checked) {
        subtotal += parseFloat(document.getElementById('diaperBagAmount').value) || 0;
    }

    return subtotal;
}

/**
 * 计算活动装备费用小计
 */
function calculateActivityEquipmentSubtotal() {
    let subtotal = 0;

    // 婴儿弹跳椅或摇椅
    if (document.getElementById('bouncer').checked) {
        subtotal += parseFloat(document.getElementById('bouncerAmount').value) || 0;
    }

    // 婴儿活动中心
    if (document.getElementById('activityCenter').checked) {
        subtotal += parseFloat(document.getElementById('activityCenterAmount').value) || 0;
    }

    // 婴儿游戏垫
    if (document.getElementById('playMat').checked) {
        subtotal += parseFloat(document.getElementById('playMatAmount').value) || 0;
    }

    return subtotal;
}

/**
 * 计算婴儿房费用小计
 */
function calculateNurserySubtotal() {
    let subtotal = 0;

    // 婴儿床
    if (document.getElementById('crib').checked) {
        subtotal += parseFloat(document.getElementById('cribAmount').value) || 0;
    }

    // 婴儿床垫
    if (document.getElementById('mattress').checked) {
        subtotal += parseFloat(document.getElementById('mattressAmount').value) || 0;
    }

    // 婴儿监视器
    if (document.getElementById('monitor').checked) {
        subtotal += parseFloat(document.getElementById('monitorAmount').value) || 0;
    }

    return subtotal;
}

/**
 * 计算喂养装备费用小计
 */
function calculateFeedingEquipmentSubtotal() {
    let subtotal = 0;

    // 奶瓶和奶嘴
    if (document.getElementById('bottles').checked) {
        subtotal += parseFloat(document.getElementById('bottlesAmount').value) || 0;
    }

    // 高脚椅
    if (document.getElementById('highchair').checked) {
        subtotal += parseFloat(document.getElementById('highchairAmount').value) || 0;
    }

    return subtotal;
}

/**
 * 计算洗浴和梳理费用小计
 */
function calculateBathingGroomingSubtotal() {
    let subtotal = 0;

    // 婴儿浴盆
    if (document.getElementById('bathtub').checked) {
        subtotal += parseFloat(document.getElementById('bathtubAmount').value) || 0;
    }

    // 婴儿梳理套件
    if (document.getElementById('groomingKit').checked) {
        subtotal += parseFloat(document.getElementById('groomingKitAmount').value) || 0;
    }

    return subtotal;
}

/**
 * 格式化货币显示
 * @param {number} amount - 金额
 * @returns {string} - 格式化后的金额字符串
 */
function formatCurrency(amount) {
    return '¥' + amount.toLocaleString('zh-CN');
} 