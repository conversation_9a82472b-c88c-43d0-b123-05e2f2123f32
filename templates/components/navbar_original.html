<header class="site-header">
    <div class="container">
        <nav class="main-nav">
            <button class="hamburger-menu" aria-label="显示主导航菜单">
                <span class="hamburger-icon"></span>
            </button>
            <div class="logo">
                <a href="{{ url_for('main.index') }}">
                    <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="BabyJourney Logo">
                </a>
            </div>
            <ul class="nav-links">
                <li><a href="{{ url_for('main.index') }}">首页</a></li>
                <!-- 一级导航：备孕 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">备孕</a>
                    <ul class="dropdown-menu">
                        <!-- 二级导航 -->
                        {% for secondary in primary_categories|selectattr('slug', 'eq', 'pregnancy-prep')|first|attr('secondary_categories') if secondary.is_visible %}
                        <li>
                            <a href="{{ url_for('main.secondary_category', primary_slug='pregnancy-prep', secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                            <!-- 三级导航 -->
                            <ul class="submenu">
                                {% for category in secondary.categories if category.is_visible %}
                                <li><a href="{{ url_for('main.category_detail', slug=category.slug) }}">{{ category.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                        {% endfor %}
                    </ul>
                </li>
                <!-- 一级导航：怀孕 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">怀孕</a>
                    <ul class="dropdown-menu">
                        <!-- 二级导航 -->
                        {% for secondary in primary_categories|selectattr('slug', 'eq', 'pregnancy')|first|attr('secondary_categories') if secondary.is_visible %}
                        <li>
                            <a href="{{ url_for('main.secondary_category', primary_slug='pregnancy', secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                            <!-- 三级导航 -->
                            <ul class="submenu">
                                {% for category in secondary.categories if category.is_visible %}
                                <li><a href="{{ url_for('main.category_detail', slug=category.slug) }}">{{ category.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                        {% endfor %}
                    </ul>
                </li>
                <!-- 一级导航：婴儿名字 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">婴儿名字</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">流行名字</a></li>
                        <li><a href="#">男孩名字</a></li>
                        <li><a href="#">女孩名字</a></li>
                    </ul>
                </li>
                <!-- 一级导航：婴儿 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">婴儿</a>
                    <ul class="dropdown-menu">
                        <!-- 二级导航 -->
                        {% for secondary in primary_categories|selectattr('slug', 'eq', 'baby')|first|attr('secondary_categories') if secondary.is_visible %}
                        <li>
                            <a href="{{ url_for('main.secondary_category', primary_slug='baby', secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                            <!-- 三级导航 -->
                            <ul class="submenu">
                                {% for category in secondary.categories if category.is_visible %}
                                <li><a href="{{ url_for('main.category_detail', slug=category.slug) }}">{{ category.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                        {% endfor %}
                    </ul>
                </li>
                <!-- 一级导航：幼儿 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">幼儿</a>
                    <ul class="dropdown-menu">
                        <!-- 二级导航 -->
                        {% for secondary in primary_categories|selectattr('slug', 'eq', 'toddler')|first|attr('secondary_categories') if secondary.is_visible %}
                        <li>
                            <a href="{{ url_for('main.secondary_category', primary_slug='toddler', secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                            <!-- 三级导航 -->
                            <ul class="submenu">
                                {% for category in secondary.categories if category.is_visible %}
                                <li><a href="{{ url_for('main.category_detail', slug=category.slug) }}">{{ category.name }}</a></li>
                                {% endfor %}
                            </ul>
                        </li>
                        {% endfor %}
                    </ul>
                </li>
                <!-- 其他静态菜单项 -->
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">孩子</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">学龄儿童</a></li>
                        <li><a href="#">青少年</a></li>
                        <li><a href="#">教育与学习</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">健康</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">儿童健康</a></li>
                        <li><a href="#">产后恢复</a></li>
                        <li><a href="#">疾病预防</a></li>
                    </ul>
                </li>
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">家庭</a>
                    <ul class="dropdown-menu">
                        <li><a href="#">亲子关系</a></li>
                        <li><a href="#">家庭教育</a></li>
                        <li><a href="#">家庭活动</a></li>
                    </ul>
                </li>
            </ul>
            <div class="nav-right">
                {% if current_user.is_authenticated %}
                <div class="user-menu dropdown">
                    <a href="#" class="dropdown-toggle">
                        <img src="{{ current_user.get_avatar_url() }}" alt="用户头像" class="avatar">
                        <span>{{ current_user.get_display_name() }}</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="#">个人中心</a></li>
                        <li><a href="#">我的收藏</a></li>
                        <li><a href="#">消息通知</a></li>
                        {% if current_user.is_admin %}
                        <li><a href="{{ url_for('admin.index') }}">后台管理</a></li>
                        {% endif %}
                        <li><a href="{{ url_for('auth.logout') }}">退出登录</a></li>
                    </ul>
                </div>
                {% else %}
                <a href="{{ url_for('auth.login') }}" class="btn btn-outline">登录</a>
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary">注册</a>
                {% endif %}
            </div>
        </nav>
    </div>
</header>

<!-- 侧边栏导航菜单 -->
<div class="sidebar-menu">
    <div class="sidebar-header">
        <a href="{{ url_for('main.index') }}" class="sidebar-logo">
            <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="BabyJourney Logo">
        </a>
        <div class="search-box">
            <input type="text" placeholder="搜索内容..." aria-label="搜索内容">
            <img src="{{ url_for('static', filename='img/search-icon.svg') }}" alt="搜索图标">
        </div>
    </div>
    <ul class="sidebar-nav">
        <!-- 动态生成侧边栏导航 -->
        {% for primary in primary_categories if primary.is_visible %}
        <li>
            <button class="sidebar-category" aria-expanded="false">{{ primary.name }}</button>
            <ul class="sidebar-submenu">
                {% for secondary in primary.secondary_categories if secondary.is_visible %}
                <li>
                    <a href="{{ url_for('main.secondary_category', primary_slug=primary.slug, secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                    <!-- 如果需要三级菜单，可以在这里添加 -->
                </li>
                {% endfor %}
            </ul>
        </li>
        {% endfor %}
        <!-- 静态菜单项 -->
        <li>
            <button class="sidebar-category" aria-expanded="false">婴儿名字</button>
            <ul class="sidebar-submenu">
                <li><a href="#">流行名字</a></li>
                <li><a href="#">男孩名字</a></li>
                <li><a href="#">女孩名字</a></li>
                <li><a href="#">名字搜索</a></li>
            </ul>
        </li>
        <li>
            <button class="sidebar-category" aria-expanded="false">孩子</button>
            <ul class="sidebar-submenu">
                <li><a href="#">学龄儿童</a></li>
                <li><a href="#">青少年</a></li>
                <li><a href="#">教育与学习</a></li>
                <li><a href="#">心理健康</a></li>
            </ul>
        </li>
    </ul>
</div>

<!-- 遮罩层 -->
<div class="sidebar-overlay"></div> 