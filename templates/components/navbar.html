<!-- 使用原始导航栏样式，集成多级导航功能 -->
<header class="site-header">
    <div class="container">
        <nav class="main-nav">
            <button class="hamburger-menu" aria-label="显示主导航菜单">
                <span class="hamburger-icon"></span>
            </button>
            <div class="logo">
                <a href="{{ url_for('main.index') }}">
                    <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="BabyJourney Logo">
                </a>
            </div>
            <ul class="nav-links">
                <li><a href="{{ url_for('main.index') }}">首页</a></li>
                
                <!-- 动态生成一级导航 - 遍历所有可见的一级分类 -->
                {% for primary in primary_categories %}
                {% if primary.is_visible and primary.secondary_categories.count() > 0 %}
                <li class="dropdown">
                    <a href="#" class="dropdown-toggle">{{ primary.name }}</a>
                    <ul class="dropdown-menu">
                        <!-- 二级导航 - 遍历所有可见的二级分类 -->
                        {% for secondary in primary.secondary_categories %}
                        {% if secondary.is_visible %}
                        <li>
                            <a href="{{ url_for('main.secondary_category', primary_slug=primary.slug, secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                </li>
                {% endif %}
                {% endfor %}

                <!-- 工具链接 -->
                <li><a href="{{ url_for('tools.tools_index') }}"><i class="fas fa-tools"></i> 实用工具</a></li>
            </ul>
            <div class="nav-right">
                {% if current_user.is_authenticated %}
                <div class="user-menu dropdown">
                    <a href="#" class="dropdown-toggle">
                        <img src="{{ current_user.get_avatar_url() }}" alt="用户头像" class="avatar">
                        <span>{{ current_user.get_display_name() }}</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a href="#">个人中心</a></li>
                        <li><a href="#">我的收藏</a></li>
                        <li><a href="#">消息通知</a></li>
                        {% if current_user.is_admin %}
                        <li><a href="{{ url_for('admin.index') }}">后台管理</a></li>
                        {% endif %}
                        <li><a href="{{ url_for('auth.logout') }}">退出登录</a></li>
                    </ul>
                </div>
                {% else %}
                <a href="{{ url_for('auth.login') }}" class="btn btn-outline">登录</a>
                <a href="{{ url_for('auth.register') }}" class="btn btn-primary">注册</a>
                {% endif %}
            </div>
        </nav>
    </div>
</header>

<!-- 侧边栏导航菜单 -->
<div class="sidebar-menu">
    <div class="sidebar-header">
        <a href="{{ url_for('main.index') }}" class="sidebar-logo">
            <img src="{{ url_for('static', filename='img/logo.svg') }}" alt="BabyJourney Logo">
        </a>
        <div class="search-box">
            <input type="text" placeholder="搜索内容..." aria-label="搜索内容">
            <img src="{{ url_for('static', filename='img/search-icon.svg') }}" alt="搜索图标">
        </div>
    </div>
    <ul class="sidebar-nav">
        <!-- 动态生成侧边栏导航 - 遍历所有可见的一级分类 -->
        {% for primary in primary_categories %}
        {% if primary.is_visible and primary.secondary_categories.count() > 0 %}
        <li>
            <button class="sidebar-category" aria-expanded="false">{{ primary.name }}</button>
            <ul class="sidebar-submenu">
                <!-- 遍历所有可见的二级分类 -->
                {% for secondary in primary.secondary_categories %}
                {% if secondary.is_visible %}
                <li>
                    <a href="{{ url_for('main.secondary_category', primary_slug=primary.slug, secondary_slug=secondary.slug) }}">{{ secondary.name }}</a>
                </li>
                {% endif %}
                {% endfor %}
            </ul>
        </li>
        {% endif %}
        {% endfor %}

        <!-- 工具链接 -->
        <li>
            <a href="{{ url_for('tools.tools_index') }}" class="sidebar-tool-link">
                <i class="fas fa-tools"></i> 实用工具
            </a>
        </li>
    </ul>
</div>

<!-- 遮罩层 -->
<div class="sidebar-overlay"></div> 
