{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">用户登录</h4>
            </div>
            <div class="card-body">
                <form method="post" action="{{ url_for('auth.login') }}">
                    {{ form.hidden_tag() }}
                    <div class="mb-3">
                        {{ form.username.label(class="form-label") }}
                        {{ form.username(class="form-control") }}
                        {% for error in form.username.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="mb-3">
                        {{ form.password.label(class="form-label") }}
                        {{ form.password(class="form-control") }}
                        {% for error in form.password.errors %}
                            <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    <div class="mb-3 form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        {{ form.remember_me.label(class="form-check-label") }}
                    </div>
                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
                
                <hr>
                <div class="text-center">
                    <p>还没有账号？<a href="{{ url_for('auth.register') }}">立即注册</a></p>
                    <p><a href="{{ url_for('auth.reset_password_request') }}">忘记密码？</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 