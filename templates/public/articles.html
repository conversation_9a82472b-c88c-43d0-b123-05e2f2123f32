{% extends "base.html" %}

{% block title %}文章列表 - {{ site_name }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-4">文章列表</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                    <li class="breadcrumb-item active" aria-current="page">文章列表</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- 文章列表 -->
        <div class="col-lg-9">
            <div class="row">
                {% if articles %}
                    {% for article in articles %}
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="card h-100 shadow-sm">
                                {% if article.featured_image %}
                                    <img src="{{ article.featured_image }}" class="card-img-top" alt="{{ article.title }}">
                                {% else %}
                                    <img src="/static/img/placeholder.jpg" class="card-img-top" alt="{{ article.title }}">
                                {% endif %}
                                <div class="card-body">
                                    <h5 class="card-title">{{ article.title }}</h5>
                                    <p class="card-text text-muted small">
                                        {% if article.category %}
                                            <a href="{{ url_for('main.category_detail', slug=article.category.slug) }}" class="text-decoration-none">
                                                <span class="badge bg-primary">{{ article.category.name }}</span>
                                            </a>
                                        {% endif %}
                                        <span class="ms-2">{{ article.published_at|date }}</span>
                                    </p>
                                    <p class="card-text">{{ article.summary|truncate(100) }}</p>
                                </div>
                                <div class="card-footer bg-white border-top-0">
                                    <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="btn btn-outline-primary btn-sm">阅读全文</a>
                                    <div class="float-end">
                                        <small class="text-muted">
                                            <i class="fas fa-eye"></i> {{ article.view_count }}
                                            <i class="fas fa-comment ms-2"></i> {{ article.comment_count }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="alert alert-info">
                            暂无文章
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- 分页 -->
            {% if pagination and pagination.pages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.articles', page=pagination.prev_num) }}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                        {% endif %}

                        {% for page in pagination.iter_pages() %}
                            {% if page %}
                                {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('main.articles', page=page) }}">{{ page }}</a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <a class="page-link" href="#">{{ page }}</a>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <a class="page-link" href="#">...</a>
                                </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('main.articles', page=pagination.next_num) }}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
            {% endif %}
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-3">
            <!-- 分类 -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">分类</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for category in categories %}
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <a href="{{ url_for('main.category_detail', slug=category.slug) }}" class="text-decoration-none">
                                    {{ category.name }}
                                </a>
                                <span class="badge bg-primary rounded-pill">
                                    {{ category.articles.filter_by(published=True).count() }}
                                </span>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <!-- 最新文章 -->
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">最新文章</h5>
                </div>
                <div class="card-body">
                    <ul class="list-group list-group-flush">
                        {% for article in recent_articles %}
                            <li class="list-group-item">
                                <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="text-decoration-none">
                                    {{ article.title }}
                                </a>
                                <small class="text-muted d-block">{{ article.published_at|date }}</small>
                            </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 