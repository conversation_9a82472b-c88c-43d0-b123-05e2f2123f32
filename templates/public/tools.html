{% extends "base.html" %}

{% block title %}实用工具 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.tools-header {
    background-color: #f8f9fa;
    padding: 60px 0;
    margin-bottom: 40px;
    text-align: center;
}

.tools-description {
    max-width: 800px;
    margin: 20px auto 0;
    color: #6c757d;
}

.tools-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
}

.tool-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    border-bottom: 1px solid #dee2e6;
}

.tool-tab {
    padding: 15px 30px;
    font-size: 1.1em;
    color: #6c757d;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tool-tab:hover {
    color: #0d6efd;
}

.tool-tab.active {
    color: #0d6efd;
    border-bottom-color: #0d6efd;
}

.tool-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.tool-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    margin-bottom: 8px;
}

.form-help {
    font-size: 0.9em;
    color: #6c757d;
    margin-top: 5px;
}

.result-section {
    margin-top: 30px;
    padding-top: 30px;
    border-top: 1px solid #dee2e6;
}

.result-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.result-title {
    font-size: 1.2em;
    font-weight: 500;
    margin-bottom: 10px;
    color: #0d6efd;
}

.result-value {
    font-size: 2em;
    font-weight: bold;
    margin: 15px 0;
    color: #198754;
}

.result-description {
    color: #6c757d;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 5px;
    margin-top: 20px;
}

.calendar-header {
    text-align: center;
    font-weight: 500;
    padding: 10px;
    background: #e9ecef;
}

.calendar-day {
    text-align: center;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}

.calendar-day.fertile {
    background: #cfe2ff;
    color: #0d6efd;
}

.calendar-day.ovulation {
    background: #d1e7dd;
    color: #198754;
}

.calendar-legend {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 0.9em;
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 8px;
}

.bmi-chart {
    margin-top: 30px;
}

.bmi-range {
    display: flex;
    height: 30px;
    margin-bottom: 10px;
    border-radius: 4px;
    overflow: hidden;
}

.bmi-range-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9em;
}

.range-underweight { background: #ffc107; }
.range-normal { background: #198754; }
.range-overweight { background: #fd7e14; }
.range-obese { background: #dc3545; }

.bmi-marker {
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #212529;
    margin-left: calc(var(--bmi-percentage) * 1% - 10px);
}

@media (max-width: 768px) {
    .tool-tabs {
        flex-wrap: wrap;
    }
    
    .tool-tab {
        padding: 10px 20px;
    }
}
</style>
{% endblock %}

{% block content %}
<header class="tools-header">
    <div class="container">
        <h1>实用工具</h1>
        <p class="tools-description">我们提供一系列实用的孕期工具，帮助您更好地了解和规划孕育过程</p>
    </div>
</header>

<div class="tools-container">
    <div class="tool-tabs">
        <div class="tool-tab {% if active_tool == 'ovulation' %}active{% endif %}" data-tool="ovulation">排卵期计算器</div>
        <div class="tool-tab {% if active_tool == 'due_date' %}active{% endif %}" data-tool="due_date">预产期计算器</div>
        <div class="tool-tab {% if active_tool == 'bmi' %}active{% endif %}" data-tool="bmi">BMI计算器</div>
    </div>

    <!-- 排卵期计算器 -->
    <div class="tool-content" id="ovulation-calculator" {% if active_tool != 'ovulation' %}style="display: none;"{% endif %}>
        <form class="tool-form" id="ovulation-form">
            <div class="form-group">
                <label class="form-label">最后一次月经第一天</label>
                <input type="date" class="form-control" name="last_period" required>
                <div class="form-help">请选择您最后一次月经的第一天日期</div>
            </div>
            <div class="form-group">
                <label class="form-label">平均月经周期</label>
                <select class="form-control" name="cycle_length" required>
                    {% for i in range(21, 36) %}
                    <option value="{{ i }}" {% if i == 28 %}selected{% endif %}>{{ i }}天</option>
                    {% endfor %}
                </select>
                <div class="form-help">正常月经周期为21-35天，如果不确定请选择28天</div>
            </div>
            <button type="submit" class="btn btn-primary w-100">计算排卵期</button>
        </form>

        <div class="result-section" id="ovulation-result" style="display: none;">
            <div class="result-card">
                <div class="result-title">下次排卵日期</div>
                <div class="result-value" id="ovulation-date"></div>
                <p class="result-description">这是您下一次排卵的预计日期，建议在排卵日前后同房，可提高受孕几率</p>
            </div>

            <div class="result-card">
                <div class="result-title">排卵窗口期</div>
                <div class="calendar-grid">
                    <div class="calendar-header">日</div>
                    <div class="calendar-header">一</div>
                    <div class="calendar-header">二</div>
                    <div class="calendar-header">三</div>
                    <div class="calendar-header">四</div>
                    <div class="calendar-header">五</div>
                    <div class="calendar-header">六</div>
                    <!-- Calendar days will be inserted here by JavaScript -->
                </div>
                <div class="calendar-legend">
                    <div class="legend-item">
                        <div class="legend-color" style="background: #cfe2ff;"></div>
                        <span>易受孕期</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background: #d1e7dd;"></div>
                        <span>排卵日</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 预产期计算器 -->
    <div class="tool-content" id="due-date-calculator" {% if active_tool != 'due_date' %}style="display: none;"{% endif %}>
        <form class="tool-form" id="due-date-form">
            <div class="form-group">
                <label class="form-label">计算方式</label>
                <select class="form-control" name="calculation_method" required>
                    <option value="last_period">最后一次月经</option>
                    <option value="conception">受孕日期</option>
                    <option value="ivf">试管婴儿移植日期</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">日期</label>
                <input type="date" class="form-control" name="date" required>
                <div class="form-help" id="date-help">请选择最后一次月经的第一天</div>
            </div>
            <button type="submit" class="btn btn-primary w-100">计算预产期</button>
        </form>

        <div class="result-section" id="due-date-result" style="display: none;">
            <div class="result-card">
                <div class="result-title">预产期</div>
                <div class="result-value" id="due-date"></div>
                <p class="result-description">这是您预计分娩的日期，实际分娩日期可能会有1-2周的误差</p>
            </div>

            <div class="result-card">
                <div class="result-title">当前孕周</div>
                <div class="result-value" id="pregnancy-week"></div>
                <p class="result-description" id="week-description"></p>
            </div>
        </div>
    </div>

    <!-- BMI计算器 -->
    <div class="tool-content" id="bmi-calculator" {% if active_tool != 'bmi' %}style="display: none;"{% endif %}>
        <form class="tool-form" id="bmi-form">
            <div class="form-group">
                <label class="form-label">身高 (cm)</label>
                <input type="number" class="form-control" name="height" min="140" max="200" required>
            </div>
            <div class="form-group">
                <label class="form-label">体重 (kg)</label>
                <input type="number" class="form-control" name="weight" min="30" max="150" step="0.1" required>
            </div>
            <div class="form-group">
                <label class="form-label">孕周</label>
                <select class="form-control" name="pregnancy_week">
                    <option value="">未怀孕</option>
                    {% for i in range(1, 41) %}
                    <option value="{{ i }}">{{ i }}周</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit" class="btn btn-primary w-100">计算BMI</button>
        </form>

        <div class="result-section" id="bmi-result" style="display: none;">
            <div class="result-card">
                <div class="result-title">您的BMI指数</div>
                <div class="result-value" id="bmi-value"></div>
                <p class="result-description" id="bmi-category"></p>
            </div>

            <div class="bmi-chart">
                <div class="bmi-range">
                    <div class="bmi-range-item range-underweight">偏瘦<br>&lt;18.5</div>
                    <div class="bmi-range-item range-normal">正常<br>18.5-24.9</div>
                    <div class="bmi-range-item range-overweight">超重<br>25-29.9</div>
                    <div class="bmi-range-item range-obese">肥胖<br>&gt;30</div>
                </div>
                <div class="bmi-marker"></div>
            </div>

            <div class="result-card">
                <div class="result-title">建议</div>
                <p class="result-description" id="bmi-advice"></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching
    const tabs = document.querySelectorAll('.tool-tab');
    const toolContents = document.querySelectorAll('.tool-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tool = this.dataset.tool;
            
            // Update tabs
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Update content visibility
            toolContents.forEach(content => {
                content.style.display = content.id === `${tool}-calculator` ? 'block' : 'none';
            });
            
            // Update URL
            const url = new URL(window.location.href);
            url.searchParams.set('tool', tool);
            history.pushState({}, '', url);
        });
    });

    // Ovulation Calculator
    const ovulationForm = document.getElementById('ovulation-form');
    if (ovulationForm) {
        ovulationForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const lastPeriod = new Date(this.elements.last_period.value);
            const cycleLength = parseInt(this.elements.cycle_length.value);
            
            // Calculate ovulation date (14 days before next period)
            const ovulationDate = new Date(lastPeriod);
            ovulationDate.setDate(lastPeriod.getDate() + cycleLength - 14);
            
            // Update results
            document.getElementById('ovulation-date').textContent = ovulationDate.toLocaleDateString('zh-CN');
            document.getElementById('ovulation-result').style.display = 'block';
            
            // Generate calendar
            generateFertilityCalendar(ovulationDate);
        });
    }

    // Due Date Calculator
    const dueDateForm = document.getElementById('due-date-form');
    if (dueDateForm) {
        const methodSelect = dueDateForm.elements.calculation_method;
        const dateHelp = document.getElementById('date-help');
        
        methodSelect.addEventListener('change', function() {
            switch(this.value) {
                case 'last_period':
                    dateHelp.textContent = '请选择最后一次月经的第一天';
                    break;
                case 'conception':
                    dateHelp.textContent = '请选择受孕日期';
                    break;
                case 'ivf':
                    dateHelp.textContent = '请选择胚胎移植日期';
                    break;
            }
        });
        
        dueDateForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const date = new Date(this.elements.date.value);
            let dueDate = new Date(date);
            
            switch(this.elements.calculation_method.value) {
                case 'last_period':
                    dueDate.setDate(date.getDate() + 280);
                    break;
                case 'conception':
                    dueDate.setDate(date.getDate() + 266);
                    break;
                case 'ivf':
                    dueDate.setDate(date.getDate() + 266);
                    break;
            }
            
            // Calculate current week
            const today = new Date();
            const diffTime = Math.abs(today - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            const currentWeek = Math.floor(diffDays / 7);
            
            // Update results
            document.getElementById('due-date').textContent = dueDate.toLocaleDateString('zh-CN');
            document.getElementById('pregnancy-week').textContent = `${currentWeek}周${diffDays % 7}天`;
            document.getElementById('week-description').textContent = getWeekDescription(currentWeek);
            document.getElementById('due-date-result').style.display = 'block';
        });
    }

    // BMI Calculator
    const bmiForm = document.getElementById('bmi-form');
    if (bmiForm) {
        bmiForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const height = parseFloat(this.elements.height.value) / 100; // Convert to meters
            const weight = parseFloat(this.elements.weight.value);
            const pregnancyWeek = this.elements.pregnancy_week.value;
            
            const bmi = weight / (height * height);
            const bmiRounded = Math.round(bmi * 10) / 10;
            
            // Update results
            document.getElementById('bmi-value').textContent = bmiRounded;
            
            let category, advice;
            if (bmi < 18.5) {
                category = '偏瘦';
                advice = '建议适当增加营养摄入，保持均衡饮食，适量运动。';
            } else if (bmi < 25) {
                category = '正常';
                advice = '继续保持健康的生活方式，均衡饮食，规律运动。';
            } else if (bmi < 30) {
                category = '超重';
                advice = '建议控制饮食，增加运动量，逐步将体重降至正常范围。';
            } else {
                category = '肥胖';
                advice = '建议在医生指导下进行科学的减重，注意控制饮食，适量运动。';
            }
            
            if (pregnancyWeek) {
                advice += '\n\n孕期体重增长建议：\n';
                if (bmi < 18.5) {
                    advice += '孕期总体重增长建议为12.5-18千克。';
                } else if (bmi < 25) {
                    advice += '孕期总体重增长建议为11.5-16千克。';
                } else if (bmi < 30) {
                    advice += '孕期总体重增长建议为7-11.5千克。';
                } else {
                    advice += '孕期总体重增长建议为5-9千克。';
                }
            }
            
            document.getElementById('bmi-category').textContent = `您的体重属于${category}范围`;
            document.getElementById('bmi-advice').textContent = advice;
            
            // Update BMI marker position
            const percentage = Math.min(Math.max((bmi - 15) / 25 * 100, 0), 100);
            document.querySelector('.bmi-marker').style.setProperty('--bmi-percentage', percentage);
            
            document.getElementById('bmi-result').style.display = 'block';
        });
    }
});

function generateFertilityCalendar(ovulationDate) {
    const calendar = document.querySelector('.calendar-grid');
    const startDate = new Date(ovulationDate);
    startDate.setDate(startDate.getDate() - 10); // Start 10 days before ovulation
    
    // Clear existing calendar days
    const headers = calendar.querySelectorAll('.calendar-header');
    calendar.innerHTML = '';
    headers.forEach(header => calendar.appendChild(header));
    
    // Add empty cells for days before the start date
    const startDay = startDate.getDay();
    for (let i = 0; i < startDay; i++) {
        const emptyCell = document.createElement('div');
        emptyCell.className = 'calendar-day';
        calendar.appendChild(emptyCell);
    }
    
    // Add calendar days
    for (let i = 0; i < 21; i++) { // Show 21 days
        const currentDate = new Date(startDate);
        currentDate.setDate(startDate.getDate() + i);
        
        const dayCell = document.createElement('div');
        dayCell.className = 'calendar-day';
        
        // Add special classes for fertile days and ovulation day
        const daysDiff = Math.round((currentDate - ovulationDate) / (1000 * 60 * 60 * 24));
        if (daysDiff === 0) {
            dayCell.classList.add('ovulation');
        } else if (daysDiff >= -3 && daysDiff <= 1) {
            dayCell.classList.add('fertile');
        }
        
        dayCell.textContent = currentDate.getDate();
        calendar.appendChild(dayCell);
    }
}

function getWeekDescription(week) {
    const descriptions = {
        0: '胚胎开始发育',
        4: '胎儿心跳开始',
        8: '主要器官开始形成',
        12: '第一孕期即将结束，建议进行唐筛检查',
        16: '可能开始感受到胎动',
        20: '可以进行大排畸检查',
        24: '胎儿各器官发育成熟',
        28: '进入第三孕期',
        32: '胎儿开始储备营养',
        36: '胎儿基本发育成熟',
        40: '预产期已到，随时可能分娩'
    };
    
    // Find the closest week description
    const weeks = Object.keys(descriptions).map(Number);
    const closestWeek = weeks.reduce((prev, curr) => {
        return (Math.abs(curr - week) < Math.abs(prev - week) ? curr : prev);
    });
    
    return descriptions[closestWeek];
}
</script>
{% endblock %} 