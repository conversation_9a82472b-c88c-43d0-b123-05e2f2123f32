{% extends "base.html" %}

{% block title %}{{ article.title }} - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.article-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 40px 20px;
}

.article-header {
    text-align: center;
    margin-bottom: 40px;
}

.article-meta {
    color: #6c757d;
    font-size: 0.9em;
    margin: 20px 0;
}

.article-meta span {
    margin: 0 10px;
}

.article-content {
    font-size: 1.1em;
    line-height: 1.8;
}

.article-content img {
    max-width: 100%;
    height: auto;
    margin: 20px 0;
}

.article-content h2 {
    margin-top: 40px;
    margin-bottom: 20px;
}

.article-content h3 {
    margin-top: 30px;
    margin-bottom: 15px;
}

.article-content p {
    margin-bottom: 20px;
}

.auto-link {
    color: #0d6efd;
    text-decoration: none;
    border-bottom: 1px dashed #0d6efd;
}

.auto-link:hover {
    color: #0a58ca;
    text-decoration: none;
    border-bottom: 1px solid #0a58ca;
}

.toc {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    position: sticky;
    top: 20px;
}

.toc-title {
    font-size: 1.2em;
    margin-bottom: 15px;
    font-weight: bold;
}

.toc ul {
    list-style: none;
    padding-left: 0;
}

.toc ul ul {
    padding-left: 20px;
}

.toc li {
    margin: 8px 0;
}

.toc a {
    color: #495057;
    text-decoration: none;
}

.toc a:hover {
    color: #0d6efd;
}

/* 要点样式 */
.key-takeaways {
    background-color: #fff3cd;
    border-radius: 8px;
    padding: 20px 25px;
    margin: 30px 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.key-takeaways h4 {
    margin-bottom: 15px;
    font-weight: bold;
    color: #664d03;
}

.key-takeaways ul {
    margin-bottom: 0;
    padding-left: 20px;
}

.key-takeaways li {
    margin-bottom: 10px;
    line-height: 1.6;
    color: #664d03;
}

.key-takeaways li:last-child {
    margin-bottom: 0;
}

.medical-review {
    background: #e8f5e9;
    padding: 20px;
    border-radius: 8px;
    margin: 30px 0;
}

.medical-review-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.medical-review-header img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-right: 15px;
}

.comments-section {
    margin-top: 60px;
}

.comment {
    margin-bottom: 30px;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 15px;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9em;
}

.comment-content {
    margin-left: 55px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

.comment-form {
    margin-top: 40px;
}

.comment-form textarea {
    width: 100%;
    height: 120px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.article-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    margin: 40px 0;
}

.action-button {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    border: none;
    background: none;
    color: #6c757d;
    cursor: pointer;
}

.action-button:hover {
    color: #0d6efd;
}

.action-button i {
    margin-right: 8px;
}

.action-count {
    margin-left: 5px;
}

/* 优化文章内导航锚点样式 - BabyCenter风格 */
.article-nav-anchors {
    margin: 30px 0;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.article-nav-anchors .nav-title {
    font-size: 1.1em;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    user-select: none;
    padding: 5px;
}

.article-nav-anchors .nav-title:hover {
    background-color: rgba(0,0,0,0.02);
    border-radius: 4px;
}

.article-nav-anchors .nav-title i {
    margin-right: 8px;
    color: #0d6efd;
}

.article-nav-anchors .nav-title .toggle-icon {
    margin-left: auto;
    transition: transform 0.3s ease;
    font-size: 0.9em;
}

/* 导航内容样式 */
.article-nav-anchors .nav-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-in-out;
    opacity: 0;
    visibility: hidden;
}

.article-nav-anchors.expanded .nav-content {
    opacity: 1;
    visibility: visible;
    margin-top: 10px;
    overflow-y: auto;
}

.article-nav-anchors.expanded .nav-title .toggle-icon {
    transform: rotate(180deg);
}

/* 导航列表样式 */
.article-nav-anchors ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.article-nav-anchors ul ul {
    padding-left: 20px;
}

.article-nav-anchors li {
    margin: 10px 0;
    line-height: 1.4;
}

.article-nav-anchors a {
    color: #495057;
    text-decoration: none;
    font-size: 0.95em;
    display: block;
    padding: 3px 5px;
    transition: color 0.2s ease;
    border-radius: 4px;
}

.article-nav-anchors a:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
}

/* 适配移动端 */
@media (max-width: 768px) {
    .article-nav-anchors {
        margin: 20px 0;
        padding: 12px 15px;
    }
    
    .article-nav-anchors .nav-title {
        font-size: 1em;
    }

    .article-nav-anchors li {
        margin: 8px 0;
    }

    .article-nav-anchors a {
        font-size: 0.9em;
        padding: 2px 0;
    }
}
</style>
{% endblock %}

{% block content %}
<article class="article-container">
    <header class="article-header">
        <h1>{{ article.title }}</h1>
        <div class="article-meta">
            <span><i class="far fa-calendar"></i> {{ article.created_at.strftime('%Y-%m-%d') }}</span>
            <span><i class="far fa-eye"></i> {{ article.view_count }} 次阅读</span>
            <span><i class="far fa-heart"></i> {{ article.like_count }} 次点赞</span>
            <span><i class="far fa-comment"></i> {{ article.comment_count }} 条评论</span>
        </div>
    </header>

    <!-- 隐藏目录但保留其功能 -->
    <div class="toc" style="display: none;">
        <div class="toc-title">目录</div>
        {{ toc_html|safe }}
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="article-content">
                <div id="article-content-wrapper">
                    {{ content_html|safe }}
                </div>
            </div>

            {% if article.medical_review %}
            <div class="medical-review">
                <div class="medical-review-header">
                    <img src="{{ article.medical_reviewer.avatar }}" alt="{{ article.medical_reviewer.name }}">
                    <div>
                        <h4>医学审核</h4>
                        <p>{{ article.medical_reviewer.name }} - {{ article.medical_reviewer.title }}</p>
                    </div>
                </div>
                <p>{{ article.medical_review }}</p>
            </div>
            {% endif %}

            <div class="article-actions">
                <button class="action-button like-button" data-article-id="{{ article.id }}">
                    <i class="{% if current_user.is_authenticated and current_user.has_liked(article) %}fas{% else %}far{% endif %} fa-heart"></i>
                    点赞
                    <span class="action-count">{{ article.like_count }}</span>
                </button>
                <button class="action-button bookmark-button" data-article-id="{{ article.id }}">
                    <i class="{% if current_user.is_authenticated and current_user.has_bookmarked(article) %}fas{% else %}far{% endif %} fa-bookmark"></i>
                    收藏
                    <span class="action-count">{{ article.like_count }}</span>
                </button>
                <button class="action-button share-button">
                    <i class="fas fa-share-alt"></i>
                    分享
                </button>
            </div>

            <section class="comments-section">
                <h3>评论 ({{ article.comment_count }})</h3>
                
                {% if current_user.is_authenticated %}
                <form class="comment-form" method="POST" action="{{ url_for('main.add_comment') }}">
                    <input type="hidden" name="article_id" value="{{ article.id }}">
                    <div class="form-group">
                        <textarea name="content" class="form-control" placeholder="写下你的评论..."></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">发表评论</button>
                </form>
                {% else %}
                <div class="alert alert-info">
                    请<a href="{{ url_for('auth.login') }}">登录</a>后发表评论
                </div>
                {% endif %}

                <div class="comments-list">
                    {% for comment in article.comments %}
                    <div class="comment">
                        <div class="comment-header">
                            <img src="{{ comment.author.get_avatar_url() }}" alt="{{ comment.author.get_display_name() }}" class="comment-avatar">
                            <div>
                                <h5>{{ comment.author.get_display_name() }}</h5>
                                <div class="comment-meta">
                                    {{ comment.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                        </div>
                        <div class="comment-content">
                            {{ comment.content }}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </section>
        </div>
    </div>
</article>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 标准化标题ID的辅助函数
    function normalizeHeadingId(text) {
        return text.toLowerCase()
            .replace(/[^\w\s-]/g, '') // 移除特殊字符
            .replace(/\s+/g, '-')     // 空格替换为连字符
            .trim();
    }
    
    // 为文章中的所有标题自动添加ID属性
    const articleContent = document.getElementById('article-content-wrapper');
    if (articleContent) {
        const headings = articleContent.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
            // 只处理没有ID的标题
            if (!heading.id) {
                heading.id = normalizeHeadingId(heading.textContent);
                console.log('为标题添加ID:', heading.textContent, '→', heading.id);
            }
        });
    }
    
    // 锚点导航功能重构
    const tocElement = document.querySelector('.toc');
    
    if (tocElement) {
        const articleContent = document.getElementById('article-content-wrapper');
        if (articleContent) {
            const firstParagraph = articleContent.querySelector('p');
            
            // 创建导航锚点元素 - 默认收起
            const navAnchors = document.createElement('div');
            navAnchors.className = 'article-nav-anchors collapsible';
            
            // 创建折叠式标题 - 使用向下箭头表示可展开
            const navTitle = document.createElement('div');
            navTitle.className = 'nav-title';
            navTitle.innerHTML = '<i class="fa fa-list-ul"></i>文章导航<i class="fa fa-chevron-down toggle-icon"></i>';
            
            // 创建内容容器
            const navContent = document.createElement('div');
            navContent.className = 'nav-content';
            navContent.style.maxHeight = '0px'; // 确保初始状态是收起的
            
            // 直接复制toc元素里面的ul部分，保持完整结构
            const tocContent = tocElement.querySelector('ul');
            if (tocContent) {
                // 克隆整个ul结构
                const clonedContent = tocContent.cloneNode(true);
                navContent.appendChild(clonedContent);
            }
            
            navAnchors.appendChild(navTitle);
            navAnchors.appendChild(navContent);
            
            // 确定插入位置 - 在第一个段落后插入
            if (firstParagraph) {
                firstParagraph.parentNode.insertBefore(navAnchors, firstParagraph.nextSibling);
            } else if (articleContent.firstChild) {
                // 没有段落，插入到内容开头
                articleContent.insertBefore(navAnchors, articleContent.firstChild);
            }
            
            // 导航切换功能
            function toggleNavigation() {
                navAnchors.classList.toggle('expanded');
                const toggleIcon = navTitle.querySelector('.toggle-icon');
                toggleIcon.classList.toggle('fa-chevron-down');
                toggleIcon.classList.toggle('fa-chevron-up');
                
                // 动态设置maxHeight
                if (navAnchors.classList.contains('expanded')) {
                    // 计算内容实际高度并设置
                    navContent.style.maxHeight = navContent.scrollHeight + 'px';
                } else {
                    // 收起时设为0
                    navContent.style.maxHeight = '0px';
                }
            }
            
            // 添加点击事件
            navTitle.addEventListener('click', toggleNavigation);
            
            // 为导航内的链接添加平滑滚动和点击后自动收起功能
            navContent.querySelectorAll('a').forEach(anchor => {
                anchor.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除锚点中的问号并获取纯净的href
                    const targetId = this.getAttribute('href').replace(/\?/g, '');
                    console.log('尝试滚动到:', targetId);
                    
                    // 先尝试直接查找
                    let targetElement = document.querySelector(targetId);
                    
                    // 如果没找到，尝试其他方法定位元素
                    if (!targetElement) {
                        console.log('直接查找失败，尝试替代方法');
                        const normalizedId = targetId.substring(1); // 移除#
                        
                        // 查找具有匹配文本内容的标题元素
                        const allHeadings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
                        for (const heading of allHeadings) {
                            if (normalizeHeadingId(heading.textContent) === normalizedId || 
                                heading.textContent.trim() === this.textContent.trim()) {
                                targetElement = heading;
                                console.log('找到替代目标:', heading.textContent);
                                break;
                            }
                        }
                    }
                    
                    if (targetElement) {
                        // 平滑滚动到目标位置
                        window.scrollTo({
                            top: targetElement.offsetTop - 100, // 添加一些顶部偏移
                            behavior: 'smooth'
                        });
                        
                        // 在移动设备上自动收起导航（如果窗口宽度小于768px）
                        if (window.innerWidth < 768) {
                            setTimeout(() => {
                                navAnchors.classList.remove('expanded');
                                const toggleIcon = navTitle.querySelector('.toggle-icon');
                                toggleIcon.classList.remove('fa-chevron-up');
                                toggleIcon.classList.add('fa-chevron-down');
                                navContent.style.maxHeight = '0px';
                            }, 500); // 延迟收起，先让用户看到点击效果
                        }
                    } else {
                        console.error('未找到目标元素:', targetId);
                    }
                });
            });
        }
    }

    // Like button functionality
    const likeButton = document.querySelector('.like-button');
    if (likeButton) {
        likeButton.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            fetch(`/article/like/${articleId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    const icon = this.querySelector('i');
                    const count = this.querySelector('.action-count');
                    icon.classList.toggle('far');
                    icon.classList.toggle('fas');
                    count.textContent = data.like_count;
                }
            });
        });
    }

    // Bookmark button functionality
    const bookmarkButton = document.querySelector('.bookmark-button');
    if (bookmarkButton) {
        bookmarkButton.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            fetch(`/article/like/${articleId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    const icon = this.querySelector('i');
                    const count = this.querySelector('.action-count');
                    icon.classList.toggle('far');
                    icon.classList.toggle('fas');
                    count.textContent = data.likes;
                }
            });
        });
    }

    // Share button functionality
    const shareButton = document.querySelector('.share-button');
    if (shareButton) {
        shareButton.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ article.title }}',
                    text: '{{ article.summary }}',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const tempInput = document.createElement('input');
                tempInput.value = window.location.href;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('链接已复制到剪贴板');
            }
        });
    }
});
</script>
{% endblock %} 