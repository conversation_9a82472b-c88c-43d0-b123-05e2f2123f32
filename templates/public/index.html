{% extends "base.html" %}

{% block title %}BabyJourney - 您的孕育旅程好伙伴{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="hero-content">
            <h1>陪伴您的每一个孕育时刻</h1>
            <p>专业的孕期指导，贴心的育儿建议，让您的孕育之旅更轻松、更美好</p>
            <div class="hero-buttons">
                <a href="{{ url_for('main.tools') }}" class="btn btn-primary btn-lg">实用工具</a>
                <a href="#featured" class="btn btn-outline-primary btn-lg">精选文章</a>
            </div>
        </div>
        <div class="hero-image">
            <img src="{{ url_for('static', filename='img/hero-image.svg') }}" alt="孕育旅程">
        </div>
    </div>
</section>

<!-- Featured Articles -->
<section id="featured" class="featured-articles">
    <div class="container">
        <h2 class="section-title">精选文章</h2>
        <div class="articles-grid">
            {% for article in featured_articles %}
            <div class="article-card featured">
                <div class="article-image">
                    <img src="{{ article.featured_image }}" alt="{{ article.title }}">
                    {% if article.reviewed %}
                    <div class="medical-badge">
                        <i class="fas fa-check-circle"></i> 医学认证
                    </div>
                    {% endif %}
                </div>
                <div class="article-content">
                    <div class="article-category">{{ article.category.name }}</div>
                    <h3><a href="{{ url_for('main.article_detail', slug=article.slug) }}">{{ article.title }}</a></h3>
                    <p>{{ article.summary }}</p>
                    <div class="article-meta">
                        <span><i class="far fa-eye"></i> {{ article.view_count }}</span>
                        <span><i class="far fa-heart"></i> {{ article.like_count }}</span>
                        <span><i class="far fa-comment"></i> {{ article.comment_count }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Latest Articles -->
<section class="latest-articles">
    <div class="container">
        <h2 class="section-title">最新文章</h2>
        <div class="articles-grid">
            {% for article in latest_articles %}
            <div class="article-card">
                <div class="article-image">
                    <img src="{{ article.featured_image }}" alt="{{ article.title }}">
                </div>
                <div class="article-content">
                    <div class="article-category">{{ article.category.name }}</div>
                    <h3><a href="{{ url_for('main.article_detail', slug=article.slug) }}">{{ article.title }}</a></h3>
                    <p>{{ article.summary }}</p>
                    <div class="article-meta">
                        <span><i class="far fa-calendar"></i> {{ article.published_at.strftime('%Y-%m-%d') }}</span>
                        <span><i class="far fa-heart"></i> {{ article.like_count }}</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        <div class="text-center mt-4">
            <a href="{{ url_for('main.articles') }}" class="btn btn-outline-primary">查看更多文章</a>
        </div>
    </div>
</section>

<!-- Expert Section -->
<section class="expert-section">
    <div class="container">
        <h2 class="section-title">专业医疗团队</h2>
        <div class="experts-grid">
            {% for expert in experts %}
            <div class="expert-card">
                <div class="expert-image">
                    <img src="{{ expert.avatar }}" alt="{{ expert.name }}">
                </div>
                <div class="expert-content">
                    <h3>{{ expert.name }}</h3>
                    <p class="expert-title">{{ expert.title }}</p>
                    <p class="expert-description">{{ expert.description }}</p>
                    <div class="expert-stats">
                        <span><i class="fas fa-file-medical"></i> {{ expert.articles.count() }} 篇文章</span>
                        <span><i class="fas fa-star"></i> {{ expert.rating }} 评分</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Tools Preview -->
<section class="tools-preview">
    <div class="container">
        <h2 class="section-title">实用工具</h2>
        <p class="section-subtitle">基于前端计算器类库，提供专业的孕育计算工具</p>
        <div class="tools-grid">
            {% for tool in calculator_tools[:6] %}
            <div class="tool-card" data-calculator="{{ tool.calculator_class }}">
                <div class="tool-icon">
                    <i class="{{ tool.icon }}"></i>
                </div>
                <h3>{{ tool.title }}</h3>
                <p>{{ tool.description }}</p>
                <a href="{{ tool.url }}" class="btn btn-outline-primary">立即使用</a>
                <div class="tool-tech-info">
                    <small class="text-muted">
                        <i class="fas fa-code"></i> {{ tool.calculator_class }}
                    </small>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 查看更多工具 -->
        <div class="text-center mt-4">
            <a href="/tools" class="btn btn-primary btn-lg">
                <i class="fas fa-tools me-2"></i>
                查看所有工具
            </a>
        </div>

        <!-- 技术说明 -->
        <div class="tech-info-section mt-5">
            <div class="row">
                <div class="col-md-4">
                    <div class="tech-feature">
                        <i class="fas fa-laptop-code"></i>
                        <h4>前端计算</h4>
                        <p>所有计算在浏览器端执行，响应快速，保护隐私</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="tech-feature">
                        <i class="fas fa-cubes"></i>
                        <h4>模块化设计</h4>
                        <p>基于面向对象设计，每个工具独立封装，易于维护</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="tech-feature">
                        <i class="fas fa-mobile-alt"></i>
                        <h4>静态友好</h4>
                        <p>支持静态网站部署，无需服务器端支持</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
.hero-section {
    background-color: #f8f9fa;
    padding: 80px 0;
    position: relative;
    overflow: hidden;
}

.hero-content {
    max-width: 600px;
    position: relative;
    z-index: 2;
}

.hero-image {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    max-width: 50%;
    z-index: 1;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.article-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-card.featured {
    grid-column: span 2;
}

.experts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.tool-icon {
    font-size: 2.5rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.tool-tech-info {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.tech-info-section {
    background: #f8f9fa;
    padding: 3rem 0;
    border-radius: 15px;
}

.tech-feature {
    text-align: center;
    padding: 2rem 1rem;
}

.tech-feature i {
    font-size: 3rem;
    color: #007bff;
    margin-bottom: 1rem;
}

.tech-feature h4 {
    color: #333;
    margin-bottom: 1rem;
}

.section-subtitle {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 2rem;
}

.tool-card {
    text-align: center;
    padding: 30px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.medical-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(25, 135, 84, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
}

@media (max-width: 768px) {
    .hero-image {
        position: relative;
        max-width: 100%;
        margin-top: 30px;
        transform: none;
    }
    
    .article-card.featured {
        grid-column: span 1;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- 引入计算器类库 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/growth-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/due-date-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/conception-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/pregnancy-weight-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/baby-cost-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/height-predictor.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>

<script>
// 主页工具卡片交互增强
document.addEventListener('DOMContentLoaded', function() {
    // 为工具卡片添加悬停效果
    const toolCards = document.querySelectorAll('.tool-card');

    toolCards.forEach(card => {
        const calculatorClass = card.dataset.calculator;

        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        });

        // 显示计算器可用性状态
        if (calculatorClass && window[calculatorClass]) {
            const techInfo = card.querySelector('.tool-tech-info small');
            if (techInfo) {
                techInfo.innerHTML = `<i class="fas fa-check-circle text-success"></i> ${calculatorClass} 已加载`;
            }
        }
    });

    // 平滑滚动到工具部分
    const toolsButton = document.querySelector('a[href*="tools"]');
    if (toolsButton && toolsButton.getAttribute('href') === '#tools') {
        toolsButton.addEventListener('click', function(e) {
            e.preventDefault();
            document.querySelector('.tools-preview').scrollIntoView({
                behavior: 'smooth'
            });
        });
    }
});
</script>
{% endblock %}