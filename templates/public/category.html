{% extends "base.html" %}

{% block title %}{{ category.name }} - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.category-header {
    background-color: #f8f9fa;
    padding: 60px 0;
    margin-bottom: 40px;
}

.category-description {
    max-width: 800px;
    margin: 20px auto 0;
    color: #6c757d;
}

.filter-section {
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-title {
    font-size: 1.1em;
    margin-bottom: 15px;
    font-weight: 500;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-option {
    padding: 5px 15px;
    border-radius: 20px;
    background: #f8f9fa;
    color: #495057;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-option:hover,
.filter-option.active {
    background: #0d6efd;
    color: white;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.article-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-image {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.article-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-content {
    padding: 20px;
}

.article-meta {
    display: flex;
    gap: 15px;
    color: #6c757d;
    font-size: 0.9em;
    margin-top: 15px;
}

.medical-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(25, 135, 84, 0.9);
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
}

.pagination {
    margin-top: 40px;
}

@media (max-width: 768px) {
    .category-header {
        padding: 40px 0;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<header class="category-header text-center">
    <div class="container">
        <h1>{{ category.name }}</h1>
        <p class="category-description">{{ category.description }}</p>
    </div>
</header>

<div class="container">
    <div class="filter-section">
        <div class="row">
            <div class="col-md-4">
                <div class="filter-title">时间</div>
                <div class="filter-options">
                    <span class="filter-option {% if time_filter == 'all' %}active{% endif %}" data-value="all">全部</span>
                    <span class="filter-option {% if time_filter == 'week' %}active{% endif %}" data-value="week">本周</span>
                    <span class="filter-option {% if time_filter == 'month' %}active{% endif %}" data-value="month">本月</span>
                    <span class="filter-option {% if time_filter == 'year' %}active{% endif %}" data-value="year">今年</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="filter-title">排序</div>
                <div class="filter-options">
                    <span class="filter-option {% if sort == 'newest' %}active{% endif %}" data-value="newest">最新</span>
                    <span class="filter-option {% if sort == 'popular' %}active{% endif %}" data-value="popular">最热</span>
                    <span class="filter-option {% if sort == 'comments' %}active{% endif %}" data-value="comments">评论最多</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="filter-title">筛选</div>
                <div class="filter-options">
                    <span class="filter-option {% if filter == 'all' %}active{% endif %}" data-value="all">全部</span>
                    <span class="filter-option {% if filter == 'reviewed' %}active{% endif %}" data-value="reviewed">医学认证</span>
                </div>
            </div>
        </div>
    </div>

    <div class="articles-grid">
        {% for article in articles.items %}
        <div class="article-card">
            <div class="article-image">
                <img src="{{ article.featured_image or '/static/img/placeholder.jpg' }}" alt="{{ article.title }}">
                {% if article.reviewed %}
                <div class="medical-badge">
                    <i class="fas fa-check-circle"></i> 医学认证
                </div>
                {% endif %}
            </div>
            <div class="article-content">
                <h3><a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="text-decoration-none text-dark">{{ article.title }}</a></h3>
                <p class="text-muted">{{ article.summary }}</p>
                <div class="article-meta">
                    <span><i class="far fa-calendar"></i> {{ article.created_at.strftime('%Y-%m-%d') }}</span>
                    <span><i class="far fa-eye"></i> {{ article.view_count }}</span>
                    <span><i class="far fa-heart"></i> {{ article.like_count }}</span>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    {% if articles.pages > 1 %}
    <nav aria-label="Page navigation" class="d-flex justify-content-center">
        <ul class="pagination">
            {% if articles.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=articles.prev_num) }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
            {% endif %}
            
            {% for page in articles.iter_pages() %}
                {% if page %}
                    <li class="page-item {% if page == articles.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=page) }}">{{ page }}</a>
                    </li>
                {% else %}
                    <li class="page-item disabled"><span class="page-link">...</span></li>
                {% endif %}
            {% endfor %}
            
            {% if articles.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('main.category', slug=category.slug, page=articles.next_num) }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const filterOptions = document.querySelectorAll('.filter-option');
    
    filterOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Remove active class from siblings
            this.parentElement.querySelectorAll('.filter-option').forEach(sibling => {
                sibling.classList.remove('active');
            });
            
            // Add active class to clicked option
            this.classList.add('active');
            
            // Get all filter values
            const timeFilter = document.querySelector('.filter-options .filter-option.active[data-value^="all,week,month,year"]').dataset.value;
            const sort = document.querySelector('.filter-options .filter-option.active[data-value^="newest,popular,comments"]').dataset.value;
            const filter = document.querySelector('.filter-options .filter-option.active[data-value^="all,reviewed"]').dataset.value;
            
            // Update URL with new filter parameters
            const url = new URL(window.location.href);
            url.searchParams.set('time', timeFilter);
            url.searchParams.set('sort', sort);
            url.searchParams.set('filter', filter);
            url.searchParams.set('page', '1'); // Reset to first page when filters change
            
            // Navigate to new URL
            window.location.href = url.toString();
        });
    });
});
</script>
{% endblock %} 