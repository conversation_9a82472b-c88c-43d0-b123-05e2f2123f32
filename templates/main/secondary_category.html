{% extends "base.html" %}

{% block title %}{{ secondary_category.name }}{% endblock %}

{% block content %}
<!-- 二级分类标题 -->
<div class="container text-center py-4">
  <a href="/category/{{ primary_category.slug }}" class="text-decoration-none text-muted small">{{ primary_category.name }}</a>
  <h1 class="display-4 mt-2">{{ secondary_category.name }}</h1>
</div>

<!-- 三级分类导航 -->
<div class="container border-top border-bottom py-3">
  <div class="row">
    <div class="col-12">
      <ul class="nav nav-pills nav-fill">
        {% for category in categories %}
          <li class="nav-item">
            <a class="nav-link" href="#category-{{ category.id }}">{{ category.name }}</a>
          </li>
        {% endfor %}
      </ul>
    </div>
  </div>
</div>

<!-- 三级分类内容区块 -->
{% for category in categories %}
<div class="container py-5" id="category-{{ category.id }}">
  <div class="row mb-4">
    <div class="col-12">
      <h2>{{ category.name }}</h2>
      {% if category.description %}
        <p class="text-muted">{{ category.description }}</p>
      {% endif %}
    </div>
  </div>
  
  <!-- 文章卡片 - 前4篇 -->
  <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
    {% set category_articles = articles_by_category.get(category.id, []) %}
    {% for article in category_articles[:4] %}
      <div class="col">
        <div class="card h-100 border-0 shadow-sm">
          {% if article.featured_image %}
            <img src="{{ article.featured_image }}" class="card-img-top" alt="{{ article.title }}">
          {% else %}
            <img src="{{ url_for('static', filename='img/placeholder.jpg') }}" class="card-img-top" alt="{{ article.title }}">
          {% endif %}
          <div class="card-body">
            <h5 class="card-title">{{ article.title }}</h5>
            <p class="card-text small text-muted">
              {% if article.author %}
                由 {{ article.author.display_name or article.author.username }} 审核
              {% endif %}
            </p>
          </div>
          <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="stretched-link"></a>
        </div>
      </div>
    {% else %}
      <div class="col-12">
        <div class="alert alert-light">
          暂无相关文章。
        </div>
      </div>
    {% endfor %}
  </div>
  
  {% if category_articles|length > 4 %}
    <!-- 可展开的更多文章区域 -->
    <div class="collapse mt-4" id="moreArticles-{{ category.id }}">
      <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
        {% for article in category_articles[4:] %}
          <div class="col">
            <div class="card h-100 border-0 shadow-sm">
              {% if article.featured_image %}
                <img src="{{ article.featured_image }}" class="card-img-top" alt="{{ article.title }}">
              {% else %}
                <img src="{{ url_for('static', filename='img/placeholder.jpg') }}" class="card-img-top" alt="{{ article.title }}">
              {% endif %}
              <div class="card-body">
                <h5 class="card-title">{{ article.title }}</h5>
                <p class="card-text small text-muted">
                  {% if article.author %}
                    由 {{ article.author.display_name or article.author.username }} 审核
                  {% endif %}
                </p>
              </div>
              <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="stretched-link"></a>
            </div>
          </div>
        {% endfor %}
      </div>
    </div>
    
    <!-- 显示/隐藏更多按钮 -->
    <div class="row mt-4">
      <div class="col-12 text-center">
        <button class="btn btn-outline-secondary rounded-pill px-4 toggle-more-btn" 
                data-bs-toggle="collapse" 
                data-bs-target="#moreArticles-{{ category.id }}" 
                aria-expanded="false"
                data-category-id="{{ category.id }}">
          显示更多 <i class="fas fa-chevron-down ms-1"></i>
        </button>
      </div>
    </div>
  {% endif %}
</div>
{% endfor %}

<!-- 最新文章区块 -->
<div class="container py-5 bg-light">
  <div class="row mb-4">
    <div class="col-12">
      <h2 class="text-center">最新文章</h2>
    </div>
  </div>
  
  <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-4">
    {% for article in latest_articles %}
      <div class="col">
        <div class="card h-100 border-0 shadow-sm">
          {% if article.featured_image %}
            <img src="{{ article.featured_image }}" class="card-img-top" alt="{{ article.title }}">
          {% else %}
            <img src="{{ url_for('static', filename='img/placeholder.jpg') }}" class="card-img-top" alt="{{ article.title }}">
          {% endif %}
          <div class="card-body">
            <h5 class="card-title">{{ article.title }}</h5>
            <p class="card-text small text-muted">
              {% if article.author %}
                由 {{ article.author.display_name or article.author.username }} 审核
              {% endif %}
            </p>
          </div>
          <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="stretched-link"></a>
        </div>
      </div>
    {% endfor %}
  </div>
  
  <div class="row mt-4">
    <div class="col-12 text-center">
      <a href="{{ url_for('main.articles') }}" class="btn btn-outline-secondary rounded-pill px-4">
        浏览全部文章 <i class="fas fa-arrow-right ms-1"></i>
      </a>
    </div>
  </div>
</div>

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // 为所有显示更多按钮添加点击事件
    const toggleButtons = document.querySelectorAll('.toggle-more-btn');
    
    toggleButtons.forEach(button => {
      button.addEventListener('click', function() {
        const expanded = this.getAttribute('aria-expanded') === 'true';
        const categoryId = this.getAttribute('data-category-id');
        
        // 更新按钮文本和图标
        if (expanded) {
          this.innerHTML = '显示更多 <i class="fas fa-chevron-down ms-1"></i>';
        } else {
          this.innerHTML = '收起 <i class="fas fa-chevron-up ms-1"></i>';
        }
      });
    });
  });
</script>
{% endblock %}

{% endblock %} 