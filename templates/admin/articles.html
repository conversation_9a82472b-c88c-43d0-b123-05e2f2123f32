{% extends "admin/base.html" %}

{% block title %}文章管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">文章管理</h1>
        <a href="{{ url_for('admin.create_article') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加文章
        </a>
    </div>

    <!-- 筛选器 -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url_for('admin.articles') }}" method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">状态</label>
                    <select name="status" class="form-select">
                        <option value="all" {% if status == 'all' %}selected{% endif %}>全部</option>
                        <option value="published" {% if status == 'published' %}selected{% endif %}>已发布</option>
                        <option value="draft" {% if status == 'draft' %}selected{% endif %}>草稿</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">分类</label>
                    <select name="category" class="form-select">
                        <option value="">全部分类</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if category_id == category.id %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">标签</label>
                    <select name="tag" class="form-select">
                        <option value="">全部标签</option>
                        {% for tag in tags %}
                        <option value="{{ tag.id }}" {% if tag_id == tag.id %}selected{% endif %}>{{ tag.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">搜索</label>
                    <div class="input-group">
                        <input type="text" name="search" class="form-control" placeholder="标题关键词" value="{{ search }}">
                        <button type="submit" class="btn btn-primary">筛选</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 文章列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>标题</th>
                            <th>分类</th>
                            <th>作者</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for article in articles %}
                        <tr>
                            <td>{{ article.id }}</td>
                            <td>
                                {{ article.title }}
                                {% if article.featured %}
                                <span class="badge bg-success">推荐</span>
                                {% endif %}
                            </td>
                            <td>{{ article.category.name if article.category else '无分类' }}</td>
                            <td>{{ article.author.username if article.author else '未知' }}</td>
                            <td>
                                {% if article.published %}
                                <span class="badge bg-primary">已发布</span>
                                {% else %}
                                <span class="badge bg-secondary">草稿</span>
                                {% endif %}
                            </td>
                            <td>{{ article.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('main.article_detail', slug=article.slug) }}" class="btn btn-sm btn-outline-secondary" target="_blank">预览</a>
                                    <a href="{{ url_for('admin.edit_article', id=article.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ article.id }}">删除</button>
                                </div>
                                
                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ article.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ article.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ article.id }}">确认删除</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>您确定要删除文章"{{ article.title }}"吗？此操作无法撤销。</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <form action="{{ url_for('admin.delete_article', id=article.id) }}" method="post" class="d-inline">
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if not articles %}
            <div class="text-center py-4">
                <p class="text-muted">暂无文章，请点击"添加文章"按钮创建新文章。</p>
            </div>
            {% endif %}
            
            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in pagination.iter_pages() %}
                        {% if page %}
                            {% if page != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.articles', page=page, status=status, category=category_id, tag=tag_id, search=search) }}">{{ page }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 