{% extends "admin/base.html" %}

{% block title %}{% if is_edit %}编辑分类{% else %}创建分类{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4>{% if is_edit %}编辑分类{% else %}创建分类{% endif %}</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.secondary_category_id.label(class="form-label") }}
                            {{ form.secondary_category_id(class="form-control" + (" is-invalid" if form.secondary_category_id.errors else "")) }}
                            {% if form.secondary_category_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.secondary_category_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            {{ form.slug.label(class="form-label") }}
                            {{ form.slug(class="form-control" + (" is-invalid" if form.slug.errors else "")) }}
                            {% if form.slug.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.slug.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text text-muted">用于URL的别名，只能包含小写字母、数字和短横线</div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label(class="form-label") }}
                            {{ form.description(class="form-control", rows=3) }}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.categories') }}" class="btn btn-secondary">返回</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const nameInput = document.getElementById('name');
        const slugInput = document.getElementById('slug');
        
        // 生成slug的函数
        function generateSlug(text) {
            return text.toLowerCase()
                .replace(/\s+/g, '-')           // 替换空格为短横线
                .replace(/[^\w\-]+/g, '')       // 移除非字母数字
                .replace(/\-\-+/g, '-')         // 替换多个短横线为单个短横线
                .replace(/^-+/, '')             // 去除开头的短横线
                .replace(/-+$/, '');            // 去除结尾的短横线
        }
        
        // 如果name字段发生变化，且slug为空或未修改过，则自动生成slug
        if (nameInput && slugInput) {
            nameInput.addEventListener('input', function() {
                if (!slugInput.dataset.modified) {
                    slugInput.value = generateSlug(nameInput.value);
                }
            });
            
            // 标记slug是否被手动修改过
            slugInput.addEventListener('input', function() {
                slugInput.dataset.modified = 'true';
            });
        }
    });
</script>
{% endblock %} 