<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>{% if title %}{{ title }} - {% endif %}{{ site_name }} 管理后台</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.5/css/dataTables.bootstrap5.min.css">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
    <!-- Summernote CSS -->
    <link href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.css" rel="stylesheet">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/admin.css') }}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row flex-nowrap">
            <!-- 侧边栏 -->
            <div class="col-auto col-md-3 col-xl-2 px-sm-2 px-0 bg-dark">
                <div class="d-flex flex-column align-items-center align-items-sm-start px-3 pt-2 text-white min-vh-100">
                    <a href="{{ url_for('admin.index') }}" class="d-flex align-items-center pb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <span class="fs-5 d-none d-sm-inline">{{ site_name }} 后台</span>
                    </a>
                    <ul class="nav nav-pills flex-column mb-sm-auto mb-0 align-items-center align-items-sm-start" id="menu">
                        <li class="nav-item">
                            <a href="{{ url_for('admin.index') }}" class="nav-link align-middle px-0 {% if request.endpoint == 'admin.index' %}active{% endif %}">
                                <i class="fs-4 bi-speedometer2"></i> <span class="ms-1 d-none d-sm-inline">仪表盘</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.articles') }}" class="nav-link px-0 align-middle {% if 'admin.articles' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-file-text"></i> <span class="ms-1 d-none d-sm-inline">文章管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="#submenu1" data-bs-toggle="collapse" class="nav-link px-0 align-middle {% if 'admin.categories' in request.endpoint or 'admin.primary_categories' in request.endpoint or 'admin.secondary_categories' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-folder"></i> <span class="ms-1 d-none d-sm-inline">分类管理</span>
                            </a>
                            <ul class="collapse {% if 'admin.categories' in request.endpoint or 'admin.primary_categories' in request.endpoint or 'admin.secondary_categories' in request.endpoint %}show{% endif %} nav flex-column ms-1" id="submenu1" data-bs-parent="#menu">
                                <li class="w-100">
                                    <a href="{{ url_for('admin.categories') }}" class="nav-link px-0 {% if 'admin.categories' in request.endpoint and not 'primary' in request.endpoint and not 'secondary' in request.endpoint %}active{% endif %}">
                                        <i class="fs-5 bi-list-nested"></i> <span class="d-none d-sm-inline">分类管理</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('admin.primary_categories') }}" class="nav-link px-0 {% if 'admin.primary_categories' in request.endpoint %}active{% endif %}">
                                        <i class="fs-5 bi-list-ul"></i> <span class="d-none d-sm-inline">一级分类</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ url_for('admin.secondary_categories') }}" class="nav-link px-0 {% if 'admin.secondary_categories' in request.endpoint %}active{% endif %}">
                                        <i class="fs-5 bi-list"></i> <span class="d-none d-sm-inline">二级分类</span>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.tags') }}" class="nav-link px-0 align-middle {% if 'admin.tags' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-tags"></i> <span class="ms-1 d-none d-sm-inline">标签管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.comments') }}" class="nav-link px-0 align-middle {% if 'admin.comments' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-chat-dots"></i> <span class="ms-1 d-none d-sm-inline">评论管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.media') }}" class="nav-link px-0 align-middle {% if 'admin.media' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-images"></i> <span class="ms-1 d-none d-sm-inline">媒体管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.users') }}" class="nav-link px-0 align-middle {% if 'admin.users' in request.endpoint %}active{% endif %}">
                                <i class="fs-4 bi-people"></i> <span class="ms-1 d-none d-sm-inline">用户管理</span>
                            </a>
                        </li>
                        <li>
                            <a href="{{ url_for('admin.settings') }}" class="nav-link px-0 align-middle {% if request.endpoint == 'admin.settings' %}active{% endif %}">
                                <i class="fs-4 bi-gear"></i> <span class="ms-1 d-none d-sm-inline">系统设置</span>
                            </a>
                        </li>
                        <li class="mt-3">
                            <a href="{{ url_for('main.index') }}" class="nav-link px-0 align-middle text-warning">
                                <i class="fs-4 bi-house"></i> <span class="ms-1 d-none d-sm-inline">返回网站</span>
                            </a>
                        </li>
                    </ul>
                    <hr>
                    <div class="dropdown pb-4">
                        <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser1" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fs-4 bi-person-circle"></i>
                            <span class="d-none d-sm-inline mx-1">{{ current_user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
                            <li><a class="dropdown-item" href="{{ url_for('auth.profile') }}">个人资料</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">退出登录</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 主要内容区 -->
            <div class="col py-3">
                <div class="container">
                    <!-- 标题和面包屑导航 -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="mb-0">{% block page_title %}管理后台{% endblock %}</h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                {% block breadcrumb %}
                                <li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">首页</a></li>
                                <li class="breadcrumb-item active" aria-current="page">仪表盘</li>
                                {% endblock %}
                            </ol>
                        </nav>
                    </div>
                    
                    <!-- 闪现消息 -->
                    {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                            {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            {% endfor %}
                        {% endif %}
                    {% endwith %}
                    
                    <!-- 主内容 -->
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 确认删除对话框 -->
    <div class="modal fade" id="confirmDeleteModal" tabindex="-1" aria-labelledby="confirmDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmDeleteModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>确定要删除该项吗？此操作无法撤销。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <form id="deleteForm" method="post" action="">
                        <button type="submit" class="btn btn-danger">删除</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript 依赖 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-lite.min.js"></script>
    
    <script>
        // 删除确认对话框
        document.addEventListener('DOMContentLoaded', function() {
            const confirmDeleteModal = document.getElementById('confirmDeleteModal');
            if (confirmDeleteModal) {
                confirmDeleteModal.addEventListener('show.bs.modal', function(event) {
                    const button = event.relatedTarget;
                    const deleteUrl = button.getAttribute('data-bs-delete-url');
                    document.getElementById('deleteForm').action = deleteUrl;
                });
            }
            
            // 初始化数据表格
            if (typeof $.fn.DataTable !== 'undefined') {
                $('.datatable').DataTable({
                    "language": {
                        "lengthMenu": "每页显示 _MENU_ 条记录",
                        "zeroRecords": "没有找到记录",
                        "info": "第 _PAGE_ 页，共 _PAGES_ 页",
                        "infoEmpty": "没有记录",
                        "infoFiltered": "(从 _MAX_ 条记录过滤)",
                        "search": "搜索:",
                        "paginate": {
                            "first": "首页",
                            "last": "末页",
                            "next": "下一页",
                            "previous": "上一页"
                        }
                    }
                });
            }
            
            // 初始化富文本编辑器
            if (typeof $.fn.summernote !== 'undefined') {
                $('.summernote').summernote({
                    placeholder: '请输入内容',
                    height: 300,
                    lang: 'zh-CN',
                    toolbar: [
                        ['style', ['style']],
                        ['font', ['bold', 'underline', 'clear']],
                        ['color', ['color']],
                        ['para', ['ul', 'ol', 'paragraph']],
                        ['table', ['table']],
                        ['insert', ['link', 'picture']],
                        ['view', ['fullscreen', 'codeview', 'help']]
                    ]
                });
            }
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html> 