{% extends "admin/base.html" %}

{% block title %}一级分类管理{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="mb-0">一级分类管理</h5>
          <a href="{{ url_for('admin.create_primary_category') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加一级分类
          </a>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>名称</th>
                  <th>别名</th>
                  <th>图标</th>
                  <th>排序</th>
                  <th>可见性</th>
                  <th>二级分类数</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                {% for category in primary_categories %}
                <tr>
                  <td>{{ category.id }}</td>
                  <td>{{ category.name }}</td>
                  <td>{{ category.slug }}</td>
                  <td>
                    {% if category.icon %}
                    <i class="{{ category.icon }}"></i> {{ category.icon }}
                    {% else %}
                    <span class="text-muted">无</span>
                    {% endif %}
                  </td>
                  <td>{{ category.order }}</td>
                  <td>
                    {% if category.is_visible %}
                    <span class="badge bg-success">显示</span>
                    {% else %}
                    <span class="badge bg-secondary">隐藏</span>
                    {% endif %}
                  </td>
                  <td>{{ category.secondary_categories.count() }}</td>
                  <td>
                    <div class="btn-group btn-group-sm">
                      <a href="{{ url_for('admin.edit_primary_category', id=category.id) }}" class="btn btn-info">
                        <i class="fas fa-edit"></i> 编辑
                      </a>
                      {% if category.secondary_categories.count() == 0 %}
                      <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ category.id }}">
                        <i class="fas fa-trash"></i> 删除
                      </button>
                      {% else %}
                      <button type="button" class="btn btn-danger" disabled title="存在关联的二级分类，无法删除">
                        <i class="fas fa-trash"></i> 删除
                      </button>
                      {% endif %}
                    </div>
                    
                    <!-- 删除确认对话框 -->
                    <div class="modal fade" id="deleteModal{{ category.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ category.id }}" aria-hidden="true">
                      <div class="modal-dialog">
                        <div class="modal-content">
                          <div class="modal-header">
                            <h5 class="modal-title" id="deleteModalLabel{{ category.id }}">确认删除</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                          </div>
                          <div class="modal-body">
                            确定要删除一级分类 "{{ category.name }}" 吗？此操作无法撤销。
                          </div>
                          <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <form action="{{ url_for('admin.delete_primary_category', id=category.id) }}" method="POST">
                              <button type="submit" class="btn btn-danger">确认删除</button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="8" class="text-center">暂无一级分类</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 