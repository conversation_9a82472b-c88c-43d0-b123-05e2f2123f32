{% extends "admin/base.html" %}

{% block title %}编辑一级分类{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">编辑一级分类</h5>
        </div>
        <div class="card-body">
          <form method="post" action="{{ url_for('admin.edit_primary_category', id=primary_category.id) }}">
            <div class="mb-3">
              <label for="name" class="form-label">分类名称 <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="name" name="name" value="{{ primary_category.name }}" required>
              <div class="form-text">显示在导航菜单和页面中的名称</div>
            </div>
            
            <div class="mb-3">
              <label for="slug" class="form-label">分类别名 <span class="text-danger">*</span></label>
              <input type="text" class="form-control" id="slug" name="slug" value="{{ primary_category.slug }}" required>
              <div class="form-text">用于URL的唯一标识，只能包含字母、数字和连字符，如"pregnancy"</div>
            </div>
            
            <div class="mb-3">
              <label for="description" class="form-label">分类描述</label>
              <textarea class="form-control" id="description" name="description" rows="3">{{ primary_category.description or '' }}</textarea>
              <div class="form-text">对该分类的简短描述，可以显示在页面中</div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="icon" class="form-label">图标类名</label>
                  <input type="text" class="form-control" id="icon" name="icon" value="{{ primary_category.icon or '' }}">
                  <div class="form-text">Bootstrap或FontAwesome图标类，如"bi bi-heart"</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="image" class="form-label">图片URL</label>
                  <input type="text" class="form-control" id="image" name="image" value="{{ primary_category.image or '' }}">
                  <div class="form-text">分类图片的URL地址</div>
                </div>
              </div>
            </div>
            
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="order" class="form-label">排序序号</label>
                  <input type="number" class="form-control" id="order" name="order" value="{{ primary_category.order }}">
                  <div class="form-text">数字越小排序越靠前</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3 form-check mt-4">
                  <input type="checkbox" class="form-check-input" id="is_visible" name="is_visible" {% if primary_category.is_visible %}checked{% endif %}>
                  <label class="form-check-label" for="is_visible">在导航中显示</label>
                </div>
              </div>
            </div>
            
            <div class="d-flex justify-content-between">
              <a href="{{ url_for('admin.primary_categories') }}" class="btn btn-secondary">返回列表</a>
              <button type="submit" class="btn btn-primary">保存修改</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} 