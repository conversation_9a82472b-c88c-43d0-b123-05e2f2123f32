{% extends "admin/base.html" %}

{% block title %}分类管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">分类管理</h1>
        <a href="{{ url_for('admin.create_category') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加分类
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>二级分类</th>
                            <th>名称</th>
                            <th>别名</th>
                            <th>文章数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for category in categories %}
                        <tr>
                            <td>{{ category.id }}</td>
                            <td>{{ category.secondary_category.name if category.secondary_category else '-' }}</td>
                            <td>{{ category.name }}</td>
                            <td>{{ category.slug }}</td>
                            <td>{{ category.articles.count() }}</td>
                            <td>
                                <a href="{{ url_for('admin.edit_category', id=category.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ category.id }}">删除</button>
                                
                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ category.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ category.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ category.id }}">确认删除</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>您确定要删除"{{ category.name }}"分类吗？此操作无法撤销。</p>
                                                <p class="text-danger">注意：该分类下的所有文章将被取消分类关联。</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <form action="{{ url_for('admin.delete_category', id=category.id) }}" method="post" class="d-inline">
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if not categories %}
            <div class="text-center py-4">
                <p class="text-muted">暂无分类，请点击"添加分类"按钮创建新分类。</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 