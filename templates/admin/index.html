{% extends "admin/base.html" %}

{% block page_title %}仪表盘{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 刷新自动内链缓存按钮
        const refreshAutolinksBtn = document.getElementById('refreshAutolinksBtn');
        if (refreshAutolinksBtn) {
            refreshAutolinksBtn.addEventListener('click', function() {
                // 显示加载状态
                const originalText = refreshAutolinksBtn.innerHTML;
                refreshAutolinksBtn.disabled = true;
                refreshAutolinksBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 正在刷新...';
                
                // 发送请求刷新缓存
                fetch('{{ url_for("admin.refresh_autolinks") }}', {
                    method: 'POST',
                })
                .then(response => response.json())
                .then(data => {
                    // 显示成功提示
                    if (data.status === 'success') {
                        alert('自动内链缓存已成功刷新！');
                    } else {
                        alert('刷新缓存失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('刷新自动内链缓存出错:', error);
                    alert('刷新缓存时发生错误，请查看控制台日志');
                })
                .finally(() => {
                    // 恢复按钮状态
                    refreshAutolinksBtn.disabled = false;
                    refreshAutolinksBtn.innerHTML = originalText;
                });
            });
        }
    });
</script>
{% endblock %}

{% block content %}
<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">文章总数</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.article_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">已发布</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.published_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">草稿</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.draft_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-pen fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">待审核评论</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.pending_comments }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-comments fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 最新文章 -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">最新文章</h6>
                <a href="{{ url_for('admin.articles') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if latest_articles %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>发布状态</th>
                                <th>创建时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for article in latest_articles %}
                            <tr>
                                <td><a href="{{ url_for('admin.edit_article', id=article.id) }}">{{ article.title }}</a></td>
                                <td>
                                    {% if article.published %}
                                    <span class="badge bg-success">已发布</span>
                                    {% else %}
                                    <span class="badge bg-secondary">草稿</span>
                                    {% endif %}
                                </td>
                                <td>{{ article.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-muted mb-0">暂无文章</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- 待审核评论 -->
    <div class="col-lg-6">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">待审核评论</h6>
                <a href="{{ url_for('admin.comments', status='pending') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body">
                {% if pending_comments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>内容</th>
                                <th>作者</th>
                                <th>文章</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for comment in pending_comments %}
                            <tr>
                                <td>{{ comment.content|truncate(30) }}</td>
                                <td>{{ comment.user.username if comment.user else '游客' }}</td>
                                <td>
                                    <a href="{{ url_for('main.article', slug=comment.article.slug) }}" target="_blank">
                                        {{ comment.article.title|truncate(20) }}
                                    </a>
                                </td>
                                <td>
                                    <form method="post" action="{{ url_for('admin.approve_comment', id=comment.id) }}" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-success">通过</button>
                                    </form>
                                    <form method="post" action="{{ url_for('admin.reject_comment', id=comment.id) }}" class="d-inline">
                                        <button type="submit" class="btn btn-sm btn-danger">拒绝</button>
                                    </form>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <p class="text-muted mb-0">暂无待审核评论</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 统计信息 -->
<div class="row">
    <!-- 快捷操作区 -->
    <div class="col-lg-12 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快捷操作</h6>
            </div>
            <div class="card-body">
                <button id="refreshAutolinksBtn" class="btn btn-outline-primary me-2">
                    <i class="fas fa-sync-alt"></i> 刷新自动内链缓存
                </button>
            </div>
        </div>
    </div>

    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">系统概况</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h1 class="display-4">{{ stats.category_count }}</h1>
                                <p class="card-text">分类</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h1 class="display-4">{{ stats.tag_count }}</h1>
                                <p class="card-text">标签</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h1 class="display-4">{{ stats.user_count }}</h1>
                                <p class="card-text">用户</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h1 class="display-4">{{ stats.media_count }}</h1>
                                <p class="card-text">媒体文件</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">快速操作</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.create_article') }}" class="btn btn-primary btn-lg btn-block w-100 py-3">
                            <i class="fas fa-plus fa-lg mb-2 d-block"></i>
                            创建文章
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.create_category') }}" class="btn btn-success btn-lg btn-block w-100 py-3">
                            <i class="fas fa-folder-plus fa-lg mb-2 d-block"></i>
                            添加分类
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.upload_media') }}" class="btn btn-info btn-lg btn-block w-100 py-3">
                            <i class="fas fa-upload fa-lg mb-2 d-block"></i>
                            上传媒体
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.settings') }}" class="btn btn-secondary btn-lg btn-block w-100 py-3">
                            <i class="fas fa-cog fa-lg mb-2 d-block"></i>
                            系统设置
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 