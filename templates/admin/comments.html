{% extends "admin/base.html" %}

{% block title %}评论管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">评论管理</h1>
    </div>

    <!-- 筛选器 -->
    <div class="card mb-4">
        <div class="card-body">
            <form action="{{ url_for('admin.comments') }}" method="get" class="row g-3">
                <div class="col-md-4">
                    <label class="form-label">状态</label>
                    <select name="status" class="form-select">
                        <option value="all" {% if status == 'all' %}selected{% endif %}>全部</option>
                        <option value="approved" {% if status == 'approved' %}selected{% endif %}>已审核</option>
                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>待审核</option>
                    </select>
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">筛选</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 评论列表 -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>文章</th>
                            <th>用户</th>
                            <th>内容</th>
                            <th>时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for comment in comments %}
                        <tr>
                            <td>{{ comment.id }}</td>
                            <td>
                                <a href="{{ url_for('main.article', slug=comment.article.slug) }}" target="_blank">
                                    {{ comment.article.title }}
                                </a>
                            </td>
                            <td>{{ comment.user.username }}</td>
                            <td>{{ comment.content|truncate(50) }}</td>
                            <td>{{ comment.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>
                                {% if comment.approved %}
                                <span class="badge bg-success">已审核</span>
                                {% else %}
                                <span class="badge bg-warning">待审核</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if not comment.approved %}
                                <form action="{{ url_for('admin.approve_comment', id=comment.id) }}" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-success">通过</button>
                                </form>
                                {% endif %}
                                <form action="{{ url_for('admin.reject_comment', id=comment.id) }}" method="post" class="d-inline">
                                    <button type="submit" class="btn btn-sm btn-danger">删除</button>
                                </form>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            {% if not comments %}
            <div class="text-center py-4">
                <p class="text-muted">暂无评论。</p>
            </div>
            {% endif %}
            
            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in pagination.iter_pages() %}
                        {% if page %}
                            {% if page != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.comments', page=page, status=status) }}">{{ page }}</a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 