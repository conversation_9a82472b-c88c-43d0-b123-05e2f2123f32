{% extends "admin/base.html" %}

{% block title %}标签管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">标签管理</h1>
        <a href="{{ url_for('admin.create_tag') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> 添加标签
        </a>
    </div>

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>名称</th>
                            <th>文章数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tag in tags %}
                        <tr>
                            <td>{{ tag.id }}</td>
                            <td>{{ tag.name }}</td>
                            <td>{{ tag.articles|length }}</td>
                            <td>
                                <a href="{{ url_for('admin.edit_tag', id=tag.id) }}" class="btn btn-sm btn-outline-primary">编辑</a>
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal{{ tag.id }}">删除</button>
                                
                                <!-- 删除确认模态框 -->
                                <div class="modal fade" id="deleteModal{{ tag.id }}" tabindex="-1" aria-labelledby="deleteModalLabel{{ tag.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="deleteModalLabel{{ tag.id }}">确认删除</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>您确定要删除"{{ tag.name }}"标签吗？此操作无法撤销。</p>
                                                <p class="text-danger">注意：该标签将从所有关联的文章中移除。</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                <form action="{{ url_for('admin.delete_tag', id=tag.id) }}" method="post" class="d-inline">
                                                    <button type="submit" class="btn btn-danger">确认删除</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if not tags %}
            <div class="text-center py-4">
                <p class="text-muted">暂无标签，请点击"添加标签"按钮创建新标签。</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 