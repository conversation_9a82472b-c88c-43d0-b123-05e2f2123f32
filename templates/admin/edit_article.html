{% extends "admin/base.html" %}

{% block title %}{% if is_edit %}编辑文章{% else %}创建文章{% endif %}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.css">
<style>
    .note-editor {
        margin-bottom: 1rem;
    }
    .tag-input {
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-10 offset-md-1">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4>{% if is_edit %}编辑文章{% else %}创建文章{% endif %}</h4>
                    <div>
                        <a href="{{ url_for('admin.articles') }}" class="btn btn-secondary">返回列表</a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {{ form.csrf_token }}
                        {{ form.content_blocks(id="content-blocks-data") }}
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    {{ form.title.label(class="form-label") }}
                                    {{ form.title(class="form-control" + (" is-invalid" if form.title.errors else ""), id="title-input") }}
                                    {% if form.title.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.title.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.slug.label(class="form-label") }}
                                    {{ form.slug(class="form-control" + (" is-invalid" if form.slug.errors else ""), id="slug-input") }}
                                    {% if form.slug.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.slug.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text text-muted">用于URL的别名，只能包含小写字母、数字和短横线</div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">文章内容</label>
                                    <div id="editor-container" class="border rounded">
                                        <div id="editor-toolbar" class="bg-light p-2 border-bottom">
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="heading">标题</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="subheading">小标题</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="paragraph">段落</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="image">图片</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="list">列表</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="quote">引用</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" data-block-type="key_takeaways">要点</button>
                                        </div>
                                        <div id="editor-blocks" class="p-3">
                                            <!-- 内容块将在此动态显示 -->
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.summary.label(class="form-label") }}
                                    {{ form.summary(class="form-control", rows=3) }}
                                    <div class="form-text text-muted">文章摘要，显示在列表页面</div>
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-header">文章设置</div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            {{ form.category_id.label(class="form-label") }}
                                            {{ form.category_id(class="form-select") }}
                                        </div>
                                        
                                        <div class="mb-3">
                                            {{ form.tags.label(class="form-label") }}
                                            {{ form.tags(class="form-control tag-input") }}
                                            <div class="form-text text-muted">用逗号分隔多个标签</div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            {{ form.cover_image.label(class="form-label") }}
                                            {{ form.cover_image(class="form-control") }}
                                            {% if article and article.featured_image %}
                                                <div class="mt-2">
                                                    <img src="{{ article.featured_image }}" class="img-thumbnail" style="max-width: 100px;">
                                                </div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                {{ form.published(class="form-check-input") }}
                                                {{ form.published.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                {{ form.featured(class="form-check-input") }}
                                                {{ form.featured.label(class="form-check-label") }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card mb-3">
                                    <div class="card-header">SEO设置</div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            {{ form.meta_title.label(class="form-label") }}
                                            {{ form.meta_title(class="form-control") }}
                                        </div>
                                        
                                        <div class="mb-3">
                                            {{ form.meta_description.label(class="form-label") }}
                                            {{ form.meta_description(class="form-control", rows=2) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between mt-3">
                            <a href="{{ url_for('admin.articles') }}" class="btn btn-secondary">取消</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script src="https://cdn.jsdelivr.net/npm/summernote@0.8.18/dist/summernote-bs4.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const titleInput = document.getElementById('title-input');
        const slugInput = document.getElementById('slug-input');
        const contentBlocksData = document.getElementById('content-blocks-data');
        const editorBlocks = document.getElementById('editor-blocks');
        
        // 初始化内容编辑器
        let contentBlocks = [];
        try {
            if (contentBlocksData.value) {
                contentBlocks = JSON.parse(contentBlocksData.value);
            }
        } catch (e) {
            console.error('Error parsing content blocks:', e);
        }
        
        // 如果没有内容块或解析错误，添加一个默认的段落块
        if (!contentBlocks || !contentBlocks.length) {
            contentBlocks = [
                { type: 'paragraph', content: '' }
            ];
        }
        
        // 渲染内容块
        renderContentBlocks();
        
        // 添加新的内容块
        document.querySelectorAll('#editor-toolbar button').forEach(button => {
            button.addEventListener('click', function() {
                const blockType = this.getAttribute('data-block-type');
                addContentBlock(blockType);
            });
        });
        
        // 生成slug的函数
        function generateSlug(text) {
            return text.toLowerCase()
                .replace(/\s+/g, '-')           // 替换空格为短横线
                .replace(/[^\w\-]+/g, '')       // 移除非字母数字
                .replace(/\-\-+/g, '-')         // 替换多个短横线为单个短横线
                .replace(/^-+/, '')             // 去除开头的短横线
                .replace(/-+$/, '');            // 去除结尾的短横线
        }
        
        // 如果title字段发生变化，且slug为空，则自动生成slug
        if (titleInput && slugInput) {
            titleInput.addEventListener('input', function() {
                if (!slugInput.dataset.modified) {
                    slugInput.value = generateSlug(titleInput.value);
                }
            });
            
            // 标记slug是否被手动修改过
            slugInput.addEventListener('input', function() {
                slugInput.dataset.modified = 'true';
            });
        }
        
        // 渲染内容块
        function renderContentBlocks() {
            editorBlocks.innerHTML = '';
            
            contentBlocks.forEach((block, index) => {
                const blockElement = createBlockElement(block, index);
                editorBlocks.appendChild(blockElement);
            });
            
            updateContentBlocksData();
        }
        
        // 创建内容块元素
        function createBlockElement(block, index) {
            const blockElement = document.createElement('div');
            blockElement.className = 'content-block mb-3 p-2 border rounded';
            blockElement.dataset.index = index;
            
            let contentHTML = '';
            
            switch (block.type) {
                case 'heading':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">标题</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <input type="text" class="form-control block-content" value="${block.content || ''}" placeholder="标题内容">
                    `;
                    break;
                    
                case 'subheading':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">小标题</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <input type="text" class="form-control block-content" value="${block.content || ''}" placeholder="小标题内容">
                    `;
                    break;
                    
                case 'paragraph':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">段落</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <textarea class="form-control block-content" rows="3" placeholder="段落内容">${block.content || ''}</textarea>
                    `;
                    break;
                    
                case 'image':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">图片</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <div class="mb-2">
                            <input type="text" class="form-control block-image-url" value="${block.content || ''}" placeholder="图片URL">
                        </div>
                        <input type="text" class="form-control block-image-caption" value="${block.caption || ''}" placeholder="图片说明">
                    `;
                    break;
                    
                case 'list':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">列表</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <textarea class="form-control block-content" rows="3" placeholder="每行一个列表项">${block.content || ''}</textarea>
                    `;
                    break;
                    
                case 'quote':
                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">引用</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <textarea class="form-control block-content" rows="2" placeholder="引用内容">${block.content || ''}</textarea>
                        <input type="text" class="form-control mt-2 block-quote-source" value="${block.source || ''}" placeholder="引用来源">
                    `;
                    break;
                
                case 'key_takeaways':
                    // 准备要点列表
                    const items = block.items || [];
                    let itemsHtml = "";
                    items.forEach((item, idx) => {
                        itemsHtml += `<div class="input-group mb-2 key-takeaway-item">
                            <input type="text" class="form-control key-takeaway-content" value="${item}" placeholder="要点内容">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-sm btn-outline-danger key-takeaway-remove" data-index="${idx}">删除</button>
                            </div>
                        </div>`;
                    });

                    contentHTML = `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="badge bg-secondary">要点</span>
                            <div>
                                <button type="button" class="btn btn-sm btn-outline-danger block-delete">删除</button>
                            </div>
                        </div>
                        <div class="key-takeaways-container">
                            ${itemsHtml}
                        </div>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary key-takeaway-add">+ 添加要点</button>
                        </div>
                    `;
                    break;
            }
            
            blockElement.innerHTML = contentHTML;
            
            // 绑定事件
            blockElement.querySelector('.block-delete').addEventListener('click', () => {
                deleteContentBlock(index);
            });
            
            const contentElement = blockElement.querySelector('.block-content');
            if (contentElement) {
                contentElement.addEventListener('input', () => {
                    updateBlockContent(index);
                });
            }
            
            if (block.type === 'image') {
                const urlElement = blockElement.querySelector('.block-image-url');
                if (urlElement) {
                    urlElement.addEventListener('input', () => {
                        updateBlockImageUrl(index);
                    });
                }
                
                const captionElement = blockElement.querySelector('.block-image-caption');
                if (captionElement) {
                    captionElement.addEventListener('input', () => {
                        updateBlockImageCaption(index);
                    });
                }
            }
            
            if (block.type === 'quote') {
                const sourceElement = blockElement.querySelector('.block-quote-source');
                if (sourceElement) {
                    sourceElement.addEventListener('input', () => {
                        updateBlockQuoteSource(index);
                    });
                }
            }
            
            if (block.type === 'key_takeaways') {
                // 添加点击处理程序，为要点添加按钮
                const addButton = blockElement.querySelector('.key-takeaway-add');
                if (addButton) {
                    addButton.addEventListener('click', () => {
                        addKeyTakeaway(index);
                    });
                }
                
                // 为所有要点内容输入框添加事件监听
                blockElement.querySelectorAll('.key-takeaway-content').forEach(input => {
                    input.addEventListener('input', () => {
                        updateKeyTakeaways(index);
                    });
                });
                
                // 为所有要点删除按钮添加事件监听
                blockElement.querySelectorAll('.key-takeaway-remove').forEach(button => {
                    button.addEventListener('click', (e) => {
                        const itemIndex = e.target.dataset.index;
                        removeKeyTakeaway(index, parseInt(itemIndex));
                    });
                });
            }
            
            return blockElement;
        }
        
        // 添加内容块
        function addContentBlock(type) {
            let newBlock = { type: type, content: '' };
            
            // 为要点类型初始化特殊结构
            if (type === 'key_takeaways') {
                newBlock = { type: type, items: [''] };
            }
            
            contentBlocks.push(newBlock);
            renderContentBlocks();
        }
        
        // 删除内容块
        function deleteContentBlock(index) {
            contentBlocks.splice(index, 1);
            renderContentBlocks();
        }
        
        // 更新块内容
        function updateBlockContent(index) {
            const blockElement = document.querySelector(`.content-block[data-index="${index}"]`);
            const contentElement = blockElement.querySelector('.block-content');
            contentBlocks[index].content = contentElement.value;
            updateContentBlocksData();
        }
        
        // 更新图片URL
        function updateBlockImageUrl(index) {
            const blockElement = document.querySelector(`.content-block[data-index="${index}"]`);
            const urlElement = blockElement.querySelector('.block-image-url');
            contentBlocks[index].content = urlElement.value;
            updateContentBlocksData();
        }
        
        // 更新图片说明
        function updateBlockImageCaption(index) {
            const blockElement = document.querySelector(`.content-block[data-index="${index}"]`);
            const captionElement = blockElement.querySelector('.block-image-caption');
            contentBlocks[index].caption = captionElement.value;
            updateContentBlocksData();
        }
        
        // 更新引用来源
        function updateBlockQuoteSource(index) {
            const blockElement = document.querySelector(`.content-block[data-index="${index}"]`);
            const sourceElement = blockElement.querySelector('.block-quote-source');
            contentBlocks[index].source = sourceElement.value;
            updateContentBlocksData();
        }
        
        // 添加新的要点
        function addKeyTakeaway(blockIndex) {
            const block = contentBlocks[blockIndex];
            if (!block.items) {
                block.items = [];
            }
            block.items.push('');
            renderContentBlocks();
        }
        
        // 移除要点
        function removeKeyTakeaway(blockIndex, itemIndex) {
            const block = contentBlocks[blockIndex];
            if (block.items && block.items.length > itemIndex) {
                block.items.splice(itemIndex, 1);
                // 如果没有要点了，至少保留一个空要点
                if (block.items.length === 0) {
                    block.items = [''];
                }
                renderContentBlocks();
            }
        }
        
        // 更新要点内容
        function updateKeyTakeaways(blockIndex) {
            const blockElement = document.querySelector(`.content-block[data-index="${blockIndex}"]`);
            const inputs = blockElement.querySelectorAll('.key-takeaway-content');
            const items = [];
            
            inputs.forEach((input) => {
                items.push(input.value);
            });
            
            contentBlocks[blockIndex].items = items;
            updateContentBlocksData();
        }
        
        // 更新隐藏字段中的内容块数据
        function updateContentBlocksData() {
            contentBlocksData.value = JSON.stringify(contentBlocks);
        }
    });
</script>
{% endblock %} 