{% extends "admin/base.html" %}

{% block page_title %}二级分类管理{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('admin.index') }}">首页</a></li>
<li class="breadcrumb-item active" aria-current="page">二级分类管理</li>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">二级分类管理</h5>
        <a href="{{ url_for('admin.create_secondary_category') }}" class="btn btn-primary btn-sm">
            <i class="bi bi-plus"></i> 添加二级分类
        </a>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-bordered datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>一级分类</th>
                        <th>名称</th>
                        <th>别名</th>
                        <th>图标</th>
                        <th>排序</th>
                        <th>可见性</th>
                        <th>分类数</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for secondary_category in secondary_categories %}
                    <tr>
                        <td>{{ secondary_category.id }}</td>
                        <td>{{ secondary_category.primary_category.name }}</td>
                        <td>{{ secondary_category.name }}</td>
                        <td>{{ secondary_category.slug }}</td>
                        <td>
                            {% if secondary_category.icon %}
                            <i class="{{ secondary_category.icon }}"></i> {{ secondary_category.icon }}
                            {% endif %}
                        </td>
                        <td>{{ secondary_category.order }}</td>
                        <td>
                            {% if secondary_category.is_visible %}
                            <span class="badge bg-success">显示</span>
                            {% else %}
                            <span class="badge bg-secondary">隐藏</span>
                            {% endif %}
                        </td>
                        <td>{{ secondary_category.categories.count() }}</td>
                        <td>
                            <div class="btn-group">
                                <a href="{{ url_for('admin.edit_secondary_category', id=secondary_category.id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-pencil"></i> 编辑
                                </a>
                                {% if secondary_category.categories.count() == 0 %}
                                <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#confirmDeleteModal" data-bs-delete-url="{{ url_for('admin.delete_secondary_category', id=secondary_category.id) }}">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-sm btn-outline-danger" disabled title="该二级分类下有分类，不能删除">
                                    <i class="bi bi-trash"></i> 删除
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %} 