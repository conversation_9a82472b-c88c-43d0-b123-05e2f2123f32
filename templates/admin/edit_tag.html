{% extends "admin/base.html" %}

{% block title %}{% if is_edit %}编辑标签{% else %}创建标签{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card">
                <div class="card-header">
                    <h4>{% if is_edit %}编辑标签{% else %}创建标签{% endif %}</h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.name.label(class="form-label") }}
                            {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                            {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text text-muted">标签名称，用于分类文章内容</div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('admin.tags') }}" class="btn btn-secondary">返回</a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 