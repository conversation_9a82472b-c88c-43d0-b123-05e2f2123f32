{% extends "base.html" %}

{% block title %}孕期体重计算器 - BabyJourney{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 页面标题 -->
            <div class="text-center mb-4">
                <h1 class="display-5"><i class="fas fa-weight text-success me-3"></i>孕期体重计算器</h1>
                <p class="lead text-muted">监测孕期体重增长，确保母婴健康</p>
            </div>

            <!-- 计算器表单 -->
            <div class="card shadow-sm calculator-form">
                <div class="card-body">
                    <form id="pregnancyWeightForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="prePregnancyWeight" class="form-label">孕前体重 (kg)</label>
                                    <input type="number" class="form-control" id="prePregnancyWeight" name="prePregnancyWeight" 
                                           min="35" max="150" step="0.1" placeholder="60.0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="height" class="form-label">身高 (cm)</label>
                                    <input type="number" class="form-control" id="height" name="height" 
                                           min="140" max="200" step="0.1" placeholder="165.0" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="currentWeight" class="form-label">当前体重 (kg)</label>
                                    <input type="number" class="form-control" id="currentWeight" name="currentWeight" 
                                           min="35" max="150" step="0.1" placeholder="65.0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="gestationalWeeks" class="form-label">孕周</label>
                                    <input type="number" class="form-control" id="gestationalWeeks" name="gestationalWeeks" 
                                           min="1" max="42" step="1" placeholder="20" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="pregnancyType" class="form-label">妊娠类型</label>
                                    <select class="form-select" id="pregnancyType" name="pregnancyType" required>
                                        <option value="">请选择</option>
                                        <option value="single">单胎妊娠</option>
                                        <option value="twins">双胎妊娠</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="fas fa-calculator me-2"></i>分析体重增长
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 计算结果 -->
            <div id="results-container" class="results-container mt-4" style="display: none;">
                <!-- 结果将通过JavaScript动态插入 -->
            </div>

            <!-- 说明信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5><i class="fas fa-info-circle text-info me-2"></i>体重管理说明</h5>
                    <ul class="mb-0">
                        <li>孕期体重增长建议基于孕前BMI值</li>
                        <li>体重增长过快或过慢都可能影响母婴健康</li>
                        <li>建议定期监测体重变化，保持均衡饮食</li>
                        <li>如有异常情况，请及时咨询产科医生</li>
                    </ul>
                </div>
            </div>

            <!-- 返回按钮 -->
            <div class="text-center mt-4">
                <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回工具首页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.calculator-form {
    border: none;
    border-radius: 15px;
}

.results-container {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.result-card {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1rem;
}

.result-card h4 {
    color: white;
    margin-bottom: 1.5rem;
}

.weight-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.weight-main {
    text-align: center;
}

.weight-gain {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffd700;
    margin: 1rem 0;
}

.weight-status {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.bmi-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
}

.recommendations-section, .progress-section, .tips-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.progress-bar-container {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.progress {
    height: 20px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.2);
}

.progress-bar {
    border-radius: 10px;
}

.recommendations-list, .tips-list {
    margin-bottom: 0;
}

.recommendations-list li, .tips-list li {
    margin-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.stage-info .alert {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

@media (max-width: 768px) {
    .weight-gain {
        font-size: 2rem;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- 引入计算器类库 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/pregnancy-weight-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 手动初始化孕期体重计算器
    if (window.calculatorManager) {
        window.calculatorManager.manualInit('pregnancy-weight', '#pregnancyWeightForm', '#results-container');
    }
});
</script>
{% endblock %}
