{% extends "base.html" %}

{% block title %}实用工具 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.tools-hero {
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    color: white;
    padding: 4rem 0;
    text-align: center;
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.tool-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    text-decoration: none;
    color: inherit;
    border: 1px solid #f0f0f0;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    text-decoration: none;
    color: inherit;
}

.tool-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.tool-category {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: #f8f9fa;
    border-radius: 20px;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.tool-category.pregnancy {
    background: #ffe6f0;
    color: #d63384;
}

.tool-category.baby {
    background: #e6f7ff;
    color: #0066cc;
}

.tool-category.planning {
    background: #f0f8e6;
    color: #52c41a;
}

.tool-category.fun {
    background: #fff7e6;
    color: #fa8c16;
}

.tool-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.tool-description {
    color: #666;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.tool-action {
    color: #ff6b9d;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.features-section {
    background: #f8f9fa;
    padding: 4rem 0;
    margin-top: 3rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-item {
    text-align: center;
    padding: 2rem;
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
    color: white;
}

@media (max-width: 768px) {
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .tool-card {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="tools-hero">
    <div class="container">
        <h1 class="display-4 mb-3">实用工具箱</h1>
        <p class="lead">为准妈妈和新手父母提供的专业计算器和实用工具</p>
    </div>
</div>

<div class="container">
    <div class="tools-grid">
        {% for tool in tools %}
        <a href="{{ url_for('tools.' + tool.slug.replace('-', '_')) }}" class="tool-card">
            <div class="tool-icon">
                <i class="{{ tool.icon }}"></i>
            </div>
            <div class="tool-category {{ tool.category }}">
                {% if tool.category == 'pregnancy' %}怀孕期
                {% elif tool.category == 'baby' %}婴幼儿
                {% elif tool.category == 'planning' %}规划
                {% elif tool.category == 'fun' %}娱乐
                {% endif %}
            </div>
            <h3 class="tool-title">{{ tool.name }}</h3>
            <p class="tool-description">{{ tool.description }}</p>
            <div class="tool-action">
                使用工具 <i class="fas fa-arrow-right"></i>
            </div>
        </a>
        {% endfor %}
    </div>
</div>

<div class="features-section">
    <div class="container">
        <div class="text-center">
            <h2 class="mb-4">为什么选择我们的工具</h2>
            <p class="lead text-muted">专业、准确、易用的孕育工具集</p>
        </div>
        
        <div class="features-grid">
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-microscope"></i>
                </div>
                <h4>基于科学数据</h4>
                <p class="text-muted">所有计算器都基于WHO、医学研究和权威机构的数据标准</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h4>简单易用</h4>
                <p class="text-muted">界面设计简洁直观，支持手机和电脑，随时随地使用</p>
            </div>
            
            <div class="feature-item">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h4>注重隐私</h4>
                <p class="text-muted">所有计算在您的设备上进行，不会收集或存储任何个人数据</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 工具页面的通用JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 添加工具卡片点击动画
    const toolCards = document.querySelectorAll('.tool-card');
    
    toolCards.forEach(card => {
        card.addEventListener('click', function(e) {
            // 添加点击效果
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
});
</script>
{% endblock %}
