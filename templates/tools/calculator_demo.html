{% extends "base.html" %}

{% block title %}计算器类库演示 - BabyJourney{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">
                        <i class="fas fa-code me-2"></i>
                        前端计算器类库演示
                    </h2>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        这个页面演示了我们封装的前端计算器类库的使用方法。所有计算都在浏览器端执行，支持静态网站部署。
                    </p>
                    
                    <!-- 计算器状态检查 -->
                    <div class="alert alert-info" id="calculator-status">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        正在检查计算器类库状态...
                    </div>
                    
                    <!-- 可用计算器列表 -->
                    <div class="row" id="calculators-grid">
                        <!-- 动态生成计算器卡片 -->
                    </div>
                    
                    <!-- 演示区域 -->
                    <div class="mt-5">
                        <h3>快速演示</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>生长发育计算器演示</h5>
                                    </div>
                                    <div class="card-body">
                                        <button class="btn btn-primary" onclick="demoGrowthCalculator()">
                                            <i class="fas fa-chart-line me-2"></i>
                                            演示生长发育计算
                                        </button>
                                        <div id="growth-demo-result" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h5>预产期计算器演示</h5>
                                    </div>
                                    <div class="card-body">
                                        <button class="btn btn-success" onclick="demoDueDateCalculator()">
                                            <i class="fas fa-calendar-alt me-2"></i>
                                            演示预产期计算
                                        </button>
                                        <div id="due-date-demo-result" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 技术信息 -->
                    <div class="mt-5">
                        <h3>技术特性</h3>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3 border rounded">
                                    <i class="fas fa-laptop-code fa-2x text-primary mb-2"></i>
                                    <h5>前端计算</h5>
                                    <p class="text-muted">所有计算在浏览器端执行，无需服务器支持</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3 border rounded">
                                    <i class="fas fa-cubes fa-2x text-success mb-2"></i>
                                    <h5>模块化设计</h5>
                                    <p class="text-muted">基于面向对象设计，易于扩展和维护</p>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="feature-card text-center p-3 border rounded">
                                    <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                                    <h5>静态友好</h5>
                                    <p class="text-muted">支持静态网站部署，CDN加速</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 代码示例 -->
                    <div class="mt-5">
                        <h3>使用示例</h3>
                        <div class="card">
                            <div class="card-header">
                                <h6>JavaScript 代码示例</h6>
                            </div>
                            <div class="card-body">
                                <pre><code class="language-javascript">// 创建生长发育计算器实例
const growthCalculator = new GrowthCalculator();

// 初始化计算器
growthCalculator.init('.calculator-form', '.results-container');

// 或者使用管理器自动初始化
window.calculatorManager.manualInit('growth', '.form', '.results');

// 手动计算示例
const result = growthCalculator.calculate({
    gender: 'boy',
    birthDate: '2023-01-01',
    measurementDate: '2023-07-01',
    weight: 7.5,
    height: 68,
    headCircumference: 42
});

console.log('计算结果:', result);</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.calculator-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.calculator-card:hover {
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}

.calculator-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
}

.status-available {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-unavailable {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

pre code {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    display: block;
    overflow-x: auto;
}
</style>
{% endblock %}

{% block extra_js %}
<!-- 引入计算器类库 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/growth-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/due-date-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>

<script>
// 计算器演示脚本
document.addEventListener('DOMContentLoaded', function() {
    checkCalculatorStatus();
    generateCalculatorCards();
});

function checkCalculatorStatus() {
    const statusElement = document.getElementById('calculator-status');
    
    if (window.calculatorManager && window.BaseCalculator) {
        statusElement.className = 'alert alert-success';
        statusElement.innerHTML = `
            <i class="fas fa-check-circle me-2"></i>
            计算器类库加载成功！可用计算器: ${window.calculatorManager.getAvailableCalculators().join(', ')}
        `;
    } else {
        statusElement.className = 'alert alert-danger';
        statusElement.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            计算器类库加载失败，请检查脚本文件是否存在
        `;
    }
}

function generateCalculatorCards() {
    const grid = document.getElementById('calculators-grid');
    
    const calculators = [
        { name: 'GrowthCalculator', title: '生长发育计算器', icon: 'fas fa-chart-line' },
        { name: 'DueDateCalculator', title: '预产期计算器', icon: 'fas fa-calendar-alt' },
        { name: 'ConceptionCalculator', title: '受孕日期计算器', icon: 'fas fa-heart' },
        { name: 'PregnancyWeightCalculator', title: '孕期体重计算器', icon: 'fas fa-weight' },
        { name: 'BabyCostCalculator', title: '婴儿花费计算器', icon: 'fas fa-dollar-sign' },
        { name: 'HeightPredictor', title: '身高预测器', icon: 'fas fa-ruler-vertical' }
    ];
    
    calculators.forEach(calc => {
        const isAvailable = window[calc.name] !== undefined;
        const statusClass = isAvailable ? 'status-available' : 'status-unavailable';
        const statusText = isAvailable ? '已加载' : '未加载';
        const statusIcon = isAvailable ? 'fas fa-check' : 'fas fa-times';
        
        const cardHtml = `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="calculator-card">
                    <div class="d-flex align-items-center mb-2">
                        <i class="${calc.icon} fa-lg text-primary me-2"></i>
                        <h6 class="mb-0">${calc.title}</h6>
                    </div>
                    <div class="calculator-status ${statusClass}">
                        <i class="${statusIcon} me-1"></i>
                        ${calc.name} - ${statusText}
                    </div>
                </div>
            </div>
        `;
        
        grid.innerHTML += cardHtml;
    });
}

function demoGrowthCalculator() {
    if (!window.GrowthCalculator) {
        alert('GrowthCalculator 类未加载');
        return;
    }
    
    const calculator = new GrowthCalculator();
    const testData = {
        gender: 'boy',
        birthDate: '2023-01-01',
        measurementDate: '2023-07-01',
        weight: 7.5,
        height: 68,
        headCircumference: 42
    };
    
    if (calculator.validateInput(testData)) {
        const result = calculator.calculate(testData);
        document.getElementById('growth-demo-result').innerHTML = `
            <div class="alert alert-success">
                <h6>计算结果：</h6>
                <ul class="mb-0">
                    <li>体重: ${result.weight.formatted}</li>
                    <li>身高: ${result.height.formatted}</li>
                    <li>头围: ${result.headCircumference.formatted}</li>
                    <li>月龄: ${result.ageInMonths.toFixed(1)} 个月</li>
                </ul>
            </div>
        `;
    } else {
        document.getElementById('growth-demo-result').innerHTML = `
            <div class="alert alert-danger">验证失败</div>
        `;
    }
}

function demoDueDateCalculator() {
    if (!window.DueDateCalculator) {
        alert('DueDateCalculator 类未加载');
        return;
    }
    
    const calculator = new DueDateCalculator();
    const testData = {
        lastPeriod: '2024-01-01',
        cycleLength: 28
    };
    
    if (calculator.validateInput(testData)) {
        const result = calculator.calculate(testData);
        document.getElementById('due-date-demo-result').innerHTML = `
            <div class="alert alert-success">
                <h6>计算结果：</h6>
                <ul class="mb-0">
                    <li>预产期: ${result.dueDate.formatted}</li>
                    <li>当前孕周: ${result.currentPregnancy.weeks}周${result.currentPregnancy.days}天</li>
                    <li>孕期阶段: ${result.trimester.name}</li>
                    <li>距离预产期: ${result.currentPregnancy.daysUntilDue}天</li>
                </ul>
            </div>
        `;
    } else {
        document.getElementById('due-date-demo-result').innerHTML = `
            <div class="alert alert-danger">验证失败</div>
        `;
    }
}
</script>
{% endblock %}
