{% extends "base.html" %}

{% block title %}婴儿花费计算器 - BabyJourney{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 页面标题 -->
            <div class="text-center mb-4">
                <h1 class="display-5"><i class="fas fa-dollar-sign text-warning me-3"></i>婴儿花费计算器</h1>
                <p class="lead text-muted">估算育儿成本，做好财务规划</p>
            </div>

            <!-- 计算器表单 -->
            <div class="card shadow-sm calculator-form">
                <div class="card-body">
                    <form id="babyCostForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="region" class="form-label">所在地区</label>
                                    <select class="form-select" id="region" name="region" required>
                                        <option value="">请选择</option>
                                        <option value="tier1">一线城市（北上广深）</option>
                                        <option value="tier2">二线城市</option>
                                        <option value="tier3">三线城市</option>
                                        <option value="rural">农村地区</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="consumptionLevel" class="form-label">消费水平</label>
                                    <select class="form-select" id="consumptionLevel" name="consumptionLevel" required>
                                        <option value="">请选择</option>
                                        <option value="basic">基础型</option>
                                        <option value="comfort">舒适型</option>
                                        <option value="premium">高端型</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="feedingMethod" class="form-label">喂养方式</label>
                                    <select class="form-select" id="feedingMethod" name="feedingMethod" required>
                                        <option value="">请选择</option>
                                        <option value="breastfeeding">纯母乳喂养</option>
                                        <option value="mixed">混合喂养</option>
                                        <option value="formula">纯奶粉喂养</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="careType" class="form-label">照护方式</label>
                                    <select class="form-select" id="careType" name="careType" required>
                                        <option value="">请选择</option>
                                        <option value="family">家庭照护</option>
                                        <option value="nanny">请保姆</option>
                                        <option value="daycare">托儿所</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="timeRange" class="form-label">计算时间范围</label>
                                    <select class="form-select" id="timeRange" name="timeRange" required>
                                        <option value="">请选择</option>
                                        <option value="first-year">第一年</option>
                                        <option value="first-three-years">前三年</option>
                                        <option value="preschool">学前期（0-6岁）</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="includeEducation" class="form-label">是否包含早教费用</label>
                                    <select class="form-select" id="includeEducation" name="includeEducation" required>
                                        <option value="">请选择</option>
                                        <option value="yes">包含</option>
                                        <option value="no">不包含</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-calculator me-2"></i>计算育儿成本
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 计算结果 -->
            <div id="results-container" class="results-container mt-4" style="display: none;">
                <!-- 结果将通过JavaScript动态插入 -->
            </div>

            <!-- 说明信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5><i class="fas fa-info-circle text-info me-2"></i>费用说明</h5>
                    <ul class="mb-0">
                        <li>费用估算基于市场平均价格和统计数据</li>
                        <li>实际费用因个人选择和地区差异而有所不同</li>
                        <li>建议提前做好财务规划，合理安排预算</li>
                        <li>可根据家庭经济状况调整消费水平</li>
                    </ul>
                </div>
            </div>

            <!-- 返回按钮 -->
            <div class="text-center mt-4">
                <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回工具首页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.calculator-form {
    border: none;
    border-radius: 15px;
}

.results-container {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.result-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1rem;
}

.result-card h4 {
    color: white;
    margin-bottom: 1.5rem;
}

.cost-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.cost-main {
    text-align: center;
}

.total-cost {
    font-size: 3rem;
    font-weight: bold;
    color: #ffd700;
    margin: 1rem 0;
}

.cost-period {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
}

.monthly-cost {
    font-size: 1.1rem;
    opacity: 0.9;
}

.breakdown-section, .recommendations-section, .tips-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.cost-breakdown {
    display: grid;
    gap: 1rem;
}

.cost-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
}

.cost-category {
    font-weight: bold;
}

.cost-amount {
    font-size: 1.1rem;
    color: #ffd700;
}

.cost-percentage {
    font-size: 0.9rem;
    opacity: 0.8;
}

.recommendations-list, .tips-list {
    margin-bottom: 0;
}

.recommendations-list li, .tips-list li {
    margin-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.parameters-info .alert {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

@media (max-width: 768px) {
    .total-cost {
        font-size: 2rem;
    }
    
    .cost-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- 引入计算器类库 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/baby-cost-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 手动初始化婴儿花费计算器
    if (window.calculatorManager) {
        window.calculatorManager.manualInit('baby-cost', '#babyCostForm', '#results-container');
    }
});
</script>
{% endblock %}
