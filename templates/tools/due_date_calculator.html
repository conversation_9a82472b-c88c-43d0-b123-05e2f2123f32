{% extends "base.html" %}

{% block title %}预产期计算器 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.tool-header {
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.tool-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.calculator-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #ff6b9d;
    box-shadow: 0 0 0 0.2rem rgba(255, 107, 157, 0.25);
}

.btn-calculate {
    background: linear-gradient(135deg, #ff6b9d, #4ecdc4);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
    transition: transform 0.3s ease;
    width: 100%;
}

.btn-calculate:hover {
    transform: translateY(-2px);
    color: white;
}

.btn-calculate:disabled {
    opacity: 0.6;
    transform: none;
}

.result-card {
    background: linear-gradient(135deg, #e8f5e8, #f0f8ff);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    display: none;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 600;
    color: #333;
}

.result-value {
    font-weight: 700;
    color: #ff6b9d;
    font-size: 1.1rem;
}

.info-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.info-title {
    color: #333;
    font-weight: 600;
    margin-bottom: 1rem;
}

.info-list {
    list-style: none;
    padding: 0;
}

.info-list li {
    padding: 0.5rem 0;
    padding-left: 1.5rem;
    position: relative;
}

.info-list li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.loading {
    display: none;
    text-align: center;
    padding: 1rem;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6b9d;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
    .tool-container {
        padding: 1rem;
    }
    
    .calculator-card {
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="tool-header">
    <div class="container">
        <h1 class="display-5 mb-3">预产期计算器</h1>
        <p class="lead">根据末次月经日期计算预产期和当前孕周</p>
    </div>
</div>

<div class="container tool-container">
    <div class="calculator-card">
        <form id="dueDateForm">
            <div class="form-group">
                <label for="lastPeriod" class="form-label">末次月经第一天日期</label>
                <input type="date" class="form-control" id="lastPeriod" required>
                <small class="form-text text-muted">请选择您末次月经开始的日期</small>
            </div>
            
            <div class="form-group">
                <label for="cycleLength" class="form-label">月经周期长度（天）</label>
                <input type="number" class="form-control" id="cycleLength" value="28" min="21" max="35" required>
                <small class="form-text text-muted">正常月经周期为21-35天，如果不确定请使用28天</small>
            </div>
            
            <button type="submit" class="btn btn-calculate">
                <i class="fas fa-calculator me-2"></i>计算预产期
            </button>
        </form>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p class="mt-2">正在计算...</p>
        </div>
        
        <div class="result-card" id="resultCard">
            <h3 class="text-center mb-4">计算结果</h3>
            <div class="result-item">
                <span class="result-label">预产期：</span>
                <span class="result-value" id="dueDate">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">当前孕周：</span>
                <span class="result-value" id="currentWeek">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">第一孕期：</span>
                <span class="result-value" id="firstTrimester">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">第二孕期：</span>
                <span class="result-value" id="secondTrimester">-</span>
            </div>
            <div class="result-item">
                <span class="result-label">第三孕期：</span>
                <span class="result-value" id="thirdTrimester">-</span>
            </div>
        </div>
    </div>
    
    <div class="info-section">
        <h4 class="info-title">重要提示</h4>
        <ul class="info-list">
            <li>预产期只是一个估计日期，大多数婴儿并不会在预产期当天出生</li>
            <li>通常认为在预产期前后两周内出生都是正常的</li>
            <li>预产期计算基于内格尔规则（Naegele's rule），即从末次月经第一天开始，加280天</li>
            <li>如果您的月经周期不规律或不确定末次月经日期，请咨询医生获取更准确的预产期</li>
            <li>定期产检是确保母婴健康的重要措施</li>
        </ul>
    </div>
    
    <div class="text-center mt-4">
        <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>返回工具首页
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('dueDateForm');
    const loading = document.getElementById('loading');
    const resultCard = document.getElementById('resultCard');
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const lastPeriod = document.getElementById('lastPeriod').value;
        const cycleLength = document.getElementById('cycleLength').value;
        
        if (!lastPeriod) {
            alert('请选择末次月经日期');
            return;
        }
        
        // 显示加载状态
        loading.style.display = 'block';
        resultCard.style.display = 'none';
        
        try {
            const response = await fetch('/api/calculate-due-date', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    last_period: lastPeriod,
                    cycle_length: cycleLength
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displayResult(data.result);
            } else {
                alert('计算失败：' + data.error);
            }
        } catch (error) {
            alert('计算失败，请稍后重试');
            console.error('Error:', error);
        } finally {
            loading.style.display = 'none';
        }
    });
    
    function displayResult(result) {
        document.getElementById('dueDate').textContent = result.due_date_formatted;
        document.getElementById('currentWeek').textContent = `${result.weeks_pregnant}周${result.days_remainder}天`;
        document.getElementById('firstTrimester').textContent = 
            `${formatDate(result.first_trimester.start)} 至 ${formatDate(result.first_trimester.end)}`;
        document.getElementById('secondTrimester').textContent = 
            `${formatDate(result.second_trimester.start)} 至 ${formatDate(result.second_trimester.end)}`;
        document.getElementById('thirdTrimester').textContent = 
            `${formatDate(result.third_trimester.start)} 至 ${formatDate(result.third_trimester.end)}`;
        
        resultCard.style.display = 'block';
        resultCard.scrollIntoView({ behavior: 'smooth' });
    }
    
    function formatDate(dateStr) {
        const date = new Date(dateStr);
        return `${date.getFullYear()}年${(date.getMonth() + 1).toString().padStart(2, '0')}月${date.getDate().toString().padStart(2, '0')}日`;
    }
    
    // 设置默认日期为3个月前
    const today = new Date();
    const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate());
    document.getElementById('lastPeriod').value = threeMonthsAgo.toISOString().split('T')[0];
});
</script>
{% endblock %}
