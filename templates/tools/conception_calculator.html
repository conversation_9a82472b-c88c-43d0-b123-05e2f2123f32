{% extends "base.html" %}

{% block title %}受孕日期计算器 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
.tool-header {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
    padding: 3rem 0;
    text-align: center;
}

.calculator-tabs {
    display: flex;
    margin-bottom: 2rem;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.tab-button {
    flex: 1;
    padding: 1rem;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.tab-button.active {
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.result-timeline {
    background: linear-gradient(135deg, #ffe6f0, #fff0f5);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
}

.timeline-item {
    display: flex;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.1);
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b9d, #c44569);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.timeline-content {
    flex: 1;
}

.timeline-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.timeline-date {
    color: #ff6b9d;
    font-weight: 700;
    font-size: 1.1rem;
}
</style>
{% endblock %}

{% block content %}
<div class="tool-header">
    <div class="container">
        <h1 class="display-5 mb-3">受孕日期计算器</h1>
        <p class="lead">根据预产期或末次月经推算受孕日期和排卵日期</p>
    </div>
</div>

<div class="container tool-container">
    <div class="calculator-card">
        <div class="calculator-tabs">
            <button class="tab-button active" onclick="switchTab('lmp')">根据末次月经计算</button>
            <button class="tab-button" onclick="switchTab('dueDate')">根据预产期计算</button>
        </div>
        
        <!-- 根据末次月经计算 -->
        <div id="lmpTab" class="tab-content active">
            <form id="lmpForm">
                <div class="form-group">
                    <label for="lastPeriodLmp" class="form-label">末次月经第一天日期</label>
                    <input type="date" class="form-control" id="lastPeriodLmp" required>
                </div>
                
                <div class="form-group">
                    <label for="cycleLengthLmp" class="form-label">月经周期长度（天）</label>
                    <input type="number" class="form-control" id="cycleLengthLmp" value="28" min="21" max="35" required>
                    <small class="form-text text-muted">正常月经周期为21-35天</small>
                </div>
                
                <button type="submit" class="btn btn-calculate">
                    <i class="fas fa-heart me-2"></i>计算受孕日期
                </button>
            </form>
        </div>
        
        <!-- 根据预产期计算 -->
        <div id="dueDateTab" class="tab-content">
            <form id="dueDateForm">
                <div class="form-group">
                    <label for="dueDateInput" class="form-label">预产期</label>
                    <input type="date" class="form-control" id="dueDateInput" required>
                    <small class="form-text text-muted">请输入医生确定的预产期</small>
                </div>
                
                <button type="submit" class="btn btn-calculate">
                    <i class="fas fa-heart me-2"></i>计算受孕日期
                </button>
            </form>
        </div>
        
        <div class="result-timeline" id="resultTimeline" style="display: none;">
            <h3 class="text-center mb-4">受孕时间线</h3>
            
            <div class="timeline-item">
                <div class="timeline-icon">
                    <i class="fas fa-calendar-day"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">排卵日期</div>
                    <div class="timeline-date" id="ovulationDate">-</div>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">最可能受孕日期</div>
                    <div class="timeline-date" id="conceptionDate">-</div>
                </div>
            </div>
            
            <div class="timeline-item">
                <div class="timeline-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="timeline-content">
                    <div class="timeline-title">受孕窗口期</div>
                    <div class="timeline-date" id="fertileWindow">-</div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="info-section">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> 计算说明</h5>
            <ul class="mb-0">
                <li>排卵通常发生在下次月经前14天</li>
                <li>精子可在体内存活3-5天，卵子存活12-24小时</li>
                <li>受孕窗口期为排卵前5天到排卵后1天</li>
                <li>这些日期都是估算值，实际情况可能有所不同</li>
            </ul>
        </div>
    </div>
    
    <div class="text-center mt-4">
        <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i>返回工具首页
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentTab = 'lmp';

function switchTab(tab) {
    // 更新按钮状态
    document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
    document.getElementById(tab + 'Tab').classList.add('active');
    
    currentTab = tab;
    
    // 隐藏结果
    document.getElementById('resultTimeline').style.display = 'none';
}

document.addEventListener('DOMContentLoaded', function() {
    // 设置默认日期
    const today = new Date();
    const threeMonthsAgo = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate());
    const nineMonthsLater = new Date(today.getFullYear(), today.getMonth() + 9, today.getDate());
    
    document.getElementById('lastPeriodLmp').value = threeMonthsAgo.toISOString().split('T')[0];
    document.getElementById('dueDateInput').value = nineMonthsLater.toISOString().split('T')[0];
    
    // 末次月经表单提交
    document.getElementById('lmpForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const lastPeriod = document.getElementById('lastPeriodLmp').value;
        const cycleLength = document.getElementById('cycleLengthLmp').value;
        
        try {
            const response = await fetch('/api/calculate-conception', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'from_lmp',
                    last_period: lastPeriod,
                    cycle_length: cycleLength
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displayResults(data.result);
            } else {
                alert('计算失败：' + data.error);
            }
        } catch (error) {
            alert('计算失败，请稍后重试');
            console.error('Error:', error);
        }
    });
    
    // 预产期表单提交
    document.getElementById('dueDateForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const dueDate = document.getElementById('dueDateInput').value;
        
        try {
            const response = await fetch('/api/calculate-conception', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: 'from_due_date',
                    due_date: dueDate
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                displayResults(data.result);
            } else {
                alert('计算失败：' + data.error);
            }
        } catch (error) {
            alert('计算失败，请稍后重试');
            console.error('Error:', error);
        }
    });
    
    function displayResults(result) {
        document.getElementById('ovulationDate').textContent = result.ovulation_date_formatted;
        document.getElementById('conceptionDate').textContent = result.conception_date_formatted;
        document.getElementById('fertileWindow').textContent = 
            `${result.fertile_window.start_formatted} 至 ${result.fertile_window.end_formatted}`;
        
        document.getElementById('resultTimeline').style.display = 'block';
        document.getElementById('resultTimeline').scrollIntoView({ behavior: 'smooth' });
    }
});
</script>
{% endblock %}
