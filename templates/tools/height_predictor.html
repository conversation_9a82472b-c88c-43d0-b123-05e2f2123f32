{% extends "base.html" %}

{% block title %}身高预测器 - <PERSON><PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-lg-8 mx-auto">
            <!-- 页面标题 -->
            <div class="text-center mb-4">
                <h1 class="display-5"><i class="fas fa-ruler-vertical text-info me-3"></i>身高预测器</h1>
                <p class="lead text-muted">基于父母身高和遗传规律预测孩子成年身高</p>
            </div>

            <!-- 计算器表单 -->
            <div class="card shadow-sm calculator-form">
                <div class="card-body">
                    <form id="heightPredictorForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="childGender" class="form-label">孩子性别</label>
                                    <select class="form-select" id="childGender" name="childGender" required>
                                        <option value="">请选择</option>
                                        <option value="boy">男孩</option>
                                        <option value="girl">女孩</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="fatherHeight" class="form-label">父亲身高 (cm)</label>
                                    <input type="number" class="form-control" id="fatherHeight" name="fatherHeight" 
                                           min="150" max="220" step="0.1" placeholder="175.0" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="motherHeight" class="form-label">母亲身高 (cm)</label>
                                    <input type="number" class="form-control" id="motherHeight" name="motherHeight" 
                                           min="140" max="200" step="0.1" placeholder="165.0" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="childAge" class="form-label">孩子当前年龄 (岁) <small class="text-muted">可选</small></label>
                                    <input type="number" class="form-control" id="childAge" name="childAge" 
                                           min="0" max="18" step="0.1" placeholder="5.0">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="childCurrentHeight" class="form-label">孩子当前身高 (cm) <small class="text-muted">可选</small></label>
                                    <input type="number" class="form-control" id="childCurrentHeight" name="childCurrentHeight" 
                                           min="40" max="200" step="0.1" placeholder="110.0">
                                </div>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-calculator me-2"></i>预测身高
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 计算结果 -->
            <div id="results-container" class="results-container mt-4" style="display: none;">
                <!-- 结果将通过JavaScript动态插入 -->
            </div>

            <!-- 说明信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h5><i class="fas fa-info-circle text-info me-2"></i>预测说明</h5>
                    <ul class="mb-0">
                        <li>身高预测基于遗传学原理和统计学方法</li>
                        <li>预测结果仅供参考，实际身高受多种因素影响</li>
                        <li>营养、运动、睡眠等后天因素同样重要</li>
                        <li>如有生长发育异常，请及时咨询专业医生</li>
                    </ul>
                </div>
            </div>

            <!-- 返回按钮 -->
            <div class="text-center mt-4">
                <a href="{{ url_for('tools.tools_index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回工具首页
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.calculator-form {
    border: none;
    border-radius: 15px;
}

.results-container {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.result-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 1rem;
}

.result-card h4 {
    color: white;
    margin-bottom: 1.5rem;
}

.prediction-summary {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.prediction-main {
    text-align: center;
}

.height-value {
    font-size: 3rem;
    font-weight: bold;
    color: #ffd700;
    margin: 1rem 0;
}

.height-range {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
}

.confidence {
    font-size: 0.9rem;
    opacity: 0.9;
}

.parent-info {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 1rem;
}

.parent-heights div {
    margin-bottom: 0.5rem;
}

.methods-section, .analysis-section, .tips-section, .disclaimer-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

.methods-list {
    display: grid;
    gap: 1rem;
}

.method-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    display: grid;
    grid-template-columns: 1fr auto auto auto;
    gap: 1rem;
    align-items: center;
}

.method-name {
    font-weight: bold;
}

.method-result {
    font-size: 1.1rem;
    color: #ffd700;
}

.method-range {
    font-size: 0.9rem;
    opacity: 0.8;
}

.method-confidence {
    font-size: 0.8rem;
    opacity: 0.7;
}

.analysis-list, .tips-list, .disclaimer-list {
    margin-bottom: 0;
}

.analysis-list li, .tips-list li, .disclaimer-list li {
    margin-bottom: 0.5rem;
    padding-left: 0.5rem;
}

.current-info .alert {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

@media (max-width: 768px) {
    .height-value {
        font-size: 2rem;
    }
    
    .method-item {
        grid-template-columns: 1fr;
        text-align: center;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- 引入计算器类库 -->
<script src="{{ url_for('static', filename='js/calculators/base-calculator.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/height-predictor.js') }}"></script>
<script src="{{ url_for('static', filename='js/calculators/calculator-manager.js') }}"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 手动初始化身高预测器
    if (window.calculatorManager) {
        window.calculatorManager.manualInit('height', '#heightPredictorForm', '#results-container');
    }
});
</script>
{% endblock %}
