/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --sidebar-width: 300px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--dark-color);
    overflow-x: hidden; /* 防止侧边栏导致水平滚动 */
}

/* Header Styles */
.site-header {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.main-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

/* 汉堡菜单按钮样式 */
.hamburger-menu {
    background: none;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    position: relative;
    z-index: 2000; /* 提高z-index确保按钮总是可点击 */
    margin-right: 10px;
    padding: 0; /* 移除默认填充 */
    outline: none; /* 移除默认焦点样式 */
    pointer-events: auto; /* 确保按钮可以接收鼠标事件 */
}

.hamburger-icon {
    position: relative;
    width: 24px;
    height: 2px;
    background-color: var(--dark-color);
    transition: background-color 0.3s ease;
}

.hamburger-icon::before,
.hamburger-icon::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 2px;
    background-color: var(--dark-color);
    transition: transform 0.3s ease;
}

.hamburger-icon::before {
    top: -6px;
}

.hamburger-icon::after {
    top: 6px;
}

/* 汉堡菜单激活状态 */
.hamburger-menu.active .hamburger-icon {
    background-color: transparent;
}

.hamburger-menu.active .hamburger-icon::before {
    transform: rotate(45deg) translate(3px, 6px);
}

.hamburger-menu.active .hamburger-icon::after {
    transform: rotate(-45deg) translate(3px, -6px);
}

/* 侧边栏导航样式 */
.sidebar-menu {
    position: fixed;
    top: 0;
    left: -100%; /* 使用-100%而不是-var(--sidebar-width)，确保完全在屏幕外 */
    width: var(--sidebar-width);
    height: 100%;
    background-color: white;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
    overflow-y: auto;
    transition: left 0.3s ease;
    z-index: 1500; /* 降低z-index，确保不会遮挡汉堡菜单按钮 */
    pointer-events: auto; /* 确保侧边栏可以接收鼠标事件 */
}

body.sidebar-open .sidebar-menu {
    left: 0; /* 当body有sidebar-open类时显示 */
}

/* 侧边栏头部样式 */
.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    position: relative; /* 确保定位上下文正确 */
    z-index: 1; /* 降低z-index使其不会遮挡汉堡菜单 */
    pointer-events: auto; /* 确保可以接收鼠标事件 */
}

.sidebar-logo img {
    height: 35px;
    margin-bottom: 1rem;
}

.search-box {
    position: relative;
    margin-top: 10px;
}

.search-box input {
    width: 100%;
    padding: 10px 35px 10px 15px;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    font-size: 14px;
}

.search-box img {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
}

/* 侧边栏导航菜单样式 */
.sidebar-nav {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-category {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 15px 20px;
    background: none;
    border: none;
    text-align: left;
    font-size: 16px;
    font-weight: 500;
    color: var(--dark-color);
    cursor: pointer;
    position: relative;
}

.sidebar-category::after {
    content: '\f107';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    transition: transform 0.3s ease;
}

.sidebar-category[aria-expanded="true"]::after {
    transform: rotate(180deg);
}

.sidebar-submenu {
    list-style: none;
    padding: 0;
    margin: 0;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    display: none; /* 默认隐藏子菜单 */
}

.sidebar-submenu.active {
    max-height: 500px; /* 足够容纳子菜单项 */
    display: block; /* 激活时显示子菜单 */
}

.sidebar-submenu li a {
    display: block;
    padding: 10px 20px 10px 35px;
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s ease;
}

.sidebar-submenu li a:hover {
    color: var(--primary-color);
    background-color: #f8f9fa;
}

/* 遮罩层样式 */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000; /* 确保遮罩层在侧边栏下方，汉堡菜单上方 */
    pointer-events: none; /* 默认不接收鼠标事件 */
}

body.sidebar-open .sidebar-overlay {
    opacity: 1;
    visibility: visible;
    pointer-events: auto; /* 侧边栏打开时接收鼠标事件 */
}

/* 确保侧边栏完全显示在主内容上方 */
body.sidebar-open .site-main,
body.sidebar-open .site-footer,
body.sidebar-open .site-header {
    pointer-events: none; /* 当侧边栏打开时，禁用主内容的鼠标事件 */
}

body.sidebar-open .hamburger-menu,
body.sidebar-open .sidebar-menu,
body.sidebar-open .sidebar-overlay {
    pointer-events: auto; /* 确保这些元素仍然可以接收鼠标事件 */
}

.logo img {
    height: 40px;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-links li {
    margin: 0 1rem;
}

.nav-links a {
    color: var(--dark-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--primary-color);
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 4px;
    padding: 0.5rem 0;
    min-width: 200px;
}

.dropdown:hover .dropdown-menu {
    display: block;
}

.dropdown-menu a {
    display: block;
    padding: 0.5rem 1rem;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

/* Footer Styles */
.site-footer {
    background: var(--light-color);
    padding: 4rem 0 2rem;
    margin-top: 4rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-section li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--secondary-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-link img {
    width: 24px;
    height: 24px;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #dee2e6;
    color: var(--secondary-color);
}

/* Button Styles */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Article Card Styles */
.article-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
}

.article-image {
    position: relative;
    padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.article-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-content {
    padding: 1.5rem;
}

.article-category {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: var(--light-color);
    border-radius: 20px;
    font-size: 0.875rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.article-meta {
    display: flex;
    gap: 1rem;
    color: var(--secondary-color);
    font-size: 0.875rem;
    margin-top: 1rem;
}

.medical-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(25, 135, 84, 0.9);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 1rem;
    line-height: 1.5;
    color: var(--dark-color);
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
}

.form-help {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

/* Alert Styles */
.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    position: relative;
}

.alert-success {
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
    color: #842029;
}

.alert-info {
    background-color: #cff4fc;
    border: 1px solid #b6effb;
    color: #055160;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .main-nav {
        flex-direction: row; /* 改为row，使元素水平排列 */
        padding: 1rem;
        justify-content: flex-start; /* 内容靠左对齐 */
    }
    
    .nav-links {
        display: none; /* 在小屏幕上隐藏导航链接 */
    }
    
    .dropdown-menu {
        position: static;
        box-shadow: none;
        padding-left: 1rem;
    }
    
    .nav-right {
        width: auto; /* 改为auto而不是100% */
        margin-left: auto; /* 将右侧元素推到最右边 */
    }
    
    .footer-content {
        grid-template-columns: 1fr;
    }
    
    .article-card.featured {
        grid-column: span 1;
    }
}

/* Animation Styles */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease forwards;
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .article-actions,
    .comments-section {
        display: none;
    }
    
    .article-content {
        font-size: 12pt;
        line-height: 1.5;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
    }
}

/* Key Takeaways 样式 */
.key-takeaways {
    background-color: #fff3cd;
    border-left: 6px solid #ffc107;
    padding: 25px;
    margin: 30px 0;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.key-takeaways h4 {
    color: #856404;
    font-size: 1.3rem;
    margin-top: 0;
    margin-bottom: 15px;
    font-weight: 700;
    display: flex;
    align-items: center;
}

.key-takeaways h4:before {
    content: "★";
    margin-right: 10px;
    color: #ffc107;
}

.key-takeaways ul {
    margin-bottom: 0;
    padding-left: 25px;
}

.key-takeaways li {
    margin-bottom: 10px;
    color: #5a4500;
    font-weight: 500;
}

.key-takeaways li:last-child {
    margin-bottom: 0;
}

/* 工具相关样式 */
.sidebar-tool-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: #333;
    text-decoration: none;
    border-radius: 8px;
    transition: background-color 0.3s ease;
    margin-bottom: 0.5rem;
}

.sidebar-tool-link:hover {
    background-color: #f8f9fa;
    color: var(--primary-color);
    text-decoration: none;
}

.sidebar-tool-link i {
    margin-right: 0.75rem;
    color: var(--primary-pink);
}

/* 导航栏工具链接 */
.nav-links li a i {
    margin-right: 0.5rem;
}