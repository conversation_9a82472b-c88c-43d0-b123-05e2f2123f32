/* 管理后台样式 */

/* 侧边栏样式 */
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
}

.sidebar .nav-link .feather {
    margin-right: 4px;
    color: #999;
}

.sidebar .nav-link.active {
    color: #007bff;
}

.sidebar .nav-link:hover .feather,
.sidebar .nav-link.active .feather {
    color: inherit;
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

/* 主内容区域 */
[role="main"] {
    padding-top: 48px;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table td, .table th {
    vertical-align: middle;
}

/* 卡片样式 */
.card {
    margin-bottom: 1.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: none;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
    padding: 0.75rem 1.25rem;
}

/* 表单样式 */
.form-label {
    font-weight: 500;
}

/* 统计卡片样式 */
.stats-card {
    border-left: 4px solid #007bff;
    border-radius: 4px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.stats-card-title {
    font-size: 0.875rem;
    text-transform: uppercase;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.stats-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

/* 文章编辑器样式 */
.editor-toolbar {
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0.25rem 0 0;
    background-color: #f8f9fa;
    padding: 0.5rem;
}

.editor-content {
    border: 1px solid #ced4da;
    border-top: none;
    border-radius: 0 0 0.25rem 0.25rem;
    padding: 1rem;
    min-height: 300px;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .sidebar {
        position: static;
        height: auto;
        padding-top: 0;
    }
    
    [role="main"] {
        padding-top: 0;
    }
    
    .sidebar-sticky {
        height: auto;
    }
} 