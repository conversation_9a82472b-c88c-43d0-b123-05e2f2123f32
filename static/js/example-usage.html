<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>新架构使用示例</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>新架构使用示例</h1>
        
        <!-- 生长发育计算器示例 -->
        <div class="card mb-4">
            <div class="card-header">
                <h3>增强版生长发育计算器</h3>
            </div>
            <div class="card-body">
                <form class="calculator-form">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="birthDate" class="form-label">出生日期</label>
                                <div class="date-input-container">
                                    <input type="date" class="form-control" id="birthDate" name="birthDate" required>
                                    <button type="button" class="btn btn-outline-secondary date-picker-button">📅</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="measurementDate" class="form-label">测量日期</label>
                                <div class="date-input-container">
                                    <input type="date" class="form-control" id="measurementDate" name="measurementDate" required>
                                    <button type="button" class="btn btn-outline-secondary date-picker-button">📅</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">宝宝性别</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="gender" id="boy" value="boy" required>
                                <label class="form-check-label" for="boy">男孩</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="gender" id="girl" value="girl" required>
                                <label class="form-check-label" for="girl">女孩</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="weight" class="form-label">体重 (千克)</label>
                                <input type="number" class="form-control" id="weight" name="weight" step="0.1" min="0.5" max="30" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="height" class="form-label">身高 (厘米)</label>
                                <input type="number" class="form-control" id="height" name="height" step="0.1" min="30" max="120" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="headCircumference" class="form-label">头围 (厘米)</label>
                                <input type="number" class="form-control" id="headCircumference" name="headCircumference" step="0.1" min="25" max="60" required>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary calculate-button" disabled>计算生长发育</button>
                </form>
            </div>
        </div>
        
        <!-- 结果显示区域 -->
        <div class="results-container" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h3>计算结果</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>体重</h5>
                                <p class="weight-value">-</p>
                                <p class="weight-percentile">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>身高</h5>
                                <p class="height-value">-</p>
                                <p class="height-percentile">-</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <h5>头围</h5>
                                <p class="head-value">-</p>
                                <p class="head-percentile">-</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 评估结果 -->
                    <div class="growth-assessment mb-4"></div>
                    
                    <!-- 图表区域 -->
                    <div class="row">
                        <div class="col-md-4">
                            <div class="chart-container" style="height: 300px;">
                                <canvas id="weightChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container" style="height: 300px;">
                                <canvas id="heightChart"></canvas>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="chart-container" style="height: 300px;">
                                <canvas id="headChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 架构说明 -->
        <div class="card mt-4">
            <div class="card-header">
                <h3>新架构特点</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>🏗️ 模块化设计</h5>
                        <ul>
                            <li>BaseCalculator 基类提供通用功能</li>
                            <li>工具类库 (ValidationHelper, DateHelper, FormatHelper, ChartHelper)</li>
                            <li>CalculatorManager 统一管理</li>
                            <li>AppLoader 动态加载模块</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>✨ 功能增强</h5>
                        <ul>
                            <li>统一的数据验证机制</li>
                            <li>智能的错误处理</li>
                            <li>丰富的图表展示</li>
                            <li>专业的评估建议</li>
                        </ul>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <h5>🚀 性能优化</h5>
                        <ul>
                            <li>按需加载模块</li>
                            <li>缓存机制</li>
                            <li>防抖处理</li>
                            <li>图表复用</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>🔧 开发友好</h5>
                        <ul>
                            <li>清晰的代码结构</li>
                            <li>详细的注释文档</li>
                            <li>统一的编码规范</li>
                            <li>易于扩展维护</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 模拟WHO数据 -->
    <script>
        // 简化的WHO数据用于演示
        window.WHO_DATA = {
            boys: {
                weight: {
                    0: [2.5, 2.9, 3.3, 3.9, 4.4],
                    1: [3.4, 4.0, 4.5, 5.1, 5.8],
                    2: [4.3, 4.9, 5.6, 6.3, 7.1],
                    3: [5.0, 5.7, 6.4, 7.2, 8.0],
                    6: [6.4, 7.3, 8.0, 8.8, 9.8],
                    9: [7.1, 8.0, 8.9, 9.9, 11.0],
                    12: [7.7, 8.6, 9.6, 10.8, 12.0],
                    18: [8.4, 9.4, 10.9, 12.4, 14.1],
                    24: [9.7, 10.8, 12.2, 13.8, 15.3]
                },
                length: {
                    0: [46.1, 48.0, 49.9, 51.8, 53.7],
                    1: [50.8, 52.8, 54.7, 56.7, 58.6],
                    2: [54.4, 56.4, 58.4, 60.4, 62.4],
                    3: [57.3, 59.4, 61.4, 63.5, 65.5],
                    6: [63.3, 65.5, 67.6, 69.8, 71.9],
                    9: [67.7, 70.1, 72.3, 74.5, 76.9],
                    12: [71.0, 73.4, 75.7, 78.1, 80.5],
                    18: [76.9, 79.6, 82.3, 85.1, 87.7],
                    24: [81.7, 84.9, 87.8, 90.9, 93.9]
                },
                headCircumference: {
                    0: [32.6, 33.9, 35.0, 36.1, 37.2],
                    1: [35.1, 36.5, 37.6, 38.7, 39.8],
                    2: [36.9, 38.3, 39.5, 40.7, 41.9],
                    3: [38.1, 39.5, 40.8, 42.0, 43.3],
                    6: [40.9, 42.4, 43.7, 45.0, 46.4],
                    9: [42.9, 44.4, 45.8, 47.2, 48.7],
                    12: [44.2, 45.8, 47.2, 48.6, 50.1],
                    18: [46.1, 47.7, 49.2, 50.7, 52.3],
                    24: [47.4, 49.0, 50.5, 52.1, 53.7]
                }
            },
            girls: {
                weight: {
                    0: [2.4, 2.8, 3.2, 3.7, 4.2],
                    1: [3.2, 3.6, 4.2, 4.8, 5.5],
                    2: [3.9, 4.5, 5.1, 5.8, 6.6],
                    3: [4.5, 5.2, 5.8, 6.6, 7.5],
                    6: [5.7, 6.5, 7.3, 8.2, 9.3],
                    9: [6.2, 7.0, 8.0, 9.0, 10.2],
                    12: [6.9, 7.8, 8.9, 10.1, 11.5],
                    18: [7.7, 8.8, 10.2, 11.8, 13.5],
                    24: [8.7, 10.0, 11.5, 13.3, 15.3]
                },
                length: {
                    0: [45.4, 47.3, 49.1, 51.0, 52.9],
                    1: [49.8, 51.7, 53.7, 55.6, 57.6],
                    2: [53.0, 55.0, 57.1, 59.1, 61.1],
                    3: [55.6, 57.7, 59.8, 61.9, 64.0],
                    6: [61.2, 63.5, 65.7, 68.0, 70.3],
                    9: [65.3, 67.7, 70.1, 72.6, 75.0],
                    12: [68.9, 71.4, 74.0, 76.6, 79.2],
                    18: [75.0, 77.9, 80.7, 83.6, 86.5],
                    24: [80.0, 83.2, 86.4, 89.6, 92.9]
                },
                headCircumference: {
                    0: [32.0, 33.2, 34.3, 35.4, 36.5],
                    1: [34.2, 35.4, 36.5, 37.6, 38.7],
                    2: [35.8, 37.1, 38.3, 39.5, 40.7],
                    3: [37.1, 38.4, 39.7, 41.0, 42.2],
                    6: [40.2, 41.5, 42.8, 44.2, 45.5],
                    9: [42.1, 43.5, 44.9, 46.3, 47.7],
                    12: [43.5, 44.9, 46.4, 47.8, 49.3],
                    18: [45.2, 46.8, 48.4, 50.0, 51.6],
                    24: [46.5, 48.1, 49.8, 51.4, 53.1]
                }
            }
        };
    </script>
    
    <!-- 加载新架构 -->
    <script src="app-loader.js"></script>
    
    <script>
        // 监听应用初始化完成事件
        document.addEventListener('appInitialized', function(event) {
            console.log('应用初始化完成，开始初始化计算器');
            
            // 手动初始化增强版生长发育计算器
            if (window.EnhancedGrowthCalculator && window.calculatorManager) {
                const calculator = new EnhancedGrowthCalculator();
                window.calculatorManager.register('enhancedGrowth', calculator);
                window.calculatorManager.initCalculator('enhancedGrowth', '.calculator-form', '.results-container');
            }
        });
        
        // 监听应用初始化错误事件
        document.addEventListener('appInitError', function(event) {
            console.error('应用初始化失败:', event.detail.error);
            alert('应用初始化失败，请刷新页面重试');
        });
    </script>
    
    <style>
        .date-input-container {
            display: flex;
            gap: 5px;
        }
        
        .date-picker-button {
            flex-shrink: 0;
        }
        
        .chart-container {
            position: relative;
        }
        
        .assessment-summary.normal {
            color: #28a745;
        }
        
        .assessment-summary.attention {
            color: #ffc107;
        }
        
        .assessment-summary.concern {
            color: #dc3545;
        }
        
        .assessment-status.normal {
            color: #28a745;
        }
        
        .assessment-status.low,
        .assessment-status.high {
            color: #dc3545;
        }
    </style>
</body>
</html>
