// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否使用新的应用加载器
    if (window.appInitializer) {
        // 使用新架构，应用加载器会处理所有初始化
        console.log('使用新架构初始化应用');
        return;
    }

    // 兼容旧版本的初始化代码
    console.log('使用传统方式初始化应用');

    // 初始化工具提示
    if (typeof bootstrap !== 'undefined') {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // 初始化弹出框
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }
    
    // 汉堡菜单功能 - 使用简单直接的方法
    const hamburgerMenu = document.querySelector('.hamburger-menu');
    const sidebarOverlay = document.querySelector('.sidebar-overlay');
    
    // 确保汉堡菜单按钮存在
    if (hamburgerMenu) {
        hamburgerMenu.addEventListener('click', function() {
            // 切换body的sidebar-open类
            document.body.classList.toggle('sidebar-open');
            // 切换汉堡菜单按钮的active类
            this.classList.toggle('active');
            console.log('汉堡菜单被点击');
        });
    }
    
    // 点击遮罩层关闭侧边栏
    if (sidebarOverlay) {
        sidebarOverlay.addEventListener('click', function() {
            // 移除body的sidebar-open类
            document.body.classList.remove('sidebar-open');
            // 移除汉堡菜单按钮的active类
            if (hamburgerMenu) {
                hamburgerMenu.classList.remove('active');
            }
            console.log('遮罩层被点击');
        });
    }
    
    // 处理侧边栏分类菜单的点击事件
    const sidebarCategories = document.querySelectorAll('.sidebar-category');
    sidebarCategories.forEach(function(category) {
        // 确保所有分类菜单初始状态为关闭
        category.setAttribute('aria-expanded', 'false');
        
        // 添加点击事件
        category.addEventListener('click', function() {
            // 切换aria-expanded属性
            const isExpanded = this.getAttribute('aria-expanded') === 'true';
            this.setAttribute('aria-expanded', !isExpanded);
            
            // 找到对应的子菜单并切换active类
            const submenu = this.nextElementSibling;
            if (submenu && submenu.classList.contains('sidebar-submenu')) {
                submenu.classList.toggle('active');
            }
            console.log('侧边栏分类被点击:', this.textContent);
        });
    });

    // 添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });

    // 回到顶部按钮
    const backToTopButton = document.getElementById('back-to-top');
    if (backToTopButton) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopButton.classList.add('show');
            } else {
                backToTopButton.classList.remove('show');
            }
        });

        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 表单验证
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // 图片延迟加载
    const lazyImages = document.querySelectorAll('img.lazy');
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const image = entry.target;
                    image.src = image.dataset.src;
                    if (image.dataset.srcset) {
                        image.srcset = image.dataset.srcset;
                    }
                    image.classList.remove('lazy');
                    imageObserver.unobserve(image);
                }
            });
        });

        lazyImages.forEach(function(image) {
            imageObserver.observe(image);
        });
    } else {
        // 对于不支持IntersectionObserver的浏览器的备用方案
        lazyImages.forEach(function(image) {
            image.src = image.dataset.src;
            if (image.dataset.srcset) {
                image.srcset = image.dataset.srcset;
            }
            image.classList.remove('lazy');
        });
    }
}); 