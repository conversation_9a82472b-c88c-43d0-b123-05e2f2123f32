/**
 * 增强版生长发育计算器
 * 基于WHO标准计算婴幼儿生长发育百分位数
 * 使用新的架构和工具类
 */
class EnhancedGrowthCalculator extends BaseCalculator {
    constructor() {
        super('EnhancedGrowthCalculator');
        this.charts = {};
        this.whoData = null;
        
        // 验证规则
        this.validationRules = {
            birthDate: {
                type: 'date',
                options: { allowFuture: false, maxDate: new Date() }
            },
            measurementDate: {
                type: 'date',
                options: { allowFuture: false, maxDate: new Date() }
            },
            weight: {
                type: 'number',
                options: { min: 0.5, max: 30, allowDecimals: true, required: true }
            },
            height: {
                type: 'number',
                options: { min: 30, max: 120, allowDecimals: true, required: true }
            },
            headCircumference: {
                type: 'number',
                options: { min: 25, max: 60, allowDecimals: true, required: true }
            }
        };
    }

    /**
     * 初始化计算器
     */
    init(formSelector, resultSelector, config = {}) {
        super.init(formSelector, resultSelector, config);
        
        // 加载WHO数据
        this.loadWHOData();
        
        // 设置日期输入限制
        this.setupDateInputs();
    }

    /**
     * 加载WHO标准数据
     */
    loadWHOData() {
        // 检查全局WHO_DATA是否存在
        if (typeof WHO_DATA !== 'undefined') {
            this.whoData = WHO_DATA;
        } else {
            console.error('WHO_DATA未加载，请确保who-data.js文件已正确加载');
        }
    }

    /**
     * 设置日期输入限制
     */
    setupDateInputs() {
        const today = new Date().toISOString().split('T')[0];
        const dateInputs = this.form.querySelectorAll('input[type="date"]');
        
        dateInputs.forEach(input => {
            input.max = today;
        });

        // 添加日期选择器按钮功能
        const datePickerButtons = this.form.querySelectorAll('.date-picker-button');
        datePickerButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const dateInput = e.target.closest('.date-input-container').querySelector('input[type="date"]');
                if (dateInput && dateInput.showPicker) {
                    dateInput.showPicker();
                }
            });
        });
    }

    /**
     * 验证输入参数
     */
    validateInput(data, showErrors = true) {
        this.clearMessages();

        // 使用ValidationHelper进行批量验证
        const validationResult = ValidationHelper.validateBatch(data, this.validationRules);
        
        if (!validationResult.isValid) {
            if (showErrors) {
                Object.entries(validationResult.errors).forEach(([field, error]) => {
                    this.addError(`${this.getFieldLabel(field)}: ${error}`);
                });
            }
            return false;
        }

        // 验证性别选择
        if (!data.gender) {
            if (showErrors) this.addError('请选择宝宝性别');
            return false;
        }

        // 验证年龄范围
        const birthDate = DateHelper.parseDate(data.birthDate);
        const measurementDate = DateHelper.parseDate(data.measurementDate);
        
        if (birthDate && measurementDate) {
            const ageInMonths = DateHelper.calculateAgeInMonths(birthDate, measurementDate);
            
            if (ageInMonths < 0) {
                if (showErrors) this.addError('测量日期不能早于出生日期');
                return false;
            }
            
            if (ageInMonths > 24) {
                if (showErrors) this.addError('目前仅支持0-24个月的婴幼儿');
                return false;
            }
        }

        return true;
    }

    /**
     * 执行计算
     */
    calculate(data) {
        if (!this.whoData) {
            throw new Error('WHO标准数据未加载');
        }

        const birthDate = DateHelper.parseDate(data.birthDate);
        const measurementDate = DateHelper.parseDate(data.measurementDate);
        const ageInMonths = DateHelper.calculateAgeInMonths(birthDate, measurementDate);
        
        // 标准化性别值
        const gender = data.gender === 'boy' ? 'boys' : 'girls';
        
        // 获取标准数据
        const standardData = this.whoData[gender];
        if (!standardData) {
            throw new Error(`未找到${gender}的标准数据`);
        }

        // 插值计算标准值
        const weightStandards = this.interpolateData(ageInMonths, standardData.weight);
        const heightStandards = this.interpolateData(ageInMonths, standardData.length || standardData.height);
        const headStandards = this.interpolateData(ageInMonths, standardData.headCircumference);

        if (!weightStandards || !heightStandards || !headStandards) {
            throw new Error('无法获取标准数据');
        }

        // 计算百分位数
        const weightPercentile = this.calculatePercentile(data.weight, weightStandards);
        const heightPercentile = this.calculatePercentile(data.height, heightStandards);
        const headPercentile = this.calculatePercentile(data.headCircumference, headStandards);

        return {
            ageInMonths,
            measurements: {
                weight: {
                    value: data.weight,
                    percentile: weightPercentile,
                    standards: weightStandards,
                    unit: '千克'
                },
                height: {
                    value: data.height,
                    percentile: heightPercentile,
                    standards: heightStandards,
                    unit: '厘米'
                },
                head: {
                    value: data.headCircumference,
                    percentile: headPercentile,
                    standards: headStandards,
                    unit: '厘米'
                }
            },
            assessment: this.generateAssessment(weightPercentile, heightPercentile, headPercentile)
        };
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        // 更新百分位数显示
        this.updatePercentileDisplay(result.measurements);
        
        // 更新评估结果
        this.updateAssessmentDisplay(result.assessment);
        
        // 绘制图表
        this.drawCharts(result);
        
        // 显示结果容器
        this.showResultContainer();
    }

    /**
     * 更新百分位数显示
     */
    updatePercentileDisplay(measurements) {
        Object.entries(measurements).forEach(([type, data]) => {
            const element = document.querySelector(`.${type}-percentile`);
            if (element) {
                element.textContent = FormatHelper.formatPercentile(data.percentile);
            }
            
            const valueElement = document.querySelector(`.${type}-value`);
            if (valueElement) {
                valueElement.textContent = `${data.value} ${data.unit}`;
            }
        });
    }

    /**
     * 更新评估显示
     */
    updateAssessmentDisplay(assessment) {
        const assessmentElement = document.querySelector('.growth-assessment');
        if (assessmentElement) {
            assessmentElement.innerHTML = `
                <h4>生长发育评估</h4>
                <div class="assessment-summary ${assessment.overall.level}">
                    <strong>${assessment.overall.text}</strong>
                </div>
                <div class="assessment-details">
                    ${assessment.details.map(detail => `
                        <div class="assessment-item">
                            <span class="assessment-metric">${detail.metric}:</span>
                            <span class="assessment-status ${detail.level}">${detail.text}</span>
                        </div>
                    `).join('')}
                </div>
                ${assessment.recommendations.length > 0 ? `
                    <div class="assessment-recommendations">
                        <h5>建议</h5>
                        <ul>
                            ${assessment.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            `;
        }
    }

    /**
     * 绘制图表
     */
    drawCharts(result) {
        const { measurements, ageInMonths } = result;
        
        // 销毁旧图表
        Object.keys(this.charts).forEach(chartId => {
            if (window.chartHelper) {
                window.chartHelper.destroyChart(chartId);
            }
        });

        // 创建新图表
        if (window.chartHelper) {
            Object.entries(measurements).forEach(([type, data]) => {
                const chartId = `${type}Chart`;
                const chartData = {
                    value: data.value,
                    standards: data.standards,
                    label: `${this.getMetricLabel(type)} (${data.unit})`,
                    age: ageInMonths,
                    unit: data.unit
                };
                
                this.charts[chartId] = window.chartHelper.createGrowthChart(chartId, chartData, {
                    title: `${this.getMetricLabel(type)}发育曲线`
                });
            });
        }
    }

    /**
     * 获取字段标签
     */
    getFieldLabel(field) {
        const labels = {
            birthDate: '出生日期',
            measurementDate: '测量日期',
            weight: '体重',
            height: '身高',
            headCircumference: '头围',
            gender: '性别'
        };
        return labels[field] || field;
    }

    /**
     * 获取指标标签
     */
    getMetricLabel(metric) {
        const labels = {
            weight: '体重',
            height: '身高',
            head: '头围'
        };
        return labels[metric] || metric;
    }

    /**
     * 数据插值计算
     */
    interpolateData(age, data) {
        // 实现与原calculator.js相同的插值逻辑
        try {
            const ages = Object.keys(data).map(Number).sort((a, b) => a - b);
            
            if (age <= ages[0]) {
                return data[ages[0]];
            }
            
            if (age >= ages[ages.length - 1]) {
                return data[ages[ages.length - 1]];
            }
            
            let lowerAge = ages[0];
            let upperAge = ages[ages.length - 1];
            
            for (let i = 0; i < ages.length - 1; i++) {
                if (age >= ages[i] && age <= ages[i + 1]) {
                    lowerAge = ages[i];
                    upperAge = ages[i + 1];
                    break;
                }
            }
            
            const ratio = (age - lowerAge) / (upperAge - lowerAge);
            const lowerData = data[lowerAge];
            const upperData = data[upperAge];
            
            if (!lowerData || !upperData) {
                console.error('Missing data for interpolation');
                return null;
            }
            
            return lowerData.map((val, i) => {
                const interpolated = val + (upperData[i] - val) * ratio;
                return Number(interpolated.toFixed(2));
            });
        } catch (error) {
            console.error('Error in interpolateData:', error);
            return null;
        }
    }

    /**
     * 计算百分位数
     */
    calculatePercentile(value, standardData) {
        // 实现与原calculator.js相同的百分位数计算逻辑
        try {
            if (!standardData || !Array.isArray(standardData) || standardData.length !== 5) {
                console.error('Invalid standard data for percentile calculation');
                return null;
            }

            value = Number(value);
            if (isNaN(value)) {
                console.error('Invalid value for percentile calculation');
                return null;
            }

            if (value <= standardData[0]) {
                return 3;
            }

            if (value > standardData[4]) {
                return 97;
            }

            for (let i = 0; i < standardData.length - 1; i++) {
                if (value > standardData[i] && value <= standardData[i + 1]) {
                    const percentiles = [3, 15, 50, 85, 97];
                    const lowerPercentile = percentiles[i];
                    const upperPercentile = percentiles[i + 1];
                    
                    const ratio = (value - standardData[i]) / (standardData[i + 1] - standardData[i]);
                    const interpolatedPercentile = lowerPercentile + (upperPercentile - lowerPercentile) * ratio;
                    
                    return Math.round(interpolatedPercentile);
                }
            }

            console.error('Unable to calculate percentile');
            return null;
        } catch (error) {
            console.error('Error in calculatePercentile:', error);
            return null;
        }
    }

    /**
     * 生成评估结果
     */
    generateAssessment(weightPercentile, heightPercentile, headPercentile) {
        const assessments = [];
        const recommendations = [];
        
        // 评估体重
        if (weightPercentile < 10) {
            assessments.push({ metric: '体重', level: 'low', text: '偏轻' });
            recommendations.push('建议咨询儿科医生，检查营养摄入情况');
        } else if (weightPercentile > 90) {
            assessments.push({ metric: '体重', level: 'high', text: '偏重' });
            recommendations.push('注意控制饮食，增加运动量');
        } else {
            assessments.push({ metric: '体重', level: 'normal', text: '正常' });
        }
        
        // 评估身高
        if (heightPercentile < 10) {
            assessments.push({ metric: '身高', level: 'low', text: '偏矮' });
            recommendations.push('建议咨询儿科医生，检查生长发育情况');
        } else if (heightPercentile > 90) {
            assessments.push({ metric: '身高', level: 'high', text: '偏高' });
        } else {
            assessments.push({ metric: '身高', level: 'normal', text: '正常' });
        }
        
        // 评估头围
        if (headPercentile < 10) {
            assessments.push({ metric: '头围', level: 'low', text: '偏小' });
            recommendations.push('建议咨询儿科医生，检查头部发育情况');
        } else if (headPercentile > 90) {
            assessments.push({ metric: '头围', level: 'high', text: '偏大' });
            recommendations.push('建议咨询儿科医生，排除相关疾病');
        } else {
            assessments.push({ metric: '头围', level: 'normal', text: '正常' });
        }
        
        // 综合评估
        const normalCount = assessments.filter(a => a.level === 'normal').length;
        let overall;
        
        if (normalCount === 3) {
            overall = { level: 'normal', text: '生长发育正常' };
        } else if (normalCount >= 2) {
            overall = { level: 'attention', text: '大部分指标正常，需要关注个别指标' };
        } else {
            overall = { level: 'concern', text: '多项指标异常，建议及时就医' };
            recommendations.push('建议尽快咨询专业儿科医生');
        }
        
        return {
            overall,
            details: assessments,
            recommendations
        };
    }
}

// 导出类
window.EnhancedGrowthCalculator = EnhancedGrowthCalculator;
