/**
 * 婴儿花费计算器
 * 基于BaseCalculator，提供婴儿第一年花费估算功能
 */
class BabyCostCalculator extends BaseCalculator {
    constructor() {
        super('baby-cost');
        
        // 消费水平定义
        this.consumptionLevels = {
            basic: { label: '基础型', multiplier: 1.0 },
            comfort: { label: '舒适型', multiplier: 1.5 },
            premium: { label: '高端型', multiplier: 2.5 }
        };
        
        // 基础费用项目（月均费用，人民币）
        this.baseCosts = {
            // 必需品
            feeding: {
                label: '喂养费用',
                items: {
                    formula: { label: '奶粉', monthly: 800, essential: true },
                    bottles: { label: '奶瓶用品', monthly: 100, essential: true },
                    supplements: { label: '营养补充', monthly: 200, essential: false }
                }
            },
            diapering: {
                label: '尿布护理',
                items: {
                    diapers: { label: '尿布/纸尿裤', monthly: 400, essential: true },
                    wipes: { label: '湿巾', monthly: 80, essential: true },
                    cream: { label: '护臀膏等', monthly: 50, essential: true }
                }
            },
            clothing: {
                label: '服装用品',
                items: {
                    clothes: { label: '婴儿服装', monthly: 300, essential: true },
                    shoes: { label: '鞋袜', monthly: 100, essential: true },
                    accessories: { label: '帽子手套等', monthly: 80, essential: false }
                }
            },
            healthcare: {
                label: '医疗保健',
                items: {
                    checkups: { label: '体检疫苗', monthly: 200, essential: true },
                    medicine: { label: '常用药品', monthly: 100, essential: true },
                    insurance: { label: '医疗保险', monthly: 150, essential: false }
                }
            },
            gear: {
                label: '婴儿用品',
                items: {
                    stroller: { label: '婴儿车', monthly: 100, essential: true },
                    carseat: { label: '安全座椅', monthly: 80, essential: true },
                    crib: { label: '婴儿床', monthly: 120, essential: true },
                    highchair: { label: '餐椅', monthly: 50, essential: false }
                }
            },
            // 可选项
            toys: {
                label: '玩具娱乐',
                items: {
                    toys: { label: '玩具', monthly: 200, essential: false },
                    books: { label: '图书', monthly: 100, essential: false },
                    music: { label: '音乐用品', monthly: 80, essential: false }
                }
            },
            education: {
                label: '早期教育',
                items: {
                    classes: { label: '早教课程', monthly: 800, essential: false },
                    materials: { label: '教育用品', monthly: 200, essential: false }
                }
            },
            childcare: {
                label: '托育费用',
                items: {
                    daycare: { label: '托儿所', monthly: 2000, essential: false },
                    nanny: { label: '保姆', monthly: 4000, essential: false },
                    babysitter: { label: '临时看护', monthly: 500, essential: false }
                }
            }
        };
    }

    /**
     * 验证输入数据
     */
    validateInput(data) {
        this.clearErrors();

        // 验证消费水平
        const level = data.consumptionLevel || 'comfort';
        if (!this.consumptionLevels[level]) {
            this.addError('请选择有效的消费水平');
            return false;
        }

        // 验证计算期间
        const months = parseInt(data.calculationMonths) || 12;
        if (months < 1 || months > 36) {
            this.addError('计算期间应在1-36个月之间');
            return false;
        }

        // 验证地区系数
        const regionMultiplier = parseFloat(data.regionMultiplier) || 1.0;
        if (regionMultiplier < 0.5 || regionMultiplier > 3.0) {
            this.addError('地区消费系数应在0.5-3.0之间');
            return false;
        }

        return true;
    }

    /**
     * 执行婴儿花费计算
     */
    calculate(data) {
        if (!this.validateInput(data)) {
            return null;
        }

        const level = data.consumptionLevel || 'comfort';
        const months = parseInt(data.calculationMonths) || 12;
        const regionMultiplier = parseFloat(data.regionMultiplier) || 1.0;
        const includeOptional = data.includeOptional !== false;
        const selectedCategories = data.selectedCategories || Object.keys(this.baseCosts);

        const levelMultiplier = this.consumptionLevels[level].multiplier;
        
        // 计算各类别费用
        const categoryResults = {};
        let totalEssential = 0;
        let totalOptional = 0;
        let totalMonthly = 0;

        for (const [categoryKey, category] of Object.entries(this.baseCosts)) {
            if (!selectedCategories.includes(categoryKey)) continue;

            const categoryResult = {
                label: category.label,
                items: {},
                monthlyEssential: 0,
                monthlyOptional: 0,
                monthlyTotal: 0
            };

            for (const [itemKey, item] of Object.entries(category.items)) {
                const adjustedCost = item.monthly * levelMultiplier * regionMultiplier;
                
                categoryResult.items[itemKey] = {
                    label: item.label,
                    monthly: adjustedCost,
                    total: adjustedCost * months,
                    essential: item.essential
                };

                if (item.essential) {
                    categoryResult.monthlyEssential += adjustedCost;
                    totalEssential += adjustedCost;
                } else if (includeOptional) {
                    categoryResult.monthlyOptional += adjustedCost;
                    totalOptional += adjustedCost;
                }
            }

            categoryResult.monthlyTotal = categoryResult.monthlyEssential + categoryResult.monthlyOptional;
            categoryResults[categoryKey] = categoryResult;
        }

        totalMonthly = totalEssential + totalOptional;

        // 计算不同阶段的费用分布
        const stageBreakdown = this.calculateStageBreakdown(totalMonthly, months);

        // 生成节省建议
        const savingTips = this.generateSavingTips(level, totalMonthly, categoryResults);

        return {
            parameters: {
                level: level,
                levelLabel: this.consumptionLevels[level].label,
                months: months,
                regionMultiplier: regionMultiplier,
                includeOptional: includeOptional
            },
            costs: {
                monthly: {
                    essential: totalEssential,
                    optional: totalOptional,
                    total: totalMonthly
                },
                total: {
                    essential: totalEssential * months,
                    optional: totalOptional * months,
                    total: totalMonthly * months
                }
            },
            categories: categoryResults,
            stageBreakdown: stageBreakdown,
            savingTips: savingTips,
            comparison: this.generateComparison(totalMonthly, level)
        };
    }

    /**
     * 计算不同阶段的费用分布
     */
    calculateStageBreakdown(monthlyTotal, totalMonths) {
        const stages = [];
        
        // 0-3个月：新生儿期
        if (totalMonths > 0) {
            const months = Math.min(3, totalMonths);
            stages.push({
                period: '0-3个月',
                label: '新生儿期',
                months: months,
                multiplier: 1.2, // 新生儿期费用较高
                monthlyAvg: monthlyTotal * 1.2,
                total: monthlyTotal * 1.2 * months
            });
        }

        // 4-6个月：婴儿期
        if (totalMonths > 3) {
            const months = Math.min(3, totalMonths - 3);
            stages.push({
                period: '4-6个月',
                label: '婴儿期',
                months: months,
                multiplier: 1.0,
                monthlyAvg: monthlyTotal,
                total: monthlyTotal * months
            });
        }

        // 7-12个月：较大婴儿期
        if (totalMonths > 6) {
            const months = Math.min(6, totalMonths - 6);
            stages.push({
                period: '7-12个月',
                label: '较大婴儿期',
                months: months,
                multiplier: 1.1, // 开始添加辅食，费用略增
                monthlyAvg: monthlyTotal * 1.1,
                total: monthlyTotal * 1.1 * months
            });
        }

        // 13个月以上：幼儿期
        if (totalMonths > 12) {
            const months = totalMonths - 12;
            stages.push({
                period: '13个月+',
                label: '幼儿期',
                months: months,
                multiplier: 1.3, // 幼儿期活动增多，费用增加
                monthlyAvg: monthlyTotal * 1.3,
                total: monthlyTotal * 1.3 * months
            });
        }

        return stages;
    }

    /**
     * 生成节省建议
     */
    generateSavingTips(level, monthlyTotal, categories) {
        const tips = [];

        // 通用节省建议
        tips.push('购买婴儿用品时可以选择品牌促销期或团购');
        tips.push('二手婴儿用品（如婴儿车、婴儿床）可以大幅节省费用');
        tips.push('母乳喂养可以显著减少奶粉费用');

        // 根据消费水平给建议
        if (level === 'premium') {
            tips.push('考虑降低部分非必需品的消费标准');
            tips.push('高端产品可以选择性购买，不必全部选择最贵的');
        } else if (level === 'basic') {
            tips.push('在保证安全的前提下，可以选择性价比更高的产品');
            tips.push('关注母婴用品的优惠活动和折扣信息');
        }

        // 根据费用分布给建议
        const sortedCategories = Object.entries(categories)
            .sort((a, b) => b[1].monthlyTotal - a[1].monthlyTotal);

        if (sortedCategories.length > 0) {
            const highestCategory = sortedCategories[0];
            tips.push(`${highestCategory[1].label}是最大支出项，可以重点关注这方面的节省`);
        }

        // 具体类别建议
        if (categories.childcare && categories.childcare.monthlyTotal > 1000) {
            tips.push('托育费用较高，可以考虑家庭照护或寻找性价比更高的托育机构');
        }

        if (categories.education && categories.education.monthlyTotal > 500) {
            tips.push('早教课程可以选择性参加，家庭教育同样重要');
        }

        return tips;
    }

    /**
     * 生成不同消费水平的对比
     */
    generateComparison(currentMonthly, currentLevel) {
        const comparisons = {};
        
        for (const [level, config] of Object.entries(this.consumptionLevels)) {
            if (level === currentLevel) continue;
            
            const adjustedMonthly = (currentMonthly / this.consumptionLevels[currentLevel].multiplier) * config.multiplier;
            const difference = adjustedMonthly - currentMonthly;
            
            comparisons[level] = {
                label: config.label,
                monthly: adjustedMonthly,
                difference: difference,
                percentage: ((difference / currentMonthly) * 100).toFixed(1)
            };
        }
        
        return comparisons;
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        if (!result) return;

        const html = `
            <div class="result-card">
                <h4><i class="fas fa-dollar-sign text-success me-2"></i>婴儿花费预算报告</h4>
                
                <div class="summary-section">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="cost-summary">
                                <h6>月均费用</h6>
                                <div class="cost-amount text-primary">¥${result.costs.monthly.total.toFixed(0)}</div>
                                <small class="text-muted">${result.parameters.levelLabel}</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="cost-summary">
                                <h6>必需品费用</h6>
                                <div class="cost-amount text-success">¥${result.costs.monthly.essential.toFixed(0)}</div>
                                <small class="text-muted">每月</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="cost-summary">
                                <h6>总预算</h6>
                                <div class="cost-amount text-warning">¥${result.costs.total.total.toFixed(0)}</div>
                                <small class="text-muted">${result.parameters.months}个月</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="categories-section mt-4">
                    <h6><i class="fas fa-list me-2"></i>费用明细：</h6>
                    <div class="row">
                        ${Object.entries(result.categories).map(([key, category]) => `
                            <div class="col-md-6 mb-3">
                                <div class="category-card">
                                    <h6>${category.label}</h6>
                                    <div class="category-cost">月均：¥${category.monthlyTotal.toFixed(0)}</div>
                                    <div class="category-items">
                                        ${Object.entries(category.items).map(([itemKey, item]) => `
                                            <div class="item-row">
                                                <span class="item-name">${item.label}</span>
                                                <span class="item-cost">¥${item.monthly.toFixed(0)}/月</span>
                                                ${item.essential ? '<span class="badge badge-sm bg-primary">必需</span>' : '<span class="badge badge-sm bg-secondary">可选</span>'}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${result.stageBreakdown.length > 1 ? `
                <div class="stages-section mt-4">
                    <h6><i class="fas fa-chart-line me-2"></i>阶段费用分布：</h6>
                    <div class="stages-list">
                        ${result.stageBreakdown.map(stage => `
                            <div class="stage-item">
                                <div class="stage-period">${stage.period}</div>
                                <div class="stage-label">${stage.label}</div>
                                <div class="stage-cost">¥${stage.total.toFixed(0)} (${stage.months}个月)</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}

                <div class="tips-section mt-4">
                    <h6><i class="fas fa-lightbulb me-2"></i>节省建议：</h6>
                    <ul class="tips-list">
                        ${result.savingTips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>

                ${Object.keys(result.comparison).length > 0 ? `
                <div class="comparison-section mt-4">
                    <h6><i class="fas fa-balance-scale me-2"></i>消费水平对比：</h6>
                    <div class="comparison-list">
                        ${Object.entries(result.comparison).map(([level, comp]) => `
                            <div class="comparison-item">
                                <span class="level-name">${comp.label}：</span>
                                <span class="level-cost">¥${comp.monthly.toFixed(0)}/月</span>
                                <span class="level-diff ${comp.difference > 0 ? 'text-danger' : 'text-success'}">
                                    ${comp.difference > 0 ? '+' : ''}${comp.difference.toFixed(0)} (${comp.percentage}%)
                                </span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        if (this.resultContainer) {
            this.resultContainer.innerHTML = html;
            this.resultContainer.style.display = 'block';
            this.resultContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// 注册到全局
window.BabyCostCalculator = BabyCostCalculator;
