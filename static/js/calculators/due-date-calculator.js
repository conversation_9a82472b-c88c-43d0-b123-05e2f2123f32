/**
 * 预产期计算器
 * 根据末次月经日期计算预产期和孕周信息
 */
class DueDateCalculator extends BaseCalculator {
    constructor() {
        super('DueDateCalculator');
    }

    /**
     * 验证输入参数
     */
    validateInput(data, showErrors = true) {
        this.clearMessages();

        // 验证末次月经日期
        const lastPeriod = this.parseDate(data.lastPeriod);
        if (!lastPeriod) {
            if (showErrors) this.addError('请输入有效的末次月经日期');
        }

        // 验证周期长度
        if (!data.cycleLength || data.cycleLength < 21 || data.cycleLength > 35) {
            if (showErrors) this.addError('月经周期长度应在21-35天之间');
        }

        // 验证日期不能是未来
        if (lastPeriod && lastPeriod > new Date()) {
            if (showErrors) this.addError('末次月经日期不能是未来日期');
        }

        // 验证日期不能太久远
        if (lastPeriod) {
            const daysSince = this.calculateDaysDifference(lastPeriod, new Date());
            if (daysSince > 300) {
                if (showErrors) this.addError('末次月经日期距今不能超过300天');
            }
        }

        return this.errors.length === 0;
    }

    /**
     * 执行计算
     */
    calculate(data) {
        const lastPeriod = this.parseDate(data.lastPeriod);
        const cycleLength = parseInt(data.cycleLength) || 28;
        const today = new Date();

        // 计算预产期 (内格尔规则: 末次月经 + 280天)
        const dueDate = new Date(lastPeriod);
        dueDate.setDate(dueDate.getDate() + 280);

        // 计算当前孕周
        const daysPregnant = this.calculateDaysDifference(lastPeriod, today);
        const weeksPregnant = Math.floor(daysPregnant / 7);
        const daysRemainder = daysPregnant % 7;

        // 计算孕期阶段
        const firstTrimesterEnd = new Date(lastPeriod);
        firstTrimesterEnd.setDate(firstTrimesterEnd.getDate() + 91); // 13周

        const secondTrimesterEnd = new Date(lastPeriod);
        secondTrimesterEnd.setDate(secondTrimesterEnd.getDate() + 182); // 26周

        // 计算排卵日和受孕日
        const ovulationDate = new Date(lastPeriod);
        ovulationDate.setDate(ovulationDate.getDate() + cycleLength - 14);

        const conceptionDate = new Date(ovulationDate);

        // 计算距离预产期的天数
        const daysUntilDue = this.calculateDaysDifference(today, dueDate);

        return {
            lastPeriod: {
                date: lastPeriod,
                formatted: this.formatDate(lastPeriod)
            },
            dueDate: {
                date: dueDate,
                formatted: this.formatDate(dueDate)
            },
            currentPregnancy: {
                weeks: weeksPregnant,
                days: daysRemainder,
                totalDays: daysPregnant,
                daysUntilDue: daysUntilDue
            },
            trimester: this.getCurrentTrimester(daysPregnant),
            trimesters: {
                first: {
                    start: lastPeriod,
                    end: firstTrimesterEnd,
                    startFormatted: this.formatDate(lastPeriod),
                    endFormatted: this.formatDate(firstTrimesterEnd)
                },
                second: {
                    start: new Date(firstTrimesterEnd.getTime() + 24 * 60 * 60 * 1000),
                    end: secondTrimesterEnd,
                    startFormatted: this.formatDate(new Date(firstTrimesterEnd.getTime() + 24 * 60 * 60 * 1000)),
                    endFormatted: this.formatDate(secondTrimesterEnd)
                },
                third: {
                    start: new Date(secondTrimesterEnd.getTime() + 24 * 60 * 60 * 1000),
                    end: new Date(dueDate.getTime() - 24 * 60 * 60 * 1000),
                    startFormatted: this.formatDate(new Date(secondTrimesterEnd.getTime() + 24 * 60 * 60 * 1000)),
                    endFormatted: this.formatDate(new Date(dueDate.getTime() - 24 * 60 * 60 * 1000))
                }
            },
            conception: {
                ovulationDate: ovulationDate,
                conceptionDate: conceptionDate,
                ovulationFormatted: this.formatDate(ovulationDate),
                conceptionFormatted: this.formatDate(conceptionDate)
            },
            milestones: this.getPregnancyMilestones(lastPeriod)
        };
    }

    /**
     * 获取当前孕期阶段
     */
    getCurrentTrimester(daysPregnant) {
        if (daysPregnant <= 91) {
            return {
                number: 1,
                name: '孕早期',
                description: '胚胎发育的关键时期'
            };
        } else if (daysPregnant <= 182) {
            return {
                number: 2,
                name: '孕中期',
                description: '相对稳定的时期'
            };
        } else {
            return {
                number: 3,
                name: '孕晚期',
                description: '准备分娩的时期'
            };
        }
    }

    /**
     * 获取孕期重要里程碑
     */
    getPregnancyMilestones(lastPeriod) {
        const milestones = [];

        // 重要检查时间点
        const checkpoints = [
            { weeks: 6, description: '首次产检建议时间' },
            { weeks: 12, description: 'NT检查时间' },
            { weeks: 16, description: '唐氏筛查时间' },
            { weeks: 20, description: '大排畸检查时间' },
            { weeks: 24, description: '糖耐量检查时间' },
            { weeks: 28, description: '孕晚期开始' },
            { weeks: 32, description: '胎位检查时间' },
            { weeks: 36, description: '足月前检查' },
            { weeks: 40, description: '预产期' }
        ];

        checkpoints.forEach(checkpoint => {
            const milestoneDate = new Date(lastPeriod);
            milestoneDate.setDate(milestoneDate.getDate() + checkpoint.weeks * 7);
            
            milestones.push({
                weeks: checkpoint.weeks,
                date: milestoneDate,
                formatted: this.formatDate(milestoneDate),
                description: checkpoint.description
            });
        });

        return milestones;
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        // 更新预产期显示
        const dueDateElement = document.querySelector('.due-date-result');
        if (dueDateElement) {
            dueDateElement.textContent = result.dueDate.formatted;
        }

        // 更新当前孕周显示
        const currentWeeksElement = document.querySelector('.current-weeks');
        if (currentWeeksElement) {
            currentWeeksElement.textContent = 
                `${result.currentPregnancy.weeks}周${result.currentPregnancy.days}天`;
        }

        // 更新孕期阶段显示
        const trimesterElement = document.querySelector('.current-trimester');
        if (trimesterElement) {
            trimesterElement.textContent = 
                `${result.trimester.name} (${result.trimester.description})`;
        }

        // 更新距离预产期天数
        const daysUntilElement = document.querySelector('.days-until-due');
        if (daysUntilElement) {
            if (result.currentPregnancy.daysUntilDue > 0) {
                daysUntilElement.textContent = `还有 ${result.currentPregnancy.daysUntilDue} 天`;
            } else if (result.currentPregnancy.daysUntilDue === 0) {
                daysUntilElement.textContent = '今天是预产期！';
            } else {
                daysUntilElement.textContent = `已过预产期 ${Math.abs(result.currentPregnancy.daysUntilDue)} 天`;
            }
        }

        // 显示孕期里程碑
        this.displayMilestones(result.milestones);

        // 显示三个孕期的时间范围
        this.displayTrimesters(result.trimesters);
    }

    /**
     * 显示孕期里程碑
     */
    displayMilestones(milestones) {
        const milestonesContainer = document.querySelector('.pregnancy-milestones');
        if (!milestonesContainer) return;

        const today = new Date();
        let html = '<h4>孕期重要时间点</h4><ul class="milestones-list">';

        milestones.forEach(milestone => {
            const isPast = milestone.date < today;
            const isToday = this.isSameDay(milestone.date, today);
            const cssClass = isPast ? 'past' : isToday ? 'today' : 'future';

            html += `
                <li class="milestone-item ${cssClass}">
                    <span class="milestone-week">${milestone.weeks}周</span>
                    <span class="milestone-date">${milestone.formatted}</span>
                    <span class="milestone-desc">${milestone.description}</span>
                </li>
            `;
        });

        html += '</ul>';
        milestonesContainer.innerHTML = html;
    }

    /**
     * 显示三个孕期的时间范围
     */
    displayTrimesters(trimesters) {
        const trimestersContainer = document.querySelector('.trimesters-info');
        if (!trimestersContainer) return;

        const html = `
            <h4>孕期阶段</h4>
            <div class="trimester-cards">
                <div class="trimester-card">
                    <h5>孕早期 (1-13周)</h5>
                    <p>${trimesters.first.startFormatted} - ${trimesters.first.endFormatted}</p>
                </div>
                <div class="trimester-card">
                    <h5>孕中期 (14-26周)</h5>
                    <p>${trimesters.second.startFormatted} - ${trimesters.second.endFormatted}</p>
                </div>
                <div class="trimester-card">
                    <h5>孕晚期 (27-40周)</h5>
                    <p>${trimesters.third.startFormatted} - ${trimesters.third.endFormatted}</p>
                </div>
            </div>
        `;

        trimestersContainer.innerHTML = html;
    }

    /**
     * 判断两个日期是否是同一天
     */
    isSameDay(date1, date2) {
        return date1.getFullYear() === date2.getFullYear() &&
               date1.getMonth() === date2.getMonth() &&
               date1.getDate() === date2.getDate();
    }
}

// 导出类
window.DueDateCalculator = DueDateCalculator;
