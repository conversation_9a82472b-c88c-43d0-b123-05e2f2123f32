/**
 * 受孕日期计算器
 * 基于BaseCalculator，提供受孕日期和排卵期计算功能
 */
class ConceptionCalculator extends BaseCalculator {
    constructor() {
        super('conception');
        this.calculationType = 'from_lmp'; // 'from_lmp' 或 'from_due_date'
    }

    /**
     * 验证输入数据
     */
    validateInput(data) {
        this.clearErrors();

        if (data.calculationType === 'from_lmp') {
            // 从末次月经计算
            if (!data.lastPeriod) {
                this.addError('请输入末次月经日期');
                return false;
            }

            const lastPeriodDate = this.parseDate(data.lastPeriod);
            if (!lastPeriodDate) {
                this.addError('末次月经日期格式不正确');
                return false;
            }

            // 检查日期是否在合理范围内（不能是未来日期，不能超过1年前）
            const today = new Date();
            const oneYearAgo = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
            
            if (lastPeriodDate > today) {
                this.addError('末次月经日期不能是未来日期');
                return false;
            }

            if (lastPeriodDate < oneYearAgo) {
                this.addError('末次月经日期不能超过一年前');
                return false;
            }

            // 验证月经周期长度
            const cycleLength = parseInt(data.cycleLength) || 28;
            if (cycleLength < 21 || cycleLength > 35) {
                this.addWarning('月经周期长度通常在21-35天之间');
            }

        } else if (data.calculationType === 'from_due_date') {
            // 从预产期计算
            if (!data.dueDate) {
                this.addError('请输入预产期');
                return false;
            }

            const dueDateObj = this.parseDate(data.dueDate);
            if (!dueDateObj) {
                this.addError('预产期格式不正确');
                return false;
            }

            // 检查预产期是否合理（应该在未来，但不超过1年）
            const today = new Date();
            const oneYearLater = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate());
            
            if (dueDateObj < today) {
                this.addError('预产期应该是未来日期');
                return false;
            }

            if (dueDateObj > oneYearLater) {
                this.addError('预产期不应超过一年后');
                return false;
            }
        } else {
            this.addError('请选择计算方式');
            return false;
        }

        return true;
    }

    /**
     * 执行受孕日期计算
     */
    calculate(data) {
        if (!this.validateInput(data)) {
            return null;
        }

        let result = {};

        if (data.calculationType === 'from_lmp') {
            result = this.calculateFromLastPeriod(data);
        } else {
            result = this.calculateFromDueDate(data);
        }

        return result;
    }

    /**
     * 从末次月经计算受孕日期
     */
    calculateFromLastPeriod(data) {
        const lastPeriod = this.parseDate(data.lastPeriod);
        const cycleLength = parseInt(data.cycleLength) || 28;
        
        // 排卵日通常在下次月经前14天
        const ovulationDay = cycleLength - 14;
        const ovulationDate = new Date(lastPeriod);
        ovulationDate.setDate(lastPeriod.getDate() + ovulationDay);
        
        // 受孕窗口期（排卵前5天到排卵后1天）
        const fertilityWindowStart = new Date(ovulationDate);
        fertilityWindowStart.setDate(ovulationDate.getDate() - 5);
        
        const fertilityWindowEnd = new Date(ovulationDate);
        fertilityWindowEnd.setDate(ovulationDate.getDate() + 1);
        
        // 最可能的受孕日期（排卵日前后1天）
        const conceptionStart = new Date(ovulationDate);
        conceptionStart.setDate(ovulationDate.getDate() - 1);
        
        const conceptionEnd = new Date(ovulationDate);
        conceptionEnd.setDate(ovulationDate.getDate() + 1);
        
        // 计算预产期（从末次月经开始算280天）
        const dueDate = new Date(lastPeriod);
        dueDate.setDate(lastPeriod.getDate() + 280);
        
        // 计算当前孕周（如果已怀孕）
        const today = new Date();
        const daysSinceLastPeriod = Math.floor((today - lastPeriod) / (1000 * 60 * 60 * 24));
        const currentWeek = Math.floor(daysSinceLastPeriod / 7);
        const currentDay = daysSinceLastPeriod % 7;
        
        return {
            calculationType: 'from_lmp',
            lastPeriod: {
                date: lastPeriod,
                formatted: this.formatDate(lastPeriod)
            },
            cycleLength: cycleLength,
            ovulationDate: {
                date: ovulationDate,
                formatted: this.formatDate(ovulationDate)
            },
            fertilityWindow: {
                start: {
                    date: fertilityWindowStart,
                    formatted: this.formatDate(fertilityWindowStart)
                },
                end: {
                    date: fertilityWindowEnd,
                    formatted: this.formatDate(fertilityWindowEnd)
                },
                duration: '6天'
            },
            conceptionPeriod: {
                start: {
                    date: conceptionStart,
                    formatted: this.formatDate(conceptionStart)
                },
                end: {
                    date: conceptionEnd,
                    formatted: this.formatDate(conceptionEnd)
                },
                mostLikely: {
                    date: ovulationDate,
                    formatted: this.formatDate(ovulationDate)
                }
            },
            dueDate: {
                date: dueDate,
                formatted: this.formatDate(dueDate)
            },
            currentPregnancy: daysSinceLastPeriod > 0 ? {
                weeks: currentWeek,
                days: currentDay,
                totalDays: daysSinceLastPeriod,
                formatted: `${currentWeek}周${currentDay}天`
            } : null,
            tips: this.getConceptionTips(ovulationDate)
        };
    }

    /**
     * 从预产期计算受孕日期
     */
    calculateFromDueDate(data) {
        const dueDate = this.parseDate(data.dueDate);
        
        // 从预产期反推末次月经（预产期减去280天）
        const lastPeriod = new Date(dueDate);
        lastPeriod.setDate(dueDate.getDate() - 280);
        
        // 受孕日期通常在末次月经后14天左右（假设28天周期）
        const conceptionDate = new Date(lastPeriod);
        conceptionDate.setDate(lastPeriod.getDate() + 14);
        
        // 受孕窗口期
        const conceptionStart = new Date(conceptionDate);
        conceptionStart.setDate(conceptionDate.getDate() - 3);
        
        const conceptionEnd = new Date(conceptionDate);
        conceptionEnd.setDate(conceptionDate.getDate() + 3);
        
        // 计算当前孕周
        const today = new Date();
        const daysSinceLastPeriod = Math.floor((today - lastPeriod) / (1000 * 60 * 60 * 24));
        const currentWeek = Math.floor(daysSinceLastPeriod / 7);
        const currentDay = daysSinceLastPeriod % 7;
        
        return {
            calculationType: 'from_due_date',
            dueDate: {
                date: dueDate,
                formatted: this.formatDate(dueDate)
            },
            estimatedLastPeriod: {
                date: lastPeriod,
                formatted: this.formatDate(lastPeriod)
            },
            conceptionPeriod: {
                start: {
                    date: conceptionStart,
                    formatted: this.formatDate(conceptionStart)
                },
                end: {
                    date: conceptionEnd,
                    formatted: this.formatDate(conceptionEnd)
                },
                mostLikely: {
                    date: conceptionDate,
                    formatted: this.formatDate(conceptionDate)
                }
            },
            currentPregnancy: daysSinceLastPeriod > 0 ? {
                weeks: currentWeek,
                days: currentDay,
                totalDays: daysSinceLastPeriod,
                formatted: `${currentWeek}周${currentDay}天`
            } : null,
            tips: this.getConceptionTips(conceptionDate)
        };
    }

    /**
     * 获取受孕相关提示
     */
    getConceptionTips(ovulationDate) {
        const today = new Date();
        const daysToOvulation = Math.floor((ovulationDate - today) / (1000 * 60 * 60 * 24));
        
        const tips = [];
        
        if (daysToOvulation > 0) {
            if (daysToOvulation <= 7) {
                tips.push(`距离排卵期还有${daysToOvulation}天，这是备孕的关键时期`);
                tips.push('建议在排卵前2-3天开始增加同房频率');
            } else {
                tips.push('现在还不是排卵期，可以先调理身体状态');
            }
        } else if (daysToOvulation === 0) {
            tips.push('今天是预计的排卵日，受孕概率最高');
        } else {
            tips.push('排卵期已过，如果已经同房，请耐心等待结果');
        }
        
        tips.push('保持良好的生活习惯，适当运动，避免压力过大');
        tips.push('补充叶酸，戒烟戒酒，为怀孕做好准备');
        
        return tips;
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        if (!result) return;

        let html = `
            <div class="result-card">
                <h4><i class="fas fa-heart text-danger me-2"></i>受孕日期计算结果</h4>
        `;

        if (result.calculationType === 'from_lmp') {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>末次月经：</label>
                            <span>${result.lastPeriod.formatted}</span>
                        </div>
                        <div class="info-item">
                            <label>月经周期：</label>
                            <span>${result.cycleLength}天</span>
                        </div>
                        <div class="info-item">
                            <label>预计排卵日：</label>
                            <span class="text-primary">${result.ovulationDate.formatted}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>易孕期：</label>
                            <span>${result.fertilityWindow.start.formatted} - ${result.fertilityWindow.end.formatted}</span>
                        </div>
                        <div class="info-item">
                            <label>最可能受孕日：</label>
                            <span class="text-success">${result.conceptionPeriod.mostLikely.formatted}</span>
                        </div>
                        <div class="info-item">
                            <label>预产期：</label>
                            <span>${result.dueDate.formatted}</span>
                        </div>
                    </div>
                </div>
            `;
        } else {
            html += `
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>预产期：</label>
                            <span>${result.dueDate.formatted}</span>
                        </div>
                        <div class="info-item">
                            <label>推算末次月经：</label>
                            <span>${result.estimatedLastPeriod.formatted}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-item">
                            <label>可能受孕期：</label>
                            <span>${result.conceptionPeriod.start.formatted} - ${result.conceptionPeriod.end.formatted}</span>
                        </div>
                        <div class="info-item">
                            <label>最可能受孕日：</label>
                            <span class="text-success">${result.conceptionPeriod.mostLikely.formatted}</span>
                        </div>
                    </div>
                </div>
            `;
        }

        if (result.currentPregnancy) {
            html += `
                <div class="alert alert-info mt-3">
                    <i class="fas fa-baby me-2"></i>
                    当前孕期：${result.currentPregnancy.formatted}
                </div>
            `;
        }

        if (result.tips && result.tips.length > 0) {
            html += `
                <div class="tips-section mt-3">
                    <h6><i class="fas fa-lightbulb me-2"></i>温馨提示：</h6>
                    <ul class="tips-list">
            `;
            result.tips.forEach(tip => {
                html += `<li>${tip}</li>`;
            });
            html += `
                    </ul>
                </div>
            `;
        }

        html += `</div>`;

        if (this.resultContainer) {
            this.resultContainer.innerHTML = html;
            this.resultContainer.style.display = 'block';
            this.resultContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// 注册到全局
window.ConceptionCalculator = ConceptionCalculator;
