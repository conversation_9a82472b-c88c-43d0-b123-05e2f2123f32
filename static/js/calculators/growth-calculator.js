/**
 * 生长发育计算器
 * 基于WHO标准计算婴儿体重、身高、头围的百分位数
 */
class GrowthCalculator extends BaseCalculator {
    constructor() {
        super('GrowthCalculator');
        
        // WHO生长标准数据 (简化版本，实际应用中需要完整数据)
        this.whoStandards = {
            // 男孩体重标准 (月龄: [P3, P15, P50, P85, P97])
            boys: {
                weight: {
                    0: [2.5, 2.9, 3.3, 3.9, 4.4],
                    1: [3.4, 4.0, 4.5, 5.1, 5.8],
                    2: [4.3, 4.9, 5.6, 6.3, 7.1],
                    3: [5.0, 5.7, 6.4, 7.2, 8.0],
                    6: [6.4, 7.3, 8.0, 8.8, 9.8],
                    9: [7.1, 8.0, 8.9, 9.9, 11.0],
                    12: [7.7, 8.6, 9.6, 10.8, 12.0],
                    18: [8.4, 9.4, 10.9, 12.4, 14.1],
                    24: [9.7, 10.8, 12.2, 13.8, 15.3]
                },
                height: {
                    0: [46.1, 48.0, 49.9, 51.8, 53.7],
                    1: [50.8, 52.8, 54.7, 56.7, 58.6],
                    2: [54.4, 56.4, 58.4, 60.4, 62.4],
                    3: [57.3, 59.4, 61.4, 63.5, 65.5],
                    6: [63.3, 65.5, 67.6, 69.8, 71.9],
                    9: [67.7, 70.1, 72.3, 74.5, 76.9],
                    12: [71.0, 73.4, 75.7, 78.1, 80.5],
                    18: [76.9, 79.6, 82.3, 85.1, 87.7],
                    24: [81.7, 84.9, 87.8, 90.9, 93.9]
                }
            },
            // 女孩体重标准
            girls: {
                weight: {
                    0: [2.4, 2.8, 3.2, 3.7, 4.2],
                    1: [3.2, 3.6, 4.2, 4.8, 5.5],
                    2: [3.9, 4.5, 5.1, 5.8, 6.6],
                    3: [4.5, 5.2, 5.8, 6.6, 7.5],
                    6: [5.7, 6.5, 7.3, 8.2, 9.3],
                    9: [6.2, 7.0, 8.0, 9.0, 10.2],
                    12: [6.9, 7.8, 8.9, 10.1, 11.5],
                    18: [7.7, 8.8, 10.2, 11.8, 13.5],
                    24: [8.7, 10.0, 11.5, 13.3, 15.3]
                },
                height: {
                    0: [45.4, 47.3, 49.1, 51.0, 52.9],
                    1: [49.8, 51.7, 53.7, 55.6, 57.6],
                    2: [53.0, 55.0, 57.1, 59.1, 61.1],
                    3: [55.6, 57.7, 59.8, 61.9, 64.0],
                    6: [61.2, 63.5, 65.7, 68.0, 70.3],
                    9: [65.3, 67.7, 70.1, 72.6, 75.0],
                    12: [68.9, 71.4, 74.0, 76.6, 79.2],
                    18: [74.9, 77.7, 80.7, 83.7, 86.7],
                    24: [80.0, 83.2, 86.4, 89.6, 92.9]
                }
            }
        };
    }

    /**
     * 验证输入参数
     */
    validateInput(data, showErrors = true) {
        this.clearMessages();

        // 验证性别
        if (!data.gender || !['boy', 'girl'].includes(data.gender)) {
            if (showErrors) this.addError('请选择宝宝性别');
        }

        // 验证出生日期
        const birthDate = this.parseDate(data.birthDate);
        if (!birthDate) {
            if (showErrors) this.addError('请输入有效的出生日期');
        }

        // 验证测量日期
        const measurementDate = this.parseDate(data.measurementDate);
        if (!measurementDate) {
            if (showErrors) this.addError('请输入有效的测量日期');
        }

        // 验证日期逻辑
        if (birthDate && measurementDate) {
            if (measurementDate < birthDate) {
                if (showErrors) this.addError('测量日期不能早于出生日期');
            }
            
            const ageInMonths = this.calculateMonthsDifference(birthDate, measurementDate);
            if (ageInMonths > 24) {
                if (showErrors) this.addError('此计算器适用于0-24个月的婴儿');
            }
        }

        // 验证体重
        if (!data.weight || data.weight <= 0 || data.weight > 30) {
            if (showErrors) this.addError('请输入有效的体重 (0-30千克)');
        }

        // 验证身高
        if (!data.height || data.height <= 0 || data.height > 120) {
            if (showErrors) this.addError('请输入有效的身高 (0-120厘米)');
        }

        // 验证头围
        if (!data.headCircumference || data.headCircumference <= 0 || data.headCircumference > 60) {
            if (showErrors) this.addError('请输入有效的头围 (0-60厘米)');
        }

        return this.errors.length === 0;
    }

    /**
     * 执行计算
     */
    calculate(data) {
        const birthDate = this.parseDate(data.birthDate);
        const measurementDate = this.parseDate(data.measurementDate);
        const ageInMonths = this.calculateMonthsDifference(birthDate, measurementDate);
        
        // 计算百分位数
        const weightPercentile = this.calculatePercentile(
            data.gender, 'weight', ageInMonths, data.weight
        );
        
        const heightPercentile = this.calculatePercentile(
            data.gender, 'height', ageInMonths, data.height
        );
        
        // 头围百分位数 (简化计算)
        const headPercentile = this.calculateHeadPercentile(
            data.gender, ageInMonths, data.headCircumference
        );

        return {
            ageInMonths: ageInMonths,
            ageInDays: this.calculateDaysDifference(birthDate, measurementDate),
            weight: {
                value: data.weight,
                percentile: weightPercentile,
                formatted: this.formatPercentile(weightPercentile)
            },
            height: {
                value: data.height,
                percentile: heightPercentile,
                formatted: this.formatPercentile(heightPercentile)
            },
            headCircumference: {
                value: data.headCircumference,
                percentile: headPercentile,
                formatted: this.formatPercentile(headPercentile)
            },
            interpretation: this.getInterpretation(weightPercentile, heightPercentile, headPercentile)
        };
    }

    /**
     * 计算百分位数
     */
    calculatePercentile(gender, measurement, ageInMonths, value) {
        const genderKey = gender === 'boy' ? 'boys' : 'girls';
        const standards = this.whoStandards[genderKey][measurement];
        
        // 找到最接近的月龄
        const availableAges = Object.keys(standards).map(Number).sort((a, b) => a - b);
        let targetAge = availableAges[0];
        
        for (let age of availableAges) {
            if (ageInMonths >= age) {
                targetAge = age;
            } else {
                break;
            }
        }
        
        const standardValues = standards[targetAge];
        if (!standardValues) {
            return 50; // 默认返回50百分位
        }
        
        // 简化的百分位计算
        const [p3, p15, p50, p85, p97] = standardValues;
        
        if (value <= p3) return 3;
        if (value <= p15) return 3 + (value - p3) / (p15 - p3) * 12;
        if (value <= p50) return 15 + (value - p15) / (p50 - p15) * 35;
        if (value <= p85) return 50 + (value - p50) / (p85 - p50) * 35;
        if (value <= p97) return 85 + (value - p85) / (p97 - p85) * 12;
        return 97;
    }

    /**
     * 计算头围百分位数 (简化版本)
     */
    calculateHeadPercentile(gender, ageInMonths, headCircumference) {
        // 简化的头围计算，实际应用中需要WHO头围标准
        const baseHead = gender === 'boy' ? 35 : 34.5;
        const expectedHead = baseHead + ageInMonths * 0.5;
        const diff = headCircumference - expectedHead;
        
        // 简单的正态分布近似
        const percentile = 50 + diff * 10;
        return Math.max(1, Math.min(99, percentile));
    }

    /**
     * 获取结果解释
     */
    getInterpretation(weightP, heightP, headP) {
        const interpretations = [];
        
        if (weightP < 3 || heightP < 3) {
            interpretations.push('建议咨询儿科医生，确认生长发育情况');
        } else if (weightP > 97 || heightP > 97) {
            interpretations.push('生长指标较高，建议定期监测');
        } else {
            interpretations.push('生长发育指标在正常范围内');
        }
        
        return interpretations;
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        // 更新百分位数显示
        const weightElement = document.querySelector('.weight-percentile');
        const heightElement = document.querySelector('.height-percentile');
        const headElement = document.querySelector('.head-percentile');
        
        if (weightElement) {
            weightElement.textContent = `${result.weight.value} 千克 (${result.weight.formatted})`;
        }
        
        if (heightElement) {
            heightElement.textContent = `${result.height.value} 厘米 (${result.height.formatted})`;
        }
        
        if (headElement) {
            headElement.textContent = `${result.headCircumference.value} 厘米 (${result.headCircumference.formatted})`;
        }

        // 生成图表 (如果需要)
        this.generateCharts(result);
    }

    /**
     * 生成生长曲线图表
     */
    generateCharts(result) {
        // 这里可以集成Chart.js或其他图表库
        // 为了简化，这里只是示例
        console.log('生成生长曲线图表:', result);
    }
}

// 导出类
window.GrowthCalculator = GrowthCalculator;
