/**
 * 孕期体重计算器
 * 基于BaseCalculator，提供孕期体重管理和监测功能
 */
class PregnancyWeightCalculator extends BaseCalculator {
    constructor() {
        super('pregnancy-weight');
        
        // BMI分类和推荐增重范围（单胎）
        this.weightGainRanges = {
            underweight: { min: 12.5, max: 18, label: '偏瘦' },    // BMI < 18.5
            normal: { min: 11.5, max: 16, label: '正常' },         // BMI 18.5-24.9
            overweight: { min: 7, max: 11.5, label: '超重' },      // BMI 25-29.9
            obese: { min: 5, max: 9, label: '肥胖' }               // BMI ≥ 30
        };
        
        // 双胎推荐增重范围
        this.twinWeightGainRanges = {
            underweight: { min: 22.7, max: 28.1, label: '偏瘦' },
            normal: { min: 16.8, max: 24.5, label: '正常' },
            overweight: { min: 14.1, max: 22.7, label: '超重' },
            obese: { min: 11.3, max: 19.1, label: '肥胖' }
        };
    }

    /**
     * 验证输入数据
     */
    validateInput(data) {
        this.clearErrors();

        // 验证身高
        if (!data.height || data.height <= 0) {
            this.addError('请输入有效的身高');
            return false;
        }
        
        const height = parseFloat(data.height);
        if (height < 140 || height > 200) {
            this.addError('身高应在140-200cm之间');
            return false;
        }

        // 验证孕前体重
        if (!data.prePregnancyWeight || data.prePregnancyWeight <= 0) {
            this.addError('请输入有效的孕前体重');
            return false;
        }
        
        const preWeight = parseFloat(data.prePregnancyWeight);
        if (preWeight < 35 || preWeight > 150) {
            this.addError('孕前体重应在35-150kg之间');
            return false;
        }

        // 验证当前体重
        if (!data.currentWeight || data.currentWeight <= 0) {
            this.addError('请输入有效的当前体重');
            return false;
        }
        
        const currentWeight = parseFloat(data.currentWeight);
        if (currentWeight < 35 || currentWeight > 200) {
            this.addError('当前体重应在35-200kg之间');
            return false;
        }

        // 验证孕周
        if (!data.gestationalWeeks) {
            this.addError('请输入当前孕周');
            return false;
        }
        
        const weeks = parseInt(data.gestationalWeeks);
        if (weeks < 0 || weeks > 42) {
            this.addError('孕周应在0-42周之间');
            return false;
        }

        // 验证胎儿数量
        const pregnancyType = data.pregnancyType || 'single';
        if (!['single', 'twin'].includes(pregnancyType)) {
            this.addError('请选择正确的妊娠类型');
            return false;
        }

        return true;
    }

    /**
     * 计算BMI
     */
    calculateBMI(weight, height) {
        const heightInMeters = height / 100;
        return weight / (heightInMeters * heightInMeters);
    }

    /**
     * 根据BMI获取分类
     */
    getBMICategory(bmi) {
        if (bmi < 18.5) return 'underweight';
        if (bmi < 25) return 'normal';
        if (bmi < 30) return 'overweight';
        return 'obese';
    }

    /**
     * 执行孕期体重计算
     */
    calculate(data) {
        if (!this.validateInput(data)) {
            return null;
        }

        const height = parseFloat(data.height);
        const preWeight = parseFloat(data.prePregnancyWeight);
        const currentWeight = parseFloat(data.currentWeight);
        const weeks = parseInt(data.gestationalWeeks);
        const pregnancyType = data.pregnancyType || 'single';

        // 计算孕前BMI
        const preBMI = this.calculateBMI(preWeight, height);
        const bmiCategory = this.getBMICategory(preBMI);

        // 获取推荐增重范围
        const weightRanges = pregnancyType === 'twin' ? this.twinWeightGainRanges : this.weightGainRanges;
        const recommendedRange = weightRanges[bmiCategory];

        // 计算当前增重
        const currentGain = currentWeight - preWeight;

        // 计算理想增重范围（基于当前孕周）
        const idealGainRange = this.calculateIdealWeightGain(weeks, recommendedRange, pregnancyType);

        // 评估当前体重状态
        const weightStatus = this.evaluateWeightStatus(currentGain, idealGainRange);

        // 计算剩余孕期建议增重
        const remainingWeeks = 40 - weeks;
        const remainingGainRange = this.calculateRemainingWeightGain(
            currentGain, recommendedRange, weeks, pregnancyType
        );

        return {
            prePregnancy: {
                weight: preWeight,
                height: height,
                bmi: preBMI,
                bmiCategory: bmiCategory,
                bmiLabel: recommendedRange.label
            },
            current: {
                weight: currentWeight,
                weeks: weeks,
                weightGain: currentGain,
                status: weightStatus
            },
            recommendations: {
                totalRange: recommendedRange,
                idealCurrentRange: idealGainRange,
                remainingRange: remainingGainRange,
                pregnancyType: pregnancyType
            },
            analysis: this.generateWeightAnalysis(currentGain, idealGainRange, weightStatus, weeks),
            tips: this.getWeightManagementTips(weightStatus, bmiCategory, weeks)
        };
    }

    /**
     * 计算理想的当前增重范围
     */
    calculateIdealWeightGain(weeks, recommendedRange, pregnancyType) {
        if (weeks <= 12) {
            // 孕早期增重较少
            return { min: 0.5, max: 2 };
        } else if (weeks <= 28) {
            // 孕中期
            const progress = (weeks - 12) / 16; // 13-28周的进度
            const earlyGain = 2; // 孕早期增重
            const midGain = (recommendedRange.max - recommendedRange.min) * 0.6; // 孕中期占总增重的60%
            
            return {
                min: earlyGain + midGain * progress * 0.7,
                max: earlyGain + midGain * progress * 1.3
            };
        } else {
            // 孕晚期
            const progress = weeks / 40;
            return {
                min: recommendedRange.min * progress * 0.8,
                max: recommendedRange.max * progress * 1.1
            };
        }
    }

    /**
     * 评估当前体重状态
     */
    evaluateWeightStatus(currentGain, idealRange) {
        if (currentGain < idealRange.min) {
            return { status: 'low', label: '增重不足', color: 'warning' };
        } else if (currentGain > idealRange.max) {
            return { status: 'high', label: '增重过多', color: 'danger' };
        } else {
            return { status: 'normal', label: '增重正常', color: 'success' };
        }
    }

    /**
     * 计算剩余孕期建议增重
     */
    calculateRemainingWeightGain(currentGain, recommendedRange, weeks, pregnancyType) {
        const remainingWeeks = Math.max(0, 40 - weeks);
        
        if (remainingWeeks === 0) {
            return { min: 0, max: 0 };
        }

        const targetMin = recommendedRange.min - currentGain;
        const targetMax = recommendedRange.max - currentGain;

        return {
            min: Math.max(0, targetMin),
            max: Math.max(0, targetMax),
            weeklyMin: Math.max(0, targetMin / remainingWeeks),
            weeklyMax: Math.max(0, targetMax / remainingWeeks)
        };
    }

    /**
     * 生成体重分析
     */
    generateWeightAnalysis(currentGain, idealRange, weightStatus, weeks) {
        const analysis = [];

        if (weightStatus.status === 'normal') {
            analysis.push('您的体重增长在正常范围内，请继续保持良好的饮食和运动习惯。');
        } else if (weightStatus.status === 'low') {
            const deficit = idealRange.min - currentGain;
            analysis.push(`您的体重增长偏少，建议增加${deficit.toFixed(1)}kg左右。`);
            analysis.push('可以适当增加营养摄入，但要确保营养均衡。');
        } else {
            const excess = currentGain - idealRange.max;
            analysis.push(`您的体重增长偏多，已超出建议范围${excess.toFixed(1)}kg。`);
            analysis.push('建议控制饮食，增加适量运动，但不要过度节食。');
        }

        if (weeks < 20) {
            analysis.push('您还在孕期前半段，体重增长相对较慢是正常的。');
        } else if (weeks > 32) {
            analysis.push('您已进入孕晚期，体重增长速度可能会放缓。');
        }

        return analysis;
    }

    /**
     * 获取体重管理建议
     */
    getWeightManagementTips(weightStatus, bmiCategory, weeks) {
        const tips = [];

        // 通用建议
        tips.push('保持规律的产检，监测体重变化');
        tips.push('均衡饮食，多吃蔬菜水果，适量摄入蛋白质');
        tips.push('适量运动，如散步、孕妇瑜伽等');

        // 根据体重状态给出建议
        if (weightStatus.status === 'low') {
            tips.push('增加健康食物的摄入量，如坚果、牛奶、瘦肉');
            tips.push('少食多餐，避免空腹时间过长');
        } else if (weightStatus.status === 'high') {
            tips.push('控制高热量食物的摄入，避免甜食和油炸食品');
            tips.push('增加膳食纤维摄入，增强饱腹感');
            tips.push('适当增加运动量，但要避免剧烈运动');
        }

        // 根据BMI分类给出建议
        if (bmiCategory === 'underweight') {
            tips.push('孕前体重偏轻，需要更多营养支持胎儿发育');
        } else if (bmiCategory === 'obese') {
            tips.push('孕前体重偏重，要特别注意控制体重增长');
            tips.push('定期监测血糖和血压');
        }

        // 根据孕周给出建议
        if (weeks < 12) {
            tips.push('孕早期可能有孕吐，注意补充水分和电解质');
        } else if (weeks > 28) {
            tips.push('孕晚期要注意预防妊娠糖尿病和高血压');
        }

        return tips;
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        if (!result) return;

        const html = `
            <div class="result-card">
                <h4><i class="fas fa-weight text-primary me-2"></i>孕期体重管理报告</h4>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6>基本信息</h6>
                            <div class="info-item">
                                <label>孕前BMI：</label>
                                <span>${result.prePregnancy.bmi.toFixed(1)} (${result.prePregnancy.bmiLabel})</span>
                            </div>
                            <div class="info-item">
                                <label>当前孕周：</label>
                                <span>${result.current.weeks}周</span>
                            </div>
                            <div class="info-item">
                                <label>已增重：</label>
                                <span class="text-${result.current.status.color}">${result.current.weightGain.toFixed(1)}kg</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="info-section">
                            <h6>增重建议</h6>
                            <div class="info-item">
                                <label>总增重范围：</label>
                                <span>${result.recommendations.totalRange.min}-${result.recommendations.totalRange.max}kg</span>
                            </div>
                            <div class="info-item">
                                <label>当前理想范围：</label>
                                <span>${result.recommendations.idealCurrentRange.min.toFixed(1)}-${result.recommendations.idealCurrentRange.max.toFixed(1)}kg</span>
                            </div>
                            <div class="info-item">
                                <label>体重状态：</label>
                                <span class="badge bg-${result.current.status.color}">${result.current.status.label}</span>
                            </div>
                        </div>
                    </div>
                </div>

                ${result.recommendations.remainingRange.max > 0 ? `
                <div class="alert alert-info mt-3">
                    <i class="fas fa-info-circle me-2"></i>
                    剩余孕期建议增重：${result.recommendations.remainingRange.min.toFixed(1)}-${result.recommendations.remainingRange.max.toFixed(1)}kg
                    （每周约${result.recommendations.remainingRange.weeklyMin.toFixed(1)}-${result.recommendations.remainingRange.weeklyMax.toFixed(1)}kg）
                </div>
                ` : ''}

                <div class="analysis-section mt-3">
                    <h6><i class="fas fa-chart-line me-2"></i>分析报告：</h6>
                    <ul class="analysis-list">
                        ${result.analysis.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>

                <div class="tips-section mt-3">
                    <h6><i class="fas fa-lightbulb me-2"></i>管理建议：</h6>
                    <ul class="tips-list">
                        ${result.tips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        if (this.resultContainer) {
            this.resultContainer.innerHTML = html;
            this.resultContainer.style.display = 'block';
            this.resultContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// 注册到全局
window.PregnancyWeightCalculator = PregnancyWeightCalculator;
