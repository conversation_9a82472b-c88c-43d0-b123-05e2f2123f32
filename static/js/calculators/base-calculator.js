/**
 * 基础计算器类 - 所有计算器工具的基类
 * 提供统一的接口和通用功能
 */
class BaseCalculator {
    constructor(name) {
        this.name = name;
        this.errors = [];
        this.warnings = [];
        this.form = null;
        this.resultContainer = null;
        this.isInitialized = false;
        this.config = {
            autoValidate: true,
            showProgress: false,
            enableCache: false,
            debounceTime: 300
        };
        this.cache = new Map();
        this.eventListeners = [];
    }

    /**
     * 初始化计算器
     * @param {string} formSelector - 表单选择器
     * @param {string} resultSelector - 结果容器选择器
     * @param {Object} config - 配置选项
     */
    init(formSelector, resultSelector, config = {}) {
        if (this.isInitialized) {
            console.warn(`${this.name} 已经初始化过了`);
            return;
        }

        // 合并配置
        this.config = { ...this.config, ...config };

        this.form = document.querySelector(formSelector);
        this.resultContainer = document.querySelector(resultSelector);

        if (!this.form) {
            console.error(`表单元素未找到: ${formSelector}`);
            return;
        }

        if (!this.resultContainer) {
            console.error(`结果容器未找到: ${resultSelector}`);
            return;
        }

        this.bindEvents();
        this.setupValidation();
        this.setupProgressIndicator();
        this.isInitialized = true;

        console.log(`${this.name} 初始化完成`);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 表单提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSubmit();
        });

        // 输入验证事件
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('input', () => this.validateForm());
            input.addEventListener('change', () => this.validateForm());
        });
    }

    /**
     * 设置表单验证
     */
    setupValidation() {
        this.validateForm();
    }

    /**
     * 处理表单提交
     */
    handleSubmit() {
        this.clearMessages();
        
        const formData = this.getFormData();
        
        if (this.validateInput(formData)) {
            try {
                const result = this.calculate(formData);
                this.displayResult(result);
                this.showResultContainer();
            } catch (error) {
                this.addError(`计算过程中发生错误: ${error.message}`);
                this.displayErrors();
            }
        } else {
            this.displayErrors();
        }
    }

    /**
     * 获取表单数据
     * @returns {Object} 表单数据对象
     */
    getFormData() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            // 处理数字类型
            if (this.isNumericField(key)) {
                data[key] = parseFloat(value) || 0;
            } else {
                data[key] = value;
            }
        }
        
        return data;
    }

    /**
     * 判断字段是否为数字类型
     * @param {string} fieldName - 字段名
     * @returns {boolean}
     */
    isNumericField(fieldName) {
        const numericFields = ['weight', 'height', 'headCircumference', 'weeks', 'days', 'age'];
        return numericFields.some(field => fieldName.includes(field));
    }

    /**
     * 验证表单
     * @returns {boolean} 验证是否通过
     */
    validateForm() {
        const submitButton = this.form.querySelector('button[type="submit"], .calculate-button');
        const formData = this.getFormData();
        const isValid = this.validateInput(formData, false); // 静默验证
        
        if (submitButton) {
            submitButton.disabled = !isValid;
        }
        
        return isValid;
    }

    /**
     * 显示结果容器
     */
    showResultContainer() {
        if (this.resultContainer) {
            this.resultContainer.style.display = 'block';
            this.resultContainer.scrollIntoView({ 
                behavior: 'smooth', 
                block: 'start' 
            });
        }
    }

    /**
     * 添加错误信息
     * @param {string} message - 错误信息
     */
    addError(message) {
        this.errors.push(message);
    }

    /**
     * 添加警告信息
     * @param {string} message - 警告信息
     */
    addWarning(message) {
        this.warnings.push(message);
    }

    /**
     * 清空错误和警告信息
     */
    clearMessages() {
        this.errors = [];
        this.warnings = [];
    }

    /**
     * 显示错误信息
     */
    displayErrors() {
        if (this.errors.length > 0) {
            // 可以在这里实现错误显示逻辑
            console.error('计算器错误:', this.errors);
        }
    }

    /**
     * 解析日期字符串
     * @param {string} dateStr - 日期字符串 (YYYY-MM-DD)
     * @returns {Date|null} 日期对象或null
     */
    parseDate(dateStr) {
        try {
            const date = new Date(dateStr);
            return isNaN(date.getTime()) ? null : date;
        } catch {
            return null;
        }
    }

    /**
     * 计算两个日期之间的天数差
     * @param {Date} startDate - 开始日期
     * @param {Date} endDate - 结束日期
     * @returns {number} 天数差
     */
    calculateDaysDifference(startDate, endDate) {
        const timeDiff = endDate.getTime() - startDate.getTime();
        return Math.floor(timeDiff / (1000 * 3600 * 24));
    }

    /**
     * 计算两个日期之间的月数差（近似）
     * @param {Date} startDate - 开始日期
     * @param {Date} endDate - 结束日期
     * @returns {number} 月数差
     */
    calculateMonthsDifference(startDate, endDate) {
        const days = this.calculateDaysDifference(startDate, endDate);
        return days / 30.44; // 平均每月天数
    }

    /**
     * 格式化日期显示
     * @param {Date} date - 日期对象
     * @returns {string} 格式化后的日期字符串
     */
    formatDate(date) {
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    /**
     * 格式化百分位数显示
     * @param {number} percentile - 百分位数
     * @returns {string} 格式化后的字符串
     */
    formatPercentile(percentile) {
        if (percentile < 3) {
            return `第${percentile.toFixed(1)}百分位 (偏低)`;
        } else if (percentile > 97) {
            return `第${percentile.toFixed(1)}百分位 (偏高)`;
        } else if (percentile >= 25 && percentile <= 75) {
            return `第${percentile.toFixed(1)}百分位 (正常范围)`;
        } else {
            return `第${percentile.toFixed(1)}百分位`;
        }
    }

    // 抽象方法 - 子类必须实现
    
    /**
     * 验证输入参数 - 子类必须实现
     * @param {Object} data - 输入数据
     * @param {boolean} showErrors - 是否显示错误信息
     * @returns {boolean} 验证是否通过
     */
    validateInput(data, showErrors = true) {
        throw new Error('validateInput方法必须在子类中实现');
    }

    /**
     * 执行计算 - 子类必须实现
     * @param {Object} data - 输入数据
     * @returns {Object} 计算结果
     */
    calculate(data) {
        throw new Error('calculate方法必须在子类中实现');
    }

    /**
     * 显示计算结果 - 子类必须实现
     * @param {Object} result - 计算结果
     */
    displayResult(result) {
        throw new Error('displayResult方法必须在子类中实现');
    }
}

// 导出基类
window.BaseCalculator = BaseCalculator;
