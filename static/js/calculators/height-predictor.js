/**
 * 身高预测器
 * 基于BaseCalculator，提供儿童成年身高预测功能
 */
class HeightPredictor extends BaseCalculator {
    constructor() {
        super('height-predictor');
        
        // 不同预测方法的权重
        this.methodWeights = {
            genetic: 0.4,      // 遗传公式
            midParental: 0.3,  // 中亲身高法
            growth: 0.3        // 生长速度法
        };
    }

    /**
     * 验证输入数据
     */
    validateInput(data) {
        this.clearMessages();

        // 验证性别
        if (!data.childGender || !['boy', 'girl'].includes(data.childGender)) {
            this.addError('请选择孩子的性别');
            return false;
        }

        // 验证父亲身高
        if (!data.fatherHeight || data.fatherHeight <= 0) {
            this.addError('请输入父亲身高');
            return false;
        }
        
        const fatherHeight = parseFloat(data.fatherHeight);
        if (fatherHeight < 150 || fatherHeight > 220) {
            this.addError('父亲身高应在150-220cm之间');
            return false;
        }

        // 验证母亲身高
        if (!data.motherHeight || data.motherHeight <= 0) {
            this.addError('请输入母亲身高');
            return false;
        }
        
        const motherHeight = parseFloat(data.motherHeight);
        if (motherHeight < 140 || motherHeight > 200) {
            this.addError('母亲身高应在140-200cm之间');
            return false;
        }

        // 如果提供了孩子当前信息，进行验证
        if (data.childAge && data.childCurrentHeight) {
            const age = parseFloat(data.childAge);
            const currentHeight = parseFloat(data.childCurrentHeight);
            
            if (age < 0 || age > 18) {
                this.addError('孩子年龄应在0-18岁之间');
                return false;
            }
            
            if (currentHeight < 40 || currentHeight > 200) {
                this.addError('孩子当前身高数据不合理');
                return false;
            }
            
            // 检查身高是否与年龄匹配
            const expectedRange = this.getExpectedHeightRange(age, data.childGender);
            if (currentHeight < expectedRange.min || currentHeight > expectedRange.max) {
                this.addWarning(`${age}岁${data.childGender === 'boy' ? '男孩' : '女孩'}的正常身高范围是${expectedRange.min}-${expectedRange.max}cm`);
            }
        }

        return true;
    }

    /**
     * 获取特定年龄的预期身高范围
     */
    getExpectedHeightRange(age, gender) {
        // 简化的身高参考范围（基于WHO标准）
        const ranges = {
            boy: {
                1: { min: 71, max: 81 },
                2: { min: 82, max: 92 },
                3: { min: 89, max: 102 },
                5: { min: 102, max: 117 },
                8: { min: 119, max: 135 },
                10: { min: 128, max: 147 },
                12: { min: 137, max: 160 },
                15: { min: 152, max: 180 },
                18: { min: 162, max: 190 }
            },
            girl: {
                1: { min: 69, max: 79 },
                2: { min: 80, max: 90 },
                3: { min: 87, max: 100 },
                5: { min: 100, max: 115 },
                8: { min: 117, max: 133 },
                10: { min: 126, max: 145 },
                12: { min: 139, max: 163 },
                15: { min: 150, max: 172 },
                18: { min: 152, max: 175 }
            }
        };

        // 找到最接近的年龄
        const ageKeys = Object.keys(ranges[gender]).map(Number).sort((a, b) => a - b);
        let closestAge = ageKeys[0];
        
        for (const ageKey of ageKeys) {
            if (Math.abs(ageKey - age) < Math.abs(closestAge - age)) {
                closestAge = ageKey;
            }
        }

        return ranges[gender][closestAge];
    }

    /**
     * 执行身高预测计算
     */
    calculate(data) {
        if (!this.validateInput(data)) {
            return null;
        }

        const fatherHeight = parseFloat(data.fatherHeight);
        const motherHeight = parseFloat(data.motherHeight);
        const childGender = data.childGender;
        const childAge = data.childAge ? parseFloat(data.childAge) : null;
        const currentHeight = data.childCurrentHeight ? parseFloat(data.childCurrentHeight) : null;

        // 使用多种方法预测身高
        const predictions = {
            genetic: this.calculateGeneticHeight(fatherHeight, motherHeight, childGender),
            midParental: this.calculateMidParentalHeight(fatherHeight, motherHeight, childGender),
            growth: childAge && currentHeight ? this.calculateGrowthBasedHeight(currentHeight, childAge, childGender) : null
        };

        // 计算综合预测
        const finalPrediction = this.calculateFinalPrediction(predictions);

        // 生成身高分析
        const analysis = this.generateHeightAnalysis(predictions, finalPrediction, fatherHeight, motherHeight);

        // 生成成长建议
        const growthTips = this.generateGrowthTips(childAge, currentHeight, finalPrediction);

        return {
            parents: {
                father: fatherHeight,
                mother: motherHeight,
                average: (fatherHeight + motherHeight) / 2
            },
            child: {
                gender: childGender,
                age: childAge,
                currentHeight: currentHeight
            },
            predictions: predictions,
            finalPrediction: finalPrediction,
            analysis: analysis,
            growthTips: growthTips,
            disclaimer: this.getDisclaimer()
        };
    }

    /**
     * 遗传身高公式计算
     */
    calculateGeneticHeight(fatherHeight, motherHeight, gender) {
        let predicted;
        
        if (gender === 'boy') {
            // 男孩：(父亲身高 + 母亲身高 + 13) / 2
            predicted = (fatherHeight + motherHeight + 13) / 2;
        } else {
            // 女孩：(父亲身高 + 母亲身高 - 13) / 2
            predicted = (fatherHeight + motherHeight - 13) / 2;
        }

        return {
            method: '遗传公式法',
            height: predicted,
            range: { min: predicted - 5, max: predicted + 5 },
            confidence: 0.75
        };
    }

    /**
     * 中亲身高法计算
     */
    calculateMidParentalHeight(fatherHeight, motherHeight, gender) {
        const midParental = (fatherHeight + motherHeight) / 2;
        let predicted;

        if (gender === 'boy') {
            predicted = midParental + 6.5;
        } else {
            predicted = midParental - 6.5;
        }

        return {
            method: '中亲身高法',
            height: predicted,
            range: { min: predicted - 7, max: predicted + 7 },
            confidence: 0.70
        };
    }

    /**
     * 基于当前生长速度的预测
     */
    calculateGrowthBasedHeight(currentHeight, age, gender) {
        if (age >= 18) {
            return {
                method: '当前身高',
                height: currentHeight,
                range: { min: currentHeight, max: currentHeight },
                confidence: 1.0
            };
        }

        // 简化的生长速度模型
        const remainingGrowth = this.calculateRemainingGrowth(age, gender);
        const predicted = currentHeight + remainingGrowth;

        return {
            method: '生长速度法',
            height: predicted,
            range: { min: predicted - 8, max: predicted + 8 },
            confidence: age > 12 ? 0.85 : 0.65
        };
    }

    /**
     * 计算剩余生长潜力
     */
    calculateRemainingGrowth(age, gender) {
        // 简化的剩余生长模型
        const growthData = {
            boy: {
                2: 85, 5: 65, 8: 45, 10: 35, 12: 25, 15: 10, 17: 2, 18: 0
            },
            girl: {
                2: 75, 5: 55, 8: 35, 10: 25, 12: 15, 15: 5, 17: 1, 18: 0
            }
        };

        const data = growthData[gender];
        const ages = Object.keys(data).map(Number).sort((a, b) => a - b);
        
        // 找到年龄区间并插值
        for (let i = 0; i < ages.length - 1; i++) {
            if (age >= ages[i] && age <= ages[i + 1]) {
                const ratio = (age - ages[i]) / (ages[i + 1] - ages[i]);
                return data[ages[i]] + (data[ages[i + 1]] - data[ages[i]]) * ratio;
            }
        }

        return age < ages[0] ? data[ages[0]] : 0;
    }

    /**
     * 计算最终预测结果
     */
    calculateFinalPrediction(predictions) {
        let weightedSum = 0;
        let totalWeight = 0;
        let minHeight = Infinity;
        let maxHeight = -Infinity;

        for (const [method, weight] of Object.entries(this.methodWeights)) {
            if (predictions[method]) {
                weightedSum += predictions[method].height * weight;
                totalWeight += weight;
                minHeight = Math.min(minHeight, predictions[method].range.min);
                maxHeight = Math.max(maxHeight, predictions[method].range.max);
            }
        }

        const finalHeight = weightedSum / totalWeight;

        return {
            height: finalHeight,
            range: { 
                min: Math.max(minHeight, finalHeight - 8), 
                max: Math.min(maxHeight, finalHeight + 8) 
            },
            confidence: totalWeight / Object.values(this.methodWeights).reduce((a, b) => a + b, 0)
        };
    }

    /**
     * 生成身高分析
     */
    generateHeightAnalysis(predictions, finalPrediction, fatherHeight, motherHeight) {
        const analysis = [];

        // 遗传因素分析
        const parentAvg = (fatherHeight + motherHeight) / 2;
        if (finalPrediction.height > parentAvg + 5) {
            analysis.push('预测身高高于父母平均身高，遗传优势明显');
        } else if (finalPrediction.height < parentAvg - 5) {
            analysis.push('预测身高略低于父母平均身高，但仍在正常范围');
        } else {
            analysis.push('预测身高接近父母平均身高，符合遗传规律');
        }

        // 预测方法一致性分析
        const heights = Object.values(predictions).filter(p => p).map(p => p.height);
        const variance = this.calculateVariance(heights);
        
        if (variance < 25) {
            analysis.push('各种预测方法结果较为一致，预测可靠性较高');
        } else {
            analysis.push('各种预测方法存在一定差异，结果仅供参考');
        }

        // 身高范围分析
        const range = finalPrediction.range.max - finalPrediction.range.min;
        analysis.push(`预测身高范围约${range.toFixed(1)}cm，属于${range < 10 ? '较窄' : range < 15 ? '中等' : '较宽'}范围`);

        return analysis;
    }

    /**
     * 计算方差
     */
    calculateVariance(values) {
        if (values.length === 0) return 0;
        
        const mean = values.reduce((a, b) => a + b, 0) / values.length;
        const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
        return variance;
    }

    /**
     * 生成成长建议
     */
    generateGrowthTips(age, currentHeight, prediction) {
        const tips = [];

        // 通用建议
        tips.push('保证充足的睡眠，生长激素主要在夜间分泌');
        tips.push('均衡营养，特别是蛋白质、钙质和维生素D的摄入');
        tips.push('适量运动，如跳跃、拉伸等有助于骨骼发育的运动');

        // 年龄特定建议
        if (age && age < 3) {
            tips.push('婴幼儿期是生长最快的时期，确保营养充足');
            tips.push('定期体检，监测生长发育指标');
        } else if (age && age < 12) {
            tips.push('学龄期要注意坐姿，避免脊柱侧弯影响身高');
            tips.push('多参加户外活动，阳光有助于维生素D合成');
        } else if (age && age < 18) {
            tips.push('青春期是第二个生长高峰，把握关键期');
            tips.push('避免过度节食和熬夜，影响生长发育');
        }

        // 基于预测结果的建议
        if (prediction.confidence < 0.7) {
            tips.push('预测结果仅供参考，实际身高受多种因素影响');
        }

        tips.push('保持积极心态，身高不是衡量价值的唯一标准');

        return tips;
    }

    /**
     * 获取免责声明
     */
    getDisclaimer() {
        return [
            '本预测结果仅供参考，不能作为医学诊断依据',
            '实际身高受遗传、营养、运动、疾病等多种因素影响',
            '如有生长发育异常，请及时咨询专业医生',
            '预测误差可能达到±5-10cm，属于正常范围'
        ];
    }

    /**
     * 显示计算结果
     */
    displayResult(result) {
        if (!result) return;

        const html = `
            <div class="result-card">
                <h4><i class="fas fa-ruler-vertical text-info me-2"></i>身高预测报告</h4>
                
                <div class="prediction-summary">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="prediction-main">
                                <h5>预测成年身高</h5>
                                <div class="height-value">${result.finalPrediction.height.toFixed(1)} cm</div>
                                <div class="height-range">
                                    范围：${result.finalPrediction.range.min.toFixed(1)} - ${result.finalPrediction.range.max.toFixed(1)} cm
                                </div>
                                <div class="confidence">
                                    可信度：${(result.finalPrediction.confidence * 100).toFixed(0)}%
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="parent-info">
                                <h6>父母身高信息</h6>
                                <div class="parent-heights">
                                    <div>父亲：${result.parents.father} cm</div>
                                    <div>母亲：${result.parents.mother} cm</div>
                                    <div>平均：${result.parents.average.toFixed(1)} cm</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="methods-section mt-4">
                    <h6><i class="fas fa-calculator me-2"></i>预测方法详情：</h6>
                    <div class="methods-list">
                        ${Object.entries(result.predictions).filter(([key, pred]) => pred).map(([key, pred]) => `
                            <div class="method-item">
                                <div class="method-name">${pred.method}</div>
                                <div class="method-result">${pred.height.toFixed(1)} cm</div>
                                <div class="method-range">(${pred.range.min.toFixed(1)} - ${pred.range.max.toFixed(1)} cm)</div>
                                <div class="method-confidence">可信度：${(pred.confidence * 100).toFixed(0)}%</div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                ${result.child.age && result.child.currentHeight ? `
                <div class="current-info mt-3">
                    <div class="alert alert-info">
                        <i class="fas fa-child me-2"></i>
                        当前信息：${result.child.age}岁，身高${result.child.currentHeight}cm
                    </div>
                </div>
                ` : ''}

                <div class="analysis-section mt-4">
                    <h6><i class="fas fa-chart-line me-2"></i>分析报告：</h6>
                    <ul class="analysis-list">
                        ${result.analysis.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>

                <div class="tips-section mt-4">
                    <h6><i class="fas fa-lightbulb me-2"></i>成长建议：</h6>
                    <ul class="tips-list">
                        ${result.growthTips.map(tip => `<li>${tip}</li>`).join('')}
                    </ul>
                </div>

                <div class="disclaimer-section mt-4">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>重要说明：</h6>
                    <ul class="disclaimer-list">
                        ${result.disclaimer.map(item => `<li>${item}</li>`).join('')}
                    </ul>
                </div>
            </div>
        `;

        if (this.resultContainer) {
            this.resultContainer.innerHTML = html;
            this.resultContainer.style.display = 'block';
            this.resultContainer.scrollIntoView({ behavior: 'smooth' });
        }
    }
}

// 注册到全局
window.HeightPredictor = HeightPredictor;
