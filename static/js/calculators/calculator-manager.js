/**
 * 计算器管理器
 * 统一管理所有计算器的初始化和使用
 */
class CalculatorManager {
    constructor() {
        this.calculators = new Map();
        this.initialized = false;
        this.plugins = new Map();
        this.cache = new Map();
        this.config = {
            enableCache: true,
            cacheTimeout: 5 * 60 * 1000, // 5分钟
            enableAnalytics: false,
            debugMode: false
        };
        this.analytics = {
            usage: new Map(),
            errors: []
        };
    }

    /**
     * 注册计算器
     * @param {string} name - 计算器名称
     * @param {BaseCalculator} calculator - 计算器实例
     * @param {Object} options - 注册选项
     */
    register(name, calculator, options = {}) {
        if (this.calculators.has(name)) {
            console.warn(`计算器 ${name} 已存在，将被覆盖`);
        }

        // 验证计算器实例
        if (!calculator || typeof calculator.init !== 'function') {
            throw new Error(`无效的计算器实例: ${name}`);
        }

        this.calculators.set(name, {
            instance: calculator,
            options: options,
            registeredAt: new Date(),
            usage: 0
        });

        if (this.config.debugMode) {
            console.log(`计算器 ${name} 注册成功`);
        }
    }

    /**
     * 获取计算器实例
     * @param {string} name - 计算器名称
     * @returns {BaseCalculator|null} 计算器实例
     */
    get(name) {
        return this.calculators.get(name) || null;
    }

    /**
     * 初始化所有计算器
     */
    init() {
        if (this.initialized) return;

        // 注册所有可用的计算器
        this.registerAllCalculators();

        // 根据当前页面自动初始化对应的计算器
        this.autoInitialize();

        this.initialized = true;
    }

    /**
     * 注册所有计算器
     */
    registerAllCalculators() {
        // 预产期计算器
        if (window.DueDateCalculator) {
            this.register('dueDate', new DueDateCalculator());
        }

        // 生长发育计算器
        if (window.GrowthCalculator) {
            this.register('growth', new GrowthCalculator());
        }

        // 受孕日期计算器
        if (window.ConceptionCalculator) {
            this.register('conception', new ConceptionCalculator());
        }

        // 孕期体重计算器
        if (window.PregnancyWeightCalculator) {
            this.register('pregnancyWeight', new PregnancyWeightCalculator());
        }

        // 婴儿花费计算器
        if (window.BabyCostCalculator) {
            this.register('babyCost', new BabyCostCalculator());
        }

        // 身高预测器
        if (window.HeightPredictor) {
            this.register('height', new HeightPredictor());
        }

        // 性别预测器
        if (window.GenderPredictor) {
            this.register('gender', new GenderPredictor());
        }
    }

    /**
     * 根据页面自动初始化计算器
     */
    autoInitialize() {
        const path = window.location.pathname;

        // 根据URL路径判断需要初始化哪个计算器
        if (path.includes('due-date-calculator')) {
            this.initCalculator('dueDate', '.calculator-form', '.results-container');
        } else if (path.includes('growth-calculator')) {
            this.initCalculator('growth', '.calculator-form', '.results-container');
        } else if (path.includes('conception-calculator')) {
            this.initCalculator('conception', '.calculator-form', '.results-container');
        } else if (path.includes('pregnancy-weight-calculator')) {
            this.initCalculator('pregnancyWeight', '.calculator-form', '.results-container');
        } else if (path.includes('baby-cost-calculator')) {
            this.initCalculator('babyCost', '.calculator-form', '.results-container');
        } else if (path.includes('height-predictor')) {
            this.initCalculator('height', '.calculator-form', '.results-container');
        } else if (path.includes('gender-predictor')) {
            this.initCalculator('gender', '.calculator-form', '.results-container');
        }

        // 工具首页 - 初始化所有计算器的预览功能
        if (path.includes('/tools') && !path.includes('-calculator') && !path.includes('-predictor')) {
            this.initToolsIndex();
        }
    }

    /**
     * 初始化指定计算器
     * @param {string} name - 计算器名称
     * @param {string} formSelector - 表单选择器
     * @param {string} resultSelector - 结果容器选择器
     */
    initCalculator(name, formSelector, resultSelector) {
        const calculator = this.get(name);
        if (calculator) {
            calculator.init(formSelector, resultSelector);
            console.log(`${name} 计算器已初始化`);
        } else {
            console.warn(`计算器 ${name} 未找到`);
        }
    }

    /**
     * 初始化工具首页
     */
    initToolsIndex() {
        // 为工具卡片添加点击事件
        const toolCards = document.querySelectorAll('.tool-card');
        toolCards.forEach(card => {
            card.addEventListener('click', (e) => {
                const link = card.querySelector('a');
                if (link && !e.target.closest('a')) {
                    window.location.href = link.href;
                }
            });
        });

        // 添加工具搜索功能
        this.initToolsSearch();
    }

    /**
     * 初始化工具搜索功能
     */
    initToolsSearch() {
        const searchInput = document.querySelector('.tools-search');
        if (!searchInput) return;

        searchInput.addEventListener('input', (e) => {
            const query = e.target.value.toLowerCase();
            const toolCards = document.querySelectorAll('.tool-card');

            toolCards.forEach(card => {
                const title = card.querySelector('.tool-title')?.textContent.toLowerCase() || '';
                const description = card.querySelector('.tool-description')?.textContent.toLowerCase() || '';
                
                const matches = title.includes(query) || description.includes(query);
                card.style.display = matches ? 'block' : 'none';
            });
        });
    }

    /**
     * 手动初始化计算器 (用于动态加载的页面)
     * @param {string} name - 计算器名称
     * @param {string} formSelector - 表单选择器
     * @param {string} resultSelector - 结果容器选择器
     */
    manualInit(name, formSelector, resultSelector) {
        this.initCalculator(name, formSelector, resultSelector);
    }

    /**
     * 获取所有已注册的计算器列表
     * @returns {Array} 计算器名称列表
     */
    getAvailableCalculators() {
        return Array.from(this.calculators.keys());
    }

    /**
     * 检查计算器是否可用
     * @param {string} name - 计算器名称
     * @returns {boolean} 是否可用
     */
    isAvailable(name) {
        return this.calculators.has(name);
    }

    /**
     * 销毁所有计算器实例
     */
    destroy() {
        this.calculators.clear();
        this.initialized = false;
    }
}

// 创建全局计算器管理器实例
window.calculatorManager = new CalculatorManager();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.calculatorManager.init();
});

// 导出管理器类
window.CalculatorManager = CalculatorManager;
