/**
 * 应用加载器
 * 统一管理所有JavaScript模块的加载和初始化
 */
class AppLoader {
    constructor() {
        this.loadedModules = new Set();
        this.loadingPromises = new Map();
        this.config = {
            baseUrl: '/static/js/',
            enableCache: true,
            timeout: 10000
        };
    }

    /**
     * 加载必需的核心模块
     */
    async loadCoreModules() {
        const coreModules = [
            'helpers/validation-helper.js',
            'helpers/date-helper.js',
            'helpers/format-helper.js',
            'helpers/chart-helper.js',
            'calculators/base-calculator.js',
            'calculators/calculator-manager.js'
        ];

        try {
            await this.loadModules(coreModules);
            console.log('核心模块加载完成');
        } catch (error) {
            console.error('核心模块加载失败:', error);
            throw error;
        }
    }

    /**
     * 根据页面类型加载对应的计算器模块
     */
    async loadPageModules() {
        const path = window.location.pathname;
        const modules = [];

        // 根据URL路径确定需要加载的模块
        if (path.includes('growth-calculator')) {
            modules.push('calculators/enhanced-growth-calculator.js');
        } else if (path.includes('due-date-calculator')) {
            modules.push('calculators/due-date-calculator.js');
        } else if (path.includes('conception-calculator')) {
            modules.push('calculators/conception-calculator.js');
        } else if (path.includes('pregnancy-weight-calculator')) {
            modules.push('calculators/pregnancy-weight-calculator.js');
        } else if (path.includes('baby-cost-calculator')) {
            modules.push('calculators/baby-cost-calculator.js');
        } else if (path.includes('height-predictor')) {
            modules.push('calculators/height-predictor.js');
        }

        // 如果是工具首页，加载所有计算器
        if (path.includes('/tools') && !path.includes('-calculator') && !path.includes('-predictor')) {
            modules.push(
                'calculators/due-date-calculator.js',
                'calculators/enhanced-growth-calculator.js',
                'calculators/conception-calculator.js',
                'calculators/pregnancy-weight-calculator.js',
                'calculators/baby-cost-calculator.js',
                'calculators/height-predictor.js'
            );
        }

        if (modules.length > 0) {
            try {
                await this.loadModules(modules);
                console.log('页面模块加载完成');
            } catch (error) {
                console.error('页面模块加载失败:', error);
            }
        }
    }

    /**
     * 加载多个模块
     * @param {Array} modules - 模块路径数组
     */
    async loadModules(modules) {
        const promises = modules.map(module => this.loadModule(module));
        await Promise.all(promises);
    }

    /**
     * 加载单个模块
     * @param {string} modulePath - 模块路径
     */
    async loadModule(modulePath) {
        // 如果已经加载过，直接返回
        if (this.loadedModules.has(modulePath)) {
            return;
        }

        // 如果正在加载，返回现有的Promise
        if (this.loadingPromises.has(modulePath)) {
            return this.loadingPromises.get(modulePath);
        }

        const fullPath = this.config.baseUrl + modulePath;
        
        const loadPromise = new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = fullPath;
            script.async = true;
            
            const timeout = setTimeout(() => {
                reject(new Error(`模块加载超时: ${modulePath}`));
            }, this.config.timeout);

            script.onload = () => {
                clearTimeout(timeout);
                this.loadedModules.add(modulePath);
                this.loadingPromises.delete(modulePath);
                console.log(`模块加载成功: ${modulePath}`);
                resolve();
            };

            script.onerror = () => {
                clearTimeout(timeout);
                this.loadingPromises.delete(modulePath);
                const error = new Error(`模块加载失败: ${modulePath}`);
                console.error(error);
                reject(error);
            };

            document.head.appendChild(script);
        });

        this.loadingPromises.set(modulePath, loadPromise);
        return loadPromise;
    }

    /**
     * 检查模块是否已加载
     * @param {string} modulePath - 模块路径
     */
    isModuleLoaded(modulePath) {
        return this.loadedModules.has(modulePath);
    }

    /**
     * 动态加载模块（用于按需加载）
     * @param {string} modulePath - 模块路径
     */
    async loadModuleOnDemand(modulePath) {
        if (!this.isModuleLoaded(modulePath)) {
            await this.loadModule(modulePath);
        }
    }

    /**
     * 预加载模块（用于性能优化）
     * @param {Array} modules - 模块路径数组
     */
    preloadModules(modules) {
        modules.forEach(module => {
            if (!this.isModuleLoaded(module)) {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'script';
                link.href = this.config.baseUrl + module;
                document.head.appendChild(link);
            }
        });
    }

    /**
     * 获取加载统计信息
     */
    getLoadStats() {
        return {
            loadedCount: this.loadedModules.size,
            loadingCount: this.loadingPromises.size,
            loadedModules: Array.from(this.loadedModules)
        };
    }

    /**
     * 清理加载器状态
     */
    cleanup() {
        this.loadedModules.clear();
        this.loadingPromises.clear();
    }
}

/**
 * 应用初始化器
 */
class AppInitializer {
    constructor() {
        this.loader = new AppLoader();
        this.initialized = false;
    }

    /**
     * 初始化应用
     */
    async init() {
        if (this.initialized) {
            console.warn('应用已经初始化过了');
            return;
        }

        try {
            console.log('开始初始化应用...');
            
            // 1. 加载核心模块
            await this.loader.loadCoreModules();
            
            // 2. 加载页面特定模块
            await this.loader.loadPageModules();
            
            // 3. 初始化计算器管理器
            if (window.calculatorManager) {
                window.calculatorManager.init();
            }
            
            // 4. 初始化其他全局功能
            this.initGlobalFeatures();
            
            this.initialized = true;
            console.log('应用初始化完成');
            
            // 触发自定义事件
            document.dispatchEvent(new CustomEvent('appInitialized', {
                detail: { loader: this.loader }
            }));
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            
            // 触发错误事件
            document.dispatchEvent(new CustomEvent('appInitError', {
                detail: { error }
            }));
        }
    }

    /**
     * 初始化全局功能
     */
    initGlobalFeatures() {
        // 初始化工具提示
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // 初始化弹出框
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }

        // 初始化其他全局功能
        this.initSmoothScroll();
        this.initBackToTop();
        this.initLazyLoading();
    }

    /**
     * 初始化平滑滚动
     */
    initSmoothScroll() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                if (targetId === '#') return;
                
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
    }

    /**
     * 初始化回到顶部按钮
     */
    initBackToTop() {
        const backToTopButton = document.getElementById('back-to-top');
        if (backToTopButton) {
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.add('show');
                } else {
                    backToTopButton.classList.remove('show');
                }
            });

            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }
    }

    /**
     * 初始化图片延迟加载
     */
    initLazyLoading() {
        const lazyImages = document.querySelectorAll('img.lazy');
        if ('IntersectionObserver' in window && lazyImages.length > 0) {
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const image = entry.target;
                        image.src = image.dataset.src;
                        if (image.dataset.srcset) {
                            image.srcset = image.dataset.srcset;
                        }
                        image.classList.remove('lazy');
                        imageObserver.unobserve(image);
                    }
                });
            });

            lazyImages.forEach(function(image) {
                imageObserver.observe(image);
            });
        }
    }
}

// 创建全局实例
window.appLoader = new AppLoader();
window.appInitializer = new AppInitializer();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    window.appInitializer.init();
});

// 导出类
window.AppLoader = AppLoader;
window.AppInitializer = AppInitializer;
