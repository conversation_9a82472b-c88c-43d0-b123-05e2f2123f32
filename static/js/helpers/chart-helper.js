/**
 * 图表助手类
 * 提供统一的图表创建和管理功能
 */
class ChartHelper {
    constructor() {
        this.charts = new Map();
        this.defaultColors = {
            primary: '#007bff',
            secondary: '#6c757d',
            success: '#28a745',
            danger: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8',
            light: '#f8f9fa',
            dark: '#343a40'
        };
    }

    /**
     * 创建生长曲线图
     * @param {string} canvasId - 画布ID
     * @param {Object} data - 图表数据
     * @param {Object} options - 图表选项
     * @returns {Chart} Chart.js实例
     */
    createGrowthChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element not found: ${canvasId}`);
            return null;
        }

        // 销毁已存在的图表
        this.destroyChart(canvasId);

        const defaultOptions = {
            type: 'line',
            data: {
                labels: ['3rd', '15th', '50th', '85th', '97th'],
                datasets: [
                    {
                        label: '标准曲线',
                        data: data.standards,
                        borderColor: this.defaultColors.info,
                        backgroundColor: this.defaultColors.info + '20',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 4,
                        pointHoverRadius: 6
                    },
                    {
                        label: '您的宝宝',
                        data: Array(5).fill(data.value),
                        borderColor: this.defaultColors.danger,
                        backgroundColor: this.defaultColors.danger,
                        borderWidth: 3,
                        pointRadius: 8,
                        pointHoverRadius: 10,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: data.label || '数值',
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '百分位',
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    title: {
                        display: true,
                        text: options.title || `${data.label} - ${data.age}个月`,
                        font: {
                            size: 16,
                            weight: 'bold'
                        },
                        padding: {
                            top: 10,
                            bottom: 20
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.dataset.label || '';
                                const value = context.parsed.y;
                                const unit = data.unit || '';
                                return `${label}: ${value}${unit}`;
                            }
                        }
                    }
                },
                ...options.chartOptions
            }
        };

        const chart = new Chart(canvas, defaultOptions);
        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * 创建孕期进度图
     * @param {string} canvasId - 画布ID
     * @param {Object} data - 图表数据
     * @param {Object} options - 图表选项
     * @returns {Chart} Chart.js实例
     */
    createPregnancyProgressChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element not found: ${canvasId}`);
            return null;
        }

        this.destroyChart(canvasId);

        const progressPercentage = (data.currentWeeks / 40) * 100;

        const defaultOptions = {
            type: 'doughnut',
            data: {
                labels: ['已完成', '剩余'],
                datasets: [{
                    data: [progressPercentage, 100 - progressPercentage],
                    backgroundColor: [
                        this.defaultColors.success,
                        this.defaultColors.light
                    ],
                    borderWidth: 0,
                    cutout: '70%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: options.title || '孕期进度',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                ...options.chartOptions
            },
            plugins: [{
                id: 'centerText',
                beforeDraw: function(chart) {
                    const ctx = chart.ctx;
                    const centerX = chart.chartArea.left + (chart.chartArea.right - chart.chartArea.left) / 2;
                    const centerY = chart.chartArea.top + (chart.chartArea.bottom - chart.chartArea.top) / 2;

                    ctx.save();
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.font = 'bold 24px Arial';
                    ctx.fillStyle = '#333';
                    ctx.fillText(`${data.currentWeeks}周`, centerX, centerY - 10);
                    
                    ctx.font = '14px Arial';
                    ctx.fillStyle = '#666';
                    ctx.fillText(`${progressPercentage.toFixed(1)}%`, centerX, centerY + 15);
                    ctx.restore();
                }
            }]
        };

        const chart = new Chart(canvas, defaultOptions);
        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * 创建体重增长趋势图
     * @param {string} canvasId - 画布ID
     * @param {Object} data - 图表数据
     * @param {Object} options - 图表选项
     * @returns {Chart} Chart.js实例
     */
    createWeightTrendChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element not found: ${canvasId}`);
            return null;
        }

        this.destroyChart(canvasId);

        const defaultOptions = {
            type: 'line',
            data: {
                labels: data.labels,
                datasets: [
                    {
                        label: '推荐范围上限',
                        data: data.upperLimit,
                        borderColor: this.defaultColors.warning,
                        backgroundColor: this.defaultColors.warning + '20',
                        fill: '+1',
                        tension: 0.4,
                        pointRadius: 0
                    },
                    {
                        label: '推荐范围下限',
                        data: data.lowerLimit,
                        borderColor: this.defaultColors.warning,
                        backgroundColor: this.defaultColors.warning + '20',
                        fill: false,
                        tension: 0.4,
                        pointRadius: 0
                    },
                    {
                        label: '实际体重',
                        data: data.actualWeight,
                        borderColor: this.defaultColors.primary,
                        backgroundColor: this.defaultColors.primary,
                        borderWidth: 3,
                        pointRadius: 6,
                        pointHoverRadius: 8,
                        fill: false
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                scales: {
                    y: {
                        title: {
                            display: true,
                            text: '体重 (千克)',
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        },
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '孕周',
                            font: {
                                weight: 'bold',
                                size: 14
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    title: {
                        display: true,
                        text: options.title || '孕期体重增长趋势',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    }
                },
                ...options.chartOptions
            }
        };

        const chart = new Chart(canvas, defaultOptions);
        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * 创建费用分布饼图
     * @param {string} canvasId - 画布ID
     * @param {Object} data - 图表数据
     * @param {Object} options - 图表选项
     * @returns {Chart} Chart.js实例
     */
    createCostPieChart(canvasId, data, options = {}) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) {
            console.error(`Canvas element not found: ${canvasId}`);
            return null;
        }

        this.destroyChart(canvasId);

        const colors = [
            this.defaultColors.primary,
            this.defaultColors.success,
            this.defaultColors.warning,
            this.defaultColors.info,
            this.defaultColors.danger,
            this.defaultColors.secondary
        ];

        const defaultOptions = {
            type: 'pie',
            data: {
                labels: data.labels,
                datasets: [{
                    data: data.values,
                    backgroundColor: colors.slice(0, data.labels.length),
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'right',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    title: {
                        display: true,
                        text: options.title || '费用分布',
                        font: {
                            size: 16,
                            weight: 'bold'
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ¥${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                },
                ...options.chartOptions
            }
        };

        const chart = new Chart(canvas, defaultOptions);
        this.charts.set(canvasId, chart);
        return chart;
    }

    /**
     * 销毁指定图表
     * @param {string} canvasId - 画布ID
     */
    destroyChart(canvasId) {
        const existingChart = this.charts.get(canvasId);
        if (existingChart) {
            existingChart.destroy();
            this.charts.delete(canvasId);
        }
    }

    /**
     * 销毁所有图表
     */
    destroyAllCharts() {
        this.charts.forEach((chart, canvasId) => {
            chart.destroy();
        });
        this.charts.clear();
    }

    /**
     * 获取图表实例
     * @param {string} canvasId - 画布ID
     * @returns {Chart|null} Chart.js实例
     */
    getChart(canvasId) {
        return this.charts.get(canvasId) || null;
    }

    /**
     * 更新图表数据
     * @param {string} canvasId - 画布ID
     * @param {Object} newData - 新数据
     */
    updateChart(canvasId, newData) {
        const chart = this.charts.get(canvasId);
        if (chart) {
            chart.data = { ...chart.data, ...newData };
            chart.update();
        }
    }

    /**
     * 设置图表主题色彩
     * @param {Object} colors - 色彩配置
     */
    setThemeColors(colors) {
        this.defaultColors = { ...this.defaultColors, ...colors };
    }
}

// 创建全局实例
window.chartHelper = new ChartHelper();

// 导出类
window.ChartHelper = ChartHelper;
