/**
 * 验证助手类
 * 提供统一的数据验证功能
 */
class ValidationHelper {
    /**
     * 验证日期格式和有效性
     * @param {string} dateStr - 日期字符串
     * @param {Object} options - 验证选项
     * @returns {Object} 验证结果
     */
    static validateDate(dateStr, options = {}) {
        const {
            allowFuture = false,
            allowPast = true,
            minDate = null,
            maxDate = null,
            format = 'YYYY-MM-DD'
        } = options;

        const result = {
            isValid: false,
            error: null,
            date: null
        };

        // 检查日期格式
        if (!dateStr || typeof dateStr !== 'string') {
            result.error = '请输入有效的日期';
            return result;
        }

        // 解析日期
        const date = new Date(dateStr);
        if (isNaN(date.getTime())) {
            result.error = '日期格式不正确';
            return result;
        }

        const today = new Date();
        today.setHours(0, 0, 0, 0);
        date.setHours(0, 0, 0, 0);

        // 检查未来日期
        if (!allowFuture && date > today) {
            result.error = '不能选择未来日期';
            return result;
        }

        // 检查过去日期
        if (!allowPast && date < today) {
            result.error = '不能选择过去日期';
            return result;
        }

        // 检查最小日期
        if (minDate && date < new Date(minDate)) {
            result.error = `日期不能早于 ${minDate}`;
            return result;
        }

        // 检查最大日期
        if (maxDate && date > new Date(maxDate)) {
            result.error = `日期不能晚于 ${maxDate}`;
            return result;
        }

        result.isValid = true;
        result.date = date;
        return result;
    }

    /**
     * 验证数字范围
     * @param {number|string} value - 数值
     * @param {Object} options - 验证选项
     * @returns {Object} 验证结果
     */
    static validateNumber(value, options = {}) {
        const {
            min = null,
            max = null,
            allowDecimals = true,
            required = true
        } = options;

        const result = {
            isValid: false,
            error: null,
            value: null
        };

        // 检查必填
        if (required && (value === null || value === undefined || value === '')) {
            result.error = '此字段为必填项';
            return result;
        }

        // 如果不是必填且为空，则通过验证
        if (!required && (value === null || value === undefined || value === '')) {
            result.isValid = true;
            return result;
        }

        // 转换为数字
        const numValue = Number(value);
        if (isNaN(numValue)) {
            result.error = '请输入有效的数字';
            return result;
        }

        // 检查小数
        if (!allowDecimals && numValue % 1 !== 0) {
            result.error = '请输入整数';
            return result;
        }

        // 检查最小值
        if (min !== null && numValue < min) {
            result.error = `数值不能小于 ${min}`;
            return result;
        }

        // 检查最大值
        if (max !== null && numValue > max) {
            result.error = `数值不能大于 ${max}`;
            return result;
        }

        result.isValid = true;
        result.value = numValue;
        return result;
    }

    /**
     * 验证邮箱格式
     * @param {string} email - 邮箱地址
     * @returns {Object} 验证结果
     */
    static validateEmail(email) {
        const result = {
            isValid: false,
            error: null
        };

        if (!email || typeof email !== 'string') {
            result.error = '请输入邮箱地址';
            return result;
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            result.error = '邮箱格式不正确';
            return result;
        }

        result.isValid = true;
        return result;
    }

    /**
     * 验证手机号格式
     * @param {string} phone - 手机号
     * @returns {Object} 验证结果
     */
    static validatePhone(phone) {
        const result = {
            isValid: false,
            error: null
        };

        if (!phone || typeof phone !== 'string') {
            result.error = '请输入手机号';
            return result;
        }

        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(phone)) {
            result.error = '手机号格式不正确';
            return result;
        }

        result.isValid = true;
        return result;
    }

    /**
     * 批量验证
     * @param {Object} data - 要验证的数据
     * @param {Object} rules - 验证规则
     * @returns {Object} 验证结果
     */
    static validateBatch(data, rules) {
        const result = {
            isValid: true,
            errors: {},
            validData: {}
        };

        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field];
            let fieldResult;

            switch (rule.type) {
                case 'date':
                    fieldResult = this.validateDate(value, rule.options || {});
                    break;
                case 'number':
                    fieldResult = this.validateNumber(value, rule.options || {});
                    break;
                case 'email':
                    fieldResult = this.validateEmail(value);
                    break;
                case 'phone':
                    fieldResult = this.validatePhone(value);
                    break;
                default:
                    fieldResult = { isValid: true, value: value };
            }

            if (!fieldResult.isValid) {
                result.isValid = false;
                result.errors[field] = fieldResult.error;
            } else {
                result.validData[field] = fieldResult.value !== undefined ? fieldResult.value : value;
            }
        }

        return result;
    }

    /**
     * 显示验证错误
     * @param {HTMLElement} element - 表单元素
     * @param {string} message - 错误信息
     */
    static showError(element, message) {
        // 移除之前的错误样式
        element.classList.remove('is-valid');
        element.classList.add('is-invalid');

        // 查找或创建错误信息元素
        let errorElement = element.parentNode.querySelector('.invalid-feedback');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'invalid-feedback';
            element.parentNode.appendChild(errorElement);
        }

        errorElement.textContent = message;
    }

    /**
     * 显示验证成功
     * @param {HTMLElement} element - 表单元素
     */
    static showSuccess(element) {
        element.classList.remove('is-invalid');
        element.classList.add('is-valid');

        // 隐藏错误信息
        const errorElement = element.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    /**
     * 清除验证状态
     * @param {HTMLElement} element - 表单元素
     */
    static clearValidation(element) {
        element.classList.remove('is-valid', 'is-invalid');
        
        const errorElement = element.parentNode.querySelector('.invalid-feedback');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }
}

// 导出类
window.ValidationHelper = ValidationHelper;
