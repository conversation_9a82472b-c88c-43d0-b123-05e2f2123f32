/**
 * 格式化助手类
 * 提供统一的数据格式化功能
 */
class FormatHelper {
    /**
     * 格式化百分位数显示
     * @param {number} percentile - 百分位数
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatPercentile(percentile, options = {}) {
        const {
            showRange = true,
            precision = 1,
            lowThreshold = 10,
            highThreshold = 90
        } = options;

        if (percentile === null || percentile === undefined) {
            return '无法计算';
        }

        const formattedValue = percentile.toFixed(precision);
        let result = `第${formattedValue}百分位`;

        if (showRange) {
            if (percentile < lowThreshold) {
                result += ' (偏低)';
            } else if (percentile > highThreshold) {
                result += ' (偏高)';
            } else if (percentile >= 25 && percentile <= 75) {
                result += ' (正常范围)';
            }
        }

        return result;
    }

    /**
     * 格式化数字显示
     * @param {number} value - 数值
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatNumber(value, options = {}) {
        const {
            precision = 2,
            unit = '',
            thousandsSeparator = true,
            prefix = '',
            suffix = ''
        } = options;

        if (value === null || value === undefined || isNaN(value)) {
            return '无数据';
        }

        let formattedValue = Number(value).toFixed(precision);
        
        if (thousandsSeparator) {
            formattedValue = formattedValue.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        return `${prefix}${formattedValue}${unit}${suffix}`;
    }

    /**
     * 格式化体重显示
     * @param {number} weight - 体重（千克）
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatWeight(weight, options = {}) {
        const { showUnit = true, precision = 1 } = options;
        
        if (weight === null || weight === undefined || isNaN(weight)) {
            return '无数据';
        }

        const formattedWeight = Number(weight).toFixed(precision);
        return showUnit ? `${formattedWeight} 千克` : formattedWeight;
    }

    /**
     * 格式化身高显示
     * @param {number} height - 身高（厘米）
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatHeight(height, options = {}) {
        const { showUnit = true, precision = 1 } = options;
        
        if (height === null || height === undefined || isNaN(height)) {
            return '无数据';
        }

        const formattedHeight = Number(height).toFixed(precision);
        return showUnit ? `${formattedHeight} 厘米` : formattedHeight;
    }

    /**
     * 格式化年龄显示
     * @param {number} ageInMonths - 年龄（月数）
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatAge(ageInMonths, options = {}) {
        const { showDays = false } = options;
        
        if (ageInMonths === null || ageInMonths === undefined || isNaN(ageInMonths)) {
            return '无数据';
        }

        const totalDays = Math.floor(ageInMonths * 30.44);
        const years = Math.floor(ageInMonths / 12);
        const months = Math.floor(ageInMonths % 12);
        const days = showDays ? Math.floor(totalDays % 30.44) : 0;

        let result = '';
        
        if (years > 0) {
            result += `${years}岁`;
        }
        
        if (months > 0) {
            result += `${months}个月`;
        }
        
        if (showDays && days > 0) {
            result += `${days}天`;
        }

        return result || '0个月';
    }

    /**
     * 格式化孕周显示
     * @param {number} weeks - 孕周数
     * @param {number} days - 额外天数
     * @returns {string} 格式化后的字符串
     */
    static formatPregnancyWeeks(weeks, days = 0) {
        if (weeks === null || weeks === undefined || isNaN(weeks)) {
            return '无数据';
        }

        let result = `${Math.floor(weeks)}周`;
        
        if (days > 0) {
            result += `${days}天`;
        }

        return result;
    }

    /**
     * 格式化货币显示
     * @param {number} amount - 金额
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatCurrency(amount, options = {}) {
        const {
            currency = '¥',
            precision = 2,
            thousandsSeparator = true
        } = options;

        if (amount === null || amount === undefined || isNaN(amount)) {
            return '无数据';
        }

        let formattedAmount = Number(amount).toFixed(precision);
        
        if (thousandsSeparator) {
            formattedAmount = formattedAmount.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        return `${currency}${formattedAmount}`;
    }

    /**
     * 格式化百分比显示
     * @param {number} value - 数值（0-1之间）
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatPercentage(value, options = {}) {
        const { precision = 1, multiply100 = true } = options;

        if (value === null || value === undefined || isNaN(value)) {
            return '无数据';
        }

        const percentage = multiply100 ? value * 100 : value;
        return `${percentage.toFixed(precision)}%`;
    }

    /**
     * 格式化时间段显示
     * @param {number} minutes - 分钟数
     * @returns {string} 格式化后的字符串
     */
    static formatDuration(minutes) {
        if (minutes === null || minutes === undefined || isNaN(minutes)) {
            return '无数据';
        }

        const hours = Math.floor(minutes / 60);
        const remainingMinutes = minutes % 60;

        if (hours > 0) {
            return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`;
        } else {
            return `${remainingMinutes}分钟`;
        }
    }

    /**
     * 格式化文件大小显示
     * @param {number} bytes - 字节数
     * @returns {string} 格式化后的字符串
     */
    static formatFileSize(bytes) {
        if (bytes === null || bytes === undefined || isNaN(bytes)) {
            return '无数据';
        }

        const units = ['B', 'KB', 'MB', 'GB', 'TB'];
        let size = bytes;
        let unitIndex = 0;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return `${size.toFixed(2)} ${units[unitIndex]}`;
    }

    /**
     * 格式化评分显示
     * @param {number} score - 评分
     * @param {number} maxScore - 最高分
     * @returns {string} 格式化后的字符串
     */
    static formatScore(score, maxScore = 10) {
        if (score === null || score === undefined || isNaN(score)) {
            return '无评分';
        }

        return `${score.toFixed(1)}/${maxScore}`;
    }

    /**
     * 格式化状态显示
     * @param {string} status - 状态值
     * @param {Object} statusMap - 状态映射
     * @returns {string} 格式化后的字符串
     */
    static formatStatus(status, statusMap = {}) {
        const defaultStatusMap = {
            'active': '活跃',
            'inactive': '非活跃',
            'pending': '待处理',
            'completed': '已完成',
            'cancelled': '已取消',
            'error': '错误'
        };

        const map = { ...defaultStatusMap, ...statusMap };
        return map[status] || status || '未知状态';
    }

    /**
     * 格式化列表显示
     * @param {Array} items - 项目数组
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatList(items, options = {}) {
        const {
            separator = '、',
            maxItems = 3,
            moreText = '等'
        } = options;

        if (!Array.isArray(items) || items.length === 0) {
            return '无数据';
        }

        const displayItems = items.slice(0, maxItems);
        let result = displayItems.join(separator);

        if (items.length > maxItems) {
            result += `${separator}${moreText}`;
        }

        return result;
    }

    /**
     * 格式化范围显示
     * @param {number} min - 最小值
     * @param {number} max - 最大值
     * @param {Object} options - 格式化选项
     * @returns {string} 格式化后的字符串
     */
    static formatRange(min, max, options = {}) {
        const {
            unit = '',
            precision = 1,
            separator = ' - '
        } = options;

        if (min === null || min === undefined || max === null || max === undefined) {
            return '无数据';
        }

        const formattedMin = Number(min).toFixed(precision);
        const formattedMax = Number(max).toFixed(precision);

        return `${formattedMin}${unit}${separator}${formattedMax}${unit}`;
    }
}

// 导出类
window.FormatHelper = FormatHelper;
