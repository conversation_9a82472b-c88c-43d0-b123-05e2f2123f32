/**
 * 日期助手类
 * 提供统一的日期处理功能
 */
class DateHelper {
    /**
     * 格式化日期显示
     * @param {Date|string} date - 日期对象或字符串
     * @param {string} format - 格式类型
     * @returns {string} 格式化后的日期字符串
     */
    static formatDate(date, format = 'full') {
        if (!date) return '';
        
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        if (isNaN(dateObj.getTime())) return '';

        const options = {
            full: {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            },
            short: {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            },
            monthDay: {
                month: 'long',
                day: 'numeric'
            },
            yearMonth: {
                year: 'numeric',
                month: 'long'
            }
        };

        return dateObj.toLocaleDateString('zh-CN', options[format] || options.full);
    }

    /**
     * 计算两个日期之间的天数差
     * @param {Date|string} startDate - 开始日期
     * @param {Date|string} endDate - 结束日期
     * @returns {number} 天数差
     */
    static calculateDaysDifference(startDate, endDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
        const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
        
        const timeDiff = end.getTime() - start.getTime();
        return Math.floor(timeDiff / (1000 * 3600 * 24));
    }

    /**
     * 计算两个日期之间的月数差
     * @param {Date|string} startDate - 开始日期
     * @param {Date|string} endDate - 结束日期
     * @returns {number} 月数差
     */
    static calculateMonthsDifference(startDate, endDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
        const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
        
        const yearDiff = end.getFullYear() - start.getFullYear();
        const monthDiff = end.getMonth() - start.getMonth();
        
        return yearDiff * 12 + monthDiff;
    }

    /**
     * 计算年龄（以月为单位）
     * @param {Date|string} birthDate - 出生日期
     * @param {Date|string} currentDate - 当前日期（默认为今天）
     * @returns {number} 年龄（月数）
     */
    static calculateAgeInMonths(birthDate, currentDate = new Date()) {
        const birth = typeof birthDate === 'string' ? new Date(birthDate) : birthDate;
        const current = typeof currentDate === 'string' ? new Date(currentDate) : currentDate;
        
        const days = this.calculateDaysDifference(birth, current);
        return Math.floor(days / 30.44); // 平均每月天数
    }

    /**
     * 添加天数到日期
     * @param {Date|string} date - 原始日期
     * @param {number} days - 要添加的天数
     * @returns {Date} 新日期
     */
    static addDays(date, days) {
        const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
        dateObj.setDate(dateObj.getDate() + days);
        return dateObj;
    }

    /**
     * 添加周数到日期
     * @param {Date|string} date - 原始日期
     * @param {number} weeks - 要添加的周数
     * @returns {Date} 新日期
     */
    static addWeeks(date, weeks) {
        return this.addDays(date, weeks * 7);
    }

    /**
     * 添加月数到日期
     * @param {Date|string} date - 原始日期
     * @param {number} months - 要添加的月数
     * @returns {Date} 新日期
     */
    static addMonths(date, months) {
        const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
        dateObj.setMonth(dateObj.getMonth() + months);
        return dateObj;
    }

    /**
     * 获取日期范围内的所有日期
     * @param {Date|string} startDate - 开始日期
     * @param {Date|string} endDate - 结束日期
     * @returns {Array<Date>} 日期数组
     */
    static getDateRange(startDate, endDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : new Date(startDate);
        const end = typeof endDate === 'string' ? new Date(endDate) : new Date(endDate);
        const dates = [];
        
        const currentDate = new Date(start);
        while (currentDate <= end) {
            dates.push(new Date(currentDate));
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        return dates;
    }

    /**
     * 判断是否为同一天
     * @param {Date|string} date1 - 日期1
     * @param {Date|string} date2 - 日期2
     * @returns {boolean} 是否为同一天
     */
    static isSameDay(date1, date2) {
        const d1 = typeof date1 === 'string' ? new Date(date1) : date1;
        const d2 = typeof date2 === 'string' ? new Date(date2) : date2;
        
        return d1.getFullYear() === d2.getFullYear() &&
               d1.getMonth() === d2.getMonth() &&
               d1.getDate() === d2.getDate();
    }

    /**
     * 判断是否为今天
     * @param {Date|string} date - 日期
     * @returns {boolean} 是否为今天
     */
    static isToday(date) {
        return this.isSameDay(date, new Date());
    }

    /**
     * 判断是否为未来日期
     * @param {Date|string} date - 日期
     * @returns {boolean} 是否为未来日期
     */
    static isFuture(date) {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        dateObj.setHours(0, 0, 0, 0);
        
        return dateObj > today;
    }

    /**
     * 判断是否为过去日期
     * @param {Date|string} date - 日期
     * @returns {boolean} 是否为过去日期
     */
    static isPast(date) {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        dateObj.setHours(0, 0, 0, 0);
        
        return dateObj < today;
    }

    /**
     * 获取本周的开始和结束日期
     * @param {Date|string} date - 参考日期（默认为今天）
     * @returns {Object} 包含开始和结束日期的对象
     */
    static getWeekRange(date = new Date()) {
        const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
        const day = dateObj.getDay();
        const diff = dateObj.getDate() - day + (day === 0 ? -6 : 1); // 调整为周一开始
        
        const startOfWeek = new Date(dateObj.setDate(diff));
        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6);
        
        return {
            start: startOfWeek,
            end: endOfWeek
        };
    }

    /**
     * 获取本月的开始和结束日期
     * @param {Date|string} date - 参考日期（默认为今天）
     * @returns {Object} 包含开始和结束日期的对象
     */
    static getMonthRange(date = new Date()) {
        const dateObj = typeof date === 'string' ? new Date(date) : new Date(date);
        
        const startOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth(), 1);
        const endOfMonth = new Date(dateObj.getFullYear(), dateObj.getMonth() + 1, 0);
        
        return {
            start: startOfMonth,
            end: endOfMonth
        };
    }

    /**
     * 格式化时间段显示
     * @param {Date|string} startDate - 开始日期
     * @param {Date|string} endDate - 结束日期
     * @returns {string} 格式化的时间段字符串
     */
    static formatDateRange(startDate, endDate) {
        const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
        const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
        
        if (this.isSameDay(start, end)) {
            return this.formatDate(start);
        }
        
        if (start.getFullYear() === end.getFullYear()) {
            if (start.getMonth() === end.getMonth()) {
                return `${start.getFullYear()}年${start.getMonth() + 1}月${start.getDate()}日 - ${end.getDate()}日`;
            } else {
                return `${start.getFullYear()}年${start.getMonth() + 1}月${start.getDate()}日 - ${end.getMonth() + 1}月${end.getDate()}日`;
            }
        } else {
            return `${this.formatDate(start, 'short')} - ${this.formatDate(end, 'short')}`;
        }
    }
}

// 导出类
window.DateHelper = DateHelper;
