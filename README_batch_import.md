# 智能文章批量导入工具

## 功能特性

### 🆕 新增功能
1. **📁 递归目录遍历** - 自动遍历指定目录下的所有子目录，查找JSON文件
2. **🏷️ 智能分类创建** - 根据目录结构自动创建分类层次结构
3. **📂 结构化文件管理** - 导入后保持原有目录结构移动文件

### 🔧 核心功能
- 批量导入JSON格式的文章数据
- 自动获取或生成API认证token
- 详细的导入进度和结果统计
- 错误处理和文件分类管理

## 使用方法

### 1. 准备文章JSON文件

将需要导入的文章JSON文件放在`scraped_articles`目录下。每个JSON文件应包含以下格式的内容：

```json
{
    "url": "原文章URL",
    "scraped_at": "抓取时间，如2025-06-24 08:30:00",
    "title": "文章标题",
    "content_blocks": [
        {
            "type": "paragraph",
            "text": "段落文本内容"
        },
        {
            "type": "image",
            "src": "图片URL",
            "alt": "图片描述"
        },
        {
            "type": "heading",
            "level": 2,
            "text": "二级标题"
        },
        {
            "type": "list",
            "style": "unordered",
            "items": [
                "列表项1",
                "列表项2",
                "列表项3"
            ]
        },
        {
            "type": "subheading",
            "level": 3,
            "text": "三级标题"
        }
    ]
}
```

### 2. 运行导入脚本

```bash
python batch_import_articles.py
```

或者指定特定目录：

```bash
python batch_import_articles.py /path/to/your/articles
```

### 3. 设置API参数（可选）

可以通过环境变量设置API参数：

```bash
export API_URL="http://your-cms-url/api/articles/import"
export API_TOKEN="your_api_token"
python batch_import_articles.py
```

### 4. 查看导入结果

脚本执行后，会在控制台显示导入结果统计，并将处理过的文件分类到以下目录：

- `scraped_articles/imported`: 成功导入的文件
- `scraped_articles/failed`: 导入失败的文件

## 故障排除

1. **API连接失败**
   - 检查API URL是否正确
   - 确认CMS系统是否正常运行

2. **认证错误**
   - 确认API令牌是否有效
   - 检查用户权限

3. **JSON格式错误**
   - 检查JSON文件格式是否符合要求
   - 确保内容块格式正确

4. **文章内容为空**
   - 确保content_blocks数组不为空
   - 检查是否包含必要的内容块

## 注意事项

- 文件名不影响导入结果，但建议使用有意义的名称以便管理
- 导入过程中会自动生成文章别名(slug)，用于URL
- 如果导入的文章与已有文章标题相似，系统会提示但不会重复导入
- 图片URL需要是可访问的公网地址 