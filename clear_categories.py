#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清空数据库中的分类数据
保留表结构，只删除数据
"""

import sqlite3
import os
import sys

def clear_categories_data():
    """删除数据库中的所有分类数据"""
    
    # 数据库路径
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🗑️ 开始清空分类数据...")
        
        # 获取删除前的统计信息
        cursor.execute("SELECT COUNT(*) FROM primary_category")
        primary_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM secondary_category")
        secondary_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM category")
        category_count = cursor.fetchone()[0]
        
        # 检查有多少文章关联到分类
        cursor.execute("SELECT COUNT(*) FROM article WHERE category_id IS NOT NULL")
        articles_with_category = cursor.fetchone()[0]
        
        print(f"📊 删除前统计:")
        print(f"   一级分类数量: {primary_count}")
        print(f"   二级分类数量: {secondary_count}")
        print(f"   三级分类数量: {category_count}")
        print(f"   有分类的文章: {articles_with_category}")
        
        if primary_count == 0 and secondary_count == 0 and category_count == 0:
            print("✅ 数据库中没有分类数据")
            return True
        
        # 确认删除
        total_categories = primary_count + secondary_count + category_count
        confirm = input(f"\n⚠️ 确认要删除 {total_categories} 个分类及相关数据吗？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return False
        
        print("\n🗑️ 正在删除分类数据...")
        
        # 删除顺序很重要：从依赖关系的末端开始删除
        
        # 1. 先将文章的分类关联设为NULL
        if articles_with_category > 0:
            cursor.execute("UPDATE article SET category_id = NULL")
            print(f"   ✅ 清除了 {articles_with_category} 篇文章的分类关联")
        
        # 2. 删除三级分类（category表）
        if category_count > 0:
            cursor.execute("DELETE FROM category")
            print(f"   ✅ 删除了 {category_count} 个三级分类")
        
        # 3. 删除二级分类（secondary_category表）
        if secondary_count > 0:
            cursor.execute("DELETE FROM secondary_category")
            print(f"   ✅ 删除了 {secondary_count} 个二级分类")
        
        # 4. 删除一级分类（primary_category表）
        if primary_count > 0:
            cursor.execute("DELETE FROM primary_category")
            print(f"   ✅ 删除了 {primary_count} 个一级分类")
        
        # 重置自增ID（可选）
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sqlite_sequence'")
            if cursor.fetchone():
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='primary_category'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='secondary_category'")
                cursor.execute("DELETE FROM sqlite_sequence WHERE name='category'")
                print("   ✅ 重置了自增ID")
            else:
                print("   ℹ️ 跳过自增ID重置（表不存在）")
        except Exception as e:
            print(f"   ⚠️ 重置自增ID时出错: {str(e)}")
            print("   ℹ️ 这不影响数据删除结果")
        
        # 提交更改
        conn.commit()
        
        # 验证删除结果
        cursor.execute("SELECT COUNT(*) FROM primary_category")
        remaining_primary = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM secondary_category")
        remaining_secondary = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM category")
        remaining_category = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM article WHERE category_id IS NOT NULL")
        remaining_articles_with_category = cursor.fetchone()[0]
        
        print(f"\n📊 删除后统计:")
        print(f"   一级分类数量: {remaining_primary}")
        print(f"   二级分类数量: {remaining_secondary}")
        print(f"   三级分类数量: {remaining_category}")
        print(f"   有分类的文章: {remaining_articles_with_category}")
        
        if (remaining_primary == 0 and remaining_secondary == 0 and 
            remaining_category == 0 and remaining_articles_with_category == 0):
            print("\n✅ 所有分类数据已成功清空！")
            print("📋 表结构保持完整，可以重新创建分类")
            return True
        else:
            print("\n⚠️ 删除可能不完整，请检查")
            return False
            
    except Exception as e:
        print(f"❌ 删除数据时出错: {str(e)}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

def show_category_info():
    """显示分类信息"""
    db_path = os.path.join(os.path.dirname(__file__), 'instance', 'articles.db')
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📋 分类数据统计信息:")
        
        # 统计各级分类数量
        cursor.execute("SELECT COUNT(*) FROM primary_category")
        primary_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM secondary_category")
        secondary_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM category")
        category_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM article WHERE category_id IS NOT NULL")
        articles_with_category = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM article WHERE category_id IS NULL")
        articles_without_category = cursor.fetchone()[0]
        
        print(f"   📊 一级分类: {primary_count} 个")
        print(f"   📊 二级分类: {secondary_count} 个")
        print(f"   📊 三级分类: {category_count} 个")
        print(f"   📄 有分类的文章: {articles_with_category} 篇")
        print(f"   📄 无分类的文章: {articles_without_category} 篇")
        
        # 显示分类层次结构概览
        if primary_count > 0:
            print(f"\n🌳 分类层次结构概览:")
            cursor.execute("""
                SELECT 
                    pc.name as primary_name,
                    COUNT(DISTINCT sc.id) as secondary_count,
                    COUNT(DISTINCT c.id) as category_count,
                    COUNT(a.id) as article_count
                FROM primary_category pc
                LEFT JOIN secondary_category sc ON pc.id = sc.primary_category_id
                LEFT JOIN category c ON sc.id = c.secondary_category_id
                LEFT JOIN article a ON c.id = a.category_id
                GROUP BY pc.id, pc.name
                ORDER BY pc.id
            """)
            
            for row in cursor.fetchall():
                primary_name, sec_count, cat_count, art_count = row
                print(f"   📁 {primary_name}: {sec_count}个二级分类, {cat_count}个三级分类, {art_count}篇文章")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询分类信息时出错: {str(e)}")

if __name__ == "__main__":
    print("🗑️ 分类数据清理工具")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--info":
        show_category_info()
    else:
        clear_categories_data()
        print("\n" + "=" * 50)
        print("💡 提示:")
        print("   - 表结构已保留，可以重新创建分类")
        print("   - 文章的分类关联已清除，但文章本身保留")
        print("   - 使用 --info 参数查看当前分类统计")
        print("   - 运行 python clear_categories.py --info")
