from app import create_app
from models import Article, db

app = create_app()
with app.app_context():
    articles = Article.query.all()
    print(f"Found {len(list(articles))} articles in the database")
    
    if articles:
        for article in articles:
            print(f"Article ID: {article.id}")
            print(f"Title: {article.title}")
            print(f"Slug: {article.slug}")
            print(f"Published: {article.published}")
            print(f"Content blocks: {article.content_blocks}")
            print("-------------------")
    else:
        print("No articles found in the database") 