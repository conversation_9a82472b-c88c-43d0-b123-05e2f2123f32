"""Add new features and update models

Revision ID: 2c7c216d12d8
Revises: 
Create Date: 2025-06-23 11:45:48.573509

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '2c7c216d12d8'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_categories_name'))
        batch_op.drop_index(batch_op.f('ix_categories_slug'))

    op.drop_table('categories')
    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_tags_name'))

    op.drop_table('tags')
    with op.batch_alter_table('comments', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_comments_approved'))
        batch_op.drop_index(batch_op.f('ix_comments_created_at'))

    op.drop_table('comments')
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_users_email'))
        batch_op.drop_index(batch_op.f('ix_users_username'))

    op.drop_table('users')
    with op.batch_alter_table('articles', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_articles_created_at'))
        batch_op.drop_index(batch_op.f('ix_articles_featured'))
        batch_op.drop_index(batch_op.f('ix_articles_published'))
        batch_op.drop_index(batch_op.f('ix_articles_published_at'))
        batch_op.drop_index(batch_op.f('ix_articles_slug'))
        batch_op.drop_index(batch_op.f('ix_articles_title'))
        batch_op.drop_index(batch_op.f('ix_articles_updated_at'))

    op.drop_table('articles')
    with op.batch_alter_table('article_bookmarks', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'article', ['article_id'], ['id'])

    with op.batch_alter_table('article_likes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])
        batch_op.create_foreign_key(None, 'article', ['article_id'], ['id'])

    with op.batch_alter_table('article_tags', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'article', ['article_id'], ['id'])
        batch_op.create_foreign_key(None, 'tag', ['tag_id'], ['id'])

    with op.batch_alter_table('media', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])

    with op.batch_alter_table('tool_usage', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'user', ['user_id'], ['id'])

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('tool_usage', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('media', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('article_tags', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'articles', ['article_id'], ['id'])
        batch_op.create_foreign_key(None, 'tags', ['tag_id'], ['id'])

    with op.batch_alter_table('article_likes', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'articles', ['article_id'], ['id'])
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    with op.batch_alter_table('article_bookmarks', schema=None) as batch_op:
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.drop_constraint(None, type_='foreignkey')
        batch_op.create_foreign_key(None, 'articles', ['article_id'], ['id'])
        batch_op.create_foreign_key(None, 'users', ['user_id'], ['id'])

    op.create_table('articles',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('title', sa.VARCHAR(length=255), nullable=False),
    sa.Column('slug', sa.VARCHAR(length=255), nullable=True),
    sa.Column('content_blocks', sa.TEXT(), nullable=True),
    sa.Column('summary', sa.TEXT(), nullable=True),
    sa.Column('cover_image', sa.VARCHAR(length=255), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.Column('published', sa.BOOLEAN(), nullable=True),
    sa.Column('published_at', sa.DATETIME(), nullable=True),
    sa.Column('featured', sa.BOOLEAN(), nullable=True),
    sa.Column('views', sa.INTEGER(), nullable=True),
    sa.Column('reviewer', sa.VARCHAR(length=64), nullable=True),
    sa.Column('source_url', sa.VARCHAR(length=255), nullable=True),
    sa.Column('meta_title', sa.VARCHAR(length=255), nullable=True),
    sa.Column('meta_description', sa.VARCHAR(length=255), nullable=True),
    sa.Column('user_id', sa.INTEGER(), nullable=True),
    sa.Column('category_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['category_id'], ['categories.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('articles', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_articles_updated_at'), ['updated_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_articles_title'), ['title'], unique=False)
        batch_op.create_index(batch_op.f('ix_articles_slug'), ['slug'], unique=1)
        batch_op.create_index(batch_op.f('ix_articles_published_at'), ['published_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_articles_published'), ['published'], unique=False)
        batch_op.create_index(batch_op.f('ix_articles_featured'), ['featured'], unique=False)
        batch_op.create_index(batch_op.f('ix_articles_created_at'), ['created_at'], unique=False)

    op.create_table('users',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('username', sa.VARCHAR(length=64), nullable=False),
    sa.Column('email', sa.VARCHAR(length=120), nullable=False),
    sa.Column('password_hash', sa.VARCHAR(length=128), nullable=True),
    sa.Column('is_admin', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('users', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_users_username'), ['username'], unique=1)
        batch_op.create_index(batch_op.f('ix_users_email'), ['email'], unique=1)

    op.create_table('comments',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('content', sa.TEXT(), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('approved', sa.BOOLEAN(), nullable=True),
    sa.Column('article_id', sa.INTEGER(), nullable=True),
    sa.Column('user_id', sa.INTEGER(), nullable=True),
    sa.ForeignKeyConstraint(['article_id'], ['articles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('comments', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_comments_created_at'), ['created_at'], unique=False)
        batch_op.create_index(batch_op.f('ix_comments_approved'), ['approved'], unique=False)

    op.create_table('tags',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=64), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('tags', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_tags_name'), ['name'], unique=1)

    op.create_table('categories',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('name', sa.VARCHAR(length=64), nullable=True),
    sa.Column('slug', sa.VARCHAR(length=64), nullable=True),
    sa.Column('description', sa.TEXT(), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('categories', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_categories_slug'), ['slug'], unique=1)
        batch_op.create_index(batch_op.f('ix_categories_name'), ['name'], unique=1)

    # ### end Alembic commands ###
