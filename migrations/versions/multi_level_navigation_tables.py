"""multi_level_navigation_tables

Revision ID: multi_level_navigation
Revises: 2c7c216d12d8
Create Date: 2023-12-04 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'multi_level_navigation'
down_revision = '2c7c216d12d8'
branch_labels = None
depends_on = None


def upgrade():
    # 创建一级分类表
    op.create_table('primary_category',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=64), nullable=False),
        sa.Column('slug', sa.String(length=64), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('icon', sa.String(length=128), nullable=True),
        sa.Column('image', sa.String(length=255), nullable=True),
        sa.Column('order', sa.Integer(), nullable=True),
        sa.Column('is_visible', sa.<PERSON>(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('slug')
    )
    
    # 创建二级分类表
    op.create_table('secondary_category',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('primary_category_id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(length=64), nullable=False),
        sa.Column('slug', sa.String(length=64), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('icon', sa.String(length=128), nullable=True),
        sa.Column('image', sa.String(length=255), nullable=True),
        sa.Column('order', sa.Integer(), nullable=True),
        sa.Column('is_visible', sa.Boolean(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['primary_category_id'], ['primary_category.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_secondary_category_primary_category_id'), 'secondary_category', ['primary_category_id'], unique=False)
    op.create_index(op.f('ix_secondary_category_slug'), 'secondary_category', ['slug'], unique=False)
    
    # 修改现有的category表，添加二级分类外键
    op.add_column('category', sa.Column('secondary_category_id', sa.Integer(), nullable=True))
    op.create_foreign_key('fk_category_secondary_category', 'category', 'secondary_category', ['secondary_category_id'], ['id'])
    op.create_index(op.f('ix_category_secondary_category_id'), 'category', ['secondary_category_id'], unique=False)


def downgrade():
    # 删除索引和外键约束
    op.drop_index(op.f('ix_category_secondary_category_id'), table_name='category')
    op.drop_constraint('fk_category_secondary_category', 'category', type_='foreignkey')
    op.drop_column('category', 'secondary_category_id')
    
    op.drop_index(op.f('ix_secondary_category_slug'), table_name='secondary_category')
    op.drop_index(op.f('ix_secondary_category_primary_category_id'), table_name='secondary_category')
    op.drop_table('secondary_category')
    op.drop_table('primary_category') 