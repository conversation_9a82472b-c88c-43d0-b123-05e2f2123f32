"""add article_count to tag table

Revision ID: add_article_count
Revises: multi_level_navigation
Create Date: 2023-12-04 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision = 'add_article_count'
down_revision = 'multi_level_navigation'
branch_labels = None
depends_on = None


def upgrade():
    # 添加article_count字段到tag表
    op.add_column('tag', sa.Column('article_count', sa.Integer(), nullable=True, server_default='0'))
    
    # 更新现有标签的文章数量
    conn = op.get_bind()
    conn.execute(text("""
        UPDATE tag
        SET article_count = (
            SELECT COUNT(*)
            FROM article_tags
            WHERE article_tags.tag_id = tag.id
        )
    """))


def downgrade():
    # 删除article_count字段
    op.drop_column('tag', 'article_count') 