#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import json
import sys
import os
import glob
import time
from pathlib import Path

def get_api_token():
    """获取API token"""
    # 检查是否有已存在的管理员用户
    import sqlite3
    try:
        conn = sqlite3.connect('instance/articles.db')
        cursor = conn.cursor()
        cursor.execute("SELECT id, username FROM user WHERE is_admin = 1 LIMIT 1")
        admin = cursor.fetchone()
        
        if admin:
            admin_id, admin_username = admin
            print(f"找到管理员用户: {admin_username} (ID: {admin_id})")
            
            # 检查是否已有API token
            cursor.execute("SELECT api_token FROM user WHERE id = ?", (admin_id,))
            token = cursor.fetchone()
            
            if token and token[0]:
                return token[0]
            
            # 生成新token
            import uuid
            new_token = str(uuid.uuid4())
            cursor.execute("UPDATE user SET api_token = ? WHERE id = ?", (new_token, admin_id))
            conn.commit()
            print(f"已为用户 {admin_username} 生成新的API token")
            return new_token
    except Exception as e:
        print(f"获取API token时出错: {str(e)}")
    finally:
        if 'conn' in locals():
            conn.close()
    
    return None

def import_article(api_url, api_token, article_data):
    """导入单篇文章到API"""
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_token}"
    }
    
    try:
        response = requests.post(api_url, json=article_data, headers=headers)
        
        if response.status_code == 200 or response.status_code == 201:
            result = response.json()
            if result.get("status") == "success":
                print(f"✅ 成功导入: {article_data.get('title')} (ID: {result.get('article_id')}, 别名: {result.get('article_slug')})")
                return True, result
            elif result.get("status") == "warning" and "已存在相似文章" in result.get("message", ""):
                print(f"⚠️ 文章已存在: {article_data.get('title')} (ID: {result.get('article_id')})")
                return False, result
            else:
                print(f"❌ 导入失败: {article_data.get('title')} - {result.get('message', '未知错误')}")
                return False, result
        else:
            print(f"❌ 请求错误 ({response.status_code}): {article_data.get('title')}")
            try:
                print(f"   错误详情: {response.json()}")
            except:
                print(f"   响应内容: {response.text[:100]}...")
            return False, None
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False, None

def batch_import_articles(api_url, api_token, directory):
    """批量导入指定目录下的所有JSON文件"""
    # 确保目录存在
    if not os.path.exists(directory):
        print(f"❌ 目录不存在: {directory}")
        return False
    
    # 获取所有JSON文件
    json_files = glob.glob(os.path.join(directory, "*.json"))
    if not json_files:
        print(f"❌ 没有找到JSON文件: {directory}")
        return False
    
    print(f"找到 {len(json_files)} 个JSON文件")
    
    # 创建导入结果目录
    imported_dir = os.path.join(directory, "imported")
    failed_dir = os.path.join(directory, "failed")
    os.makedirs(imported_dir, exist_ok=True)
    os.makedirs(failed_dir, exist_ok=True)
    
    # 导入统计
    stats = {
        "total": len(json_files),
        "success": 0,
        "exist": 0,
        "failed": 0
    }
    
    # 遍历所有JSON文件
    for i, json_file in enumerate(json_files):
        file_name = os.path.basename(json_file)
        print(f"\n[{i+1}/{stats['total']}] 处理: {file_name}")
        
        try:
            # 读取JSON文件
            with open(json_file, 'r', encoding='utf-8') as f:
                article_data = json.load(f)
            
            # 导入文章
            success, result = import_article(api_url, api_token, article_data)
            
            # 移动文件到相应目录
            if success:
                stats["success"] += 1
                target_path = os.path.join(imported_dir, file_name)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            elif result and result.get("status") == "warning":
                stats["exist"] += 1
                target_path = os.path.join(imported_dir, file_name)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            else:
                stats["failed"] += 1
                target_path = os.path.join(failed_dir, file_name)
                os.rename(json_file, target_path)
                print(f"   文件已移动到: {target_path}")
            
            # 避免请求过于频繁
            time.sleep(0.5)
            
        except json.JSONDecodeError:
            print(f"❌ JSON解析错误: {file_name}")
            stats["failed"] += 1
            target_path = os.path.join(failed_dir, file_name)
            os.rename(json_file, target_path)
        except Exception as e:
            print(f"❌ 处理文件时出错: {str(e)}")
            stats["failed"] += 1
            try:
                target_path = os.path.join(failed_dir, file_name)
                os.rename(json_file, target_path)
            except:
                pass
    
    # 打印统计信息
    print("\n" + "="*50)
    print(f"导入完成! 总计: {stats['total']} 篇文章")
    print(f"✅ 成功导入: {stats['success']} 篇")
    print(f"⚠️ 已存在: {stats['exist']} 篇")
    print(f"❌ 导入失败: {stats['failed']} 篇")
    print("="*50)
    
    return stats["success"] > 0

if __name__ == "__main__":
    # 从环境变量或命令行参数获取API URL和token
    api_url = os.environ.get("API_URL") or "http://127.0.0.1:5000/api/articles/import"
    api_token = os.environ.get("API_TOKEN") or get_api_token() or "your_api_token_here"
    
    # 获取要导入的目录
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        directory = os.path.join(os.getcwd(), "scraped_articles")
    
    print(f"API URL: {api_url}")
    print(f"导入目录: {directory}")
    
    # 执行批量导入
    batch_import_articles(api_url, api_token, directory) 